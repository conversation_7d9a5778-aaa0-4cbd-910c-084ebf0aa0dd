<template>
  <el-dialog v-model="dialogVisible" title="历史数据" style="width: 1500px;">
    <el-card>
      <!--设备列表 头-->
      <template #header>
        <div class="card-header">
          <el-form ref="queryForm" :inline="true" label-width="68px">
            <el-form-item label="时间" prop="alias">
              <el-date-picker
                  v-model="times"
                  type="datetimerange"
                  range-separator="-"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" :icon="Search" @click="seach">搜索</el-button>
              <el-button type="success" :icon="Plus" @click="exportExecl"> 导出</el-button>
            </el-form-item>

          </el-form>
          <div>

          </div>
        </div>
      </template>

      <el-table :data="tableData" border style="width: 100%">
        <el-table-column v-for="(item, index) in tableHeader" :key="index" :label="item" :prop="item">
        </el-table-column>
      </el-table>
      <pagination v-show="queryParams.total>0" :total="queryParams.total" v-model:page="queryParams.pageNum"
                  v-model:limit="queryParams.pageSize" @pagination="seach"/>
    </el-card>

  </el-dialog>
</template>


<script setup>
import {getHistory, exportHistory} from '@/api/datacenter/datacenter'
import {Plus, Search} from "@element-plus/icons-vue";
import dayjs from 'dayjs';
import * as XLSX from 'xlsx';

const {proxy} = getCurrentInstance()
const dialogVisible = ref(false);
const times = ref([]);
const baseItem = ref([]);
const tableHeader = ref([])
const tableData = ref([])
const queryParams = ref({bizPointId: '', total: 0, pageNum: 1, pageSize: 10, startTime: '', endTime: ''})

function historyTable(item) {
  tableData.value = [];
  tableHeader.value = [];
  baseItem.value = item;
  times.value = [dayjs(new Date()).format('YYYY-MM-DD') + " 00:00:00", dayjs(new Date(Date.parse(new Date()) + 86400000)).format('YYYY-MM-DD') + " 00:00:00"]
  queryParams.value = {bizPointId: baseItem.value.id, total: 0, pageNum: 1, pageSize: 10, startTime: '', endTime: ''}
  dialogVisible.value = true;
  seach();
}

function seach() {
  if (times.value == null || times.value.length == 0) {
    proxy.$modal.msgError("请先选择查询时间");
    return;
  }
  queryParams.value.startTime = times.value[0]
  queryParams.value.endTime = times.value[1]
  getHistory(queryParams.value).then(res => {
    tableData.value = [];
    tableHeader.value = [];
    tableHeader.value.push("时间")
    res.tableHeader.forEach(item => tableHeader.value.push(item));
    tableData.value = res.list;
    queryParams.value.total = Number(res.total);
  })
}

function exportExecl() {
  queryParams.value.startTime = times.value[0]
  queryParams.value.endTime = times.value[1]
  exportHistory(queryParams.value).then(res => {
    const ws = XLSX.utils.aoa_to_sheet(res.list);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, '历史数据');
    XLSX.writeFile(wb, '历史数据.xlsx');
  })
}


defineExpose({historyTable})

</script>

<style>

</style>