<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="88px">
         <el-form-item label="报警内容" prop="alarmContent">
            <el-input
               v-model="queryParams.alarmContent"
               placeholder="请输入报警内容"
               clearable
               style="width: 240px"
               @keyup.enter="handleQuery"
            />
         </el-form-item>
         <el-form-item label="机器人名称" prop="robotName">
            <el-input
               v-model="queryParams.robotName"
               placeholder="请输入机器人名称"
               clearable
               style="width: 240px"
               @keyup.enter="handleQuery"
            />
         </el-form-item>
         <el-form-item label="报警类型" prop="alarmType">
            <el-select v-model="queryParams.alarmType" placeholder="请选择报警类型" clearable style="width: 240px">
               <el-option
                  v-for="dict in escort_alarm_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
               />
            </el-select>
         </el-form-item>
         <el-form-item label="记录时间" style="width: 438px;">
            <el-date-picker
               v-model="dateRange"
               value-format="YYYY-MM-DD HH:mm:ss"
               type="datetimerange"
               range-separator="至"
               start-placeholder="开始日期"
               end-placeholder="结束日期"
            ></el-date-picker>
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
         </el-form-item>
      </el-form>


      <el-table v-loading="loading" :data="dataList">
         <el-table-column label="记录时间" align="center" prop="createTime" width="180">
            <template #default="scope">
               <span>{{ parseTime(scope.row.createTime) }}</span>
            </template>
         </el-table-column>
         <el-table-column label="任务名称" align="center" prop="taskName" />
         <el-table-column label="机器人名称" align="center" prop="robotName" />
         <el-table-column label="报警类型" align="center" prop="alarmType">
            <template #default="scope">
               <dict-tag :options="escort_alarm_type" :value="scope.row.alarmType" />
            </template>
         </el-table-column>
         <el-table-column label="报警内容" align="center" prop="alarmContent" :show-overflow-tooltip="true" />
         <el-table-column label="操作" align="center" width="150" class-name="small-padding fixed-width">
            <template #default="scope">
               <el-button size="mini" type="text" icon="Search" @click="handleDetail(scope.row)" >查看详情</el-button>
            </template>
         </el-table-column>

      </el-table>

      <pagination
         v-show="total > 0"
         :total="total"
         v-model:page="queryParams.pageNum"
         v-model:limit="queryParams.pageSize"
         @pagination="getList"
      />

    <el-dialog title="详情" v-model="open" width="600px" append-to-body>
      <el-descriptions class="margin-top" title="" :column="2" border>
          <el-descriptions-item label="任务名称">{{alarmInfo.taskName}}</el-descriptions-item>
          <el-descriptions-item label="机器人名称">{{alarmInfo.robotName}}</el-descriptions-item>

          <el-descriptions-item label="等待点位">{{alarmInfo.waitPointName}}</el-descriptions-item>
          <el-descriptions-item label="到达点位">{{alarmInfo.arrivePointName}}</el-descriptions-item>

          <el-descriptions-item label="执行时间">{{parseTime(alarmInfo.execTime)}}</el-descriptions-item>
          <el-descriptions-item label="等待超时时间（分）">{{alarmInfo.timeoutMin}}</el-descriptions-item>

          <el-descriptions-item label="UWB卡"></el-descriptions-item>
          <el-descriptions-item label="主办负责人">{{alarmInfo.superintendentName}}</el-descriptions-item>


          <el-descriptions-item label="记录时间">{{parseTime(alarmInfo.createTime)}}</el-descriptions-item>
          <el-descriptions-item label="报警类型"><dict-tag :options="escort_alarm_type" :value="alarmInfo.alarmType"/></el-descriptions-item>
          

          <el-descriptions-item label="报警内容">{{alarmInfo.alarmContent}}</el-descriptions-item>

      </el-descriptions>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">关 闭</el-button>
        </div>
      </template>
    </el-dialog>


   </div>
</template>

<script setup name="AlarmEscort">


import { listPageList } from "@/api/alarm/alarmEscort"


const { proxy } = getCurrentInstance()
const { escort_alarm_type} = proxy.useDict("escort_alarm_type")

const dataList = ref([])
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const dateRange = ref([])

const open = ref(false)
const alarmInfo = ref({})
const url= import.meta.env.VITE_APP_BASE_API;

const imageUrl = ref(null)
const imageUrlList = ref([])

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    alarmContent: undefined,
    robotName: undefined,
    alarmType: undefined
  }
})

const { queryParams} = toRefs(data)

function handleDetail(row){

    open.value = true;
    alarmInfo.value = row;
}

function cancel(){
    open.value = false;
    alarmInfo.value = {};
}



/** 查询陪同报警列表 */
function getList() {
  loading.value = true
  if(dateRange.value.length > 0){
    queryParams.value.beginTime = dateRange.value[0]
    queryParams.value.endTime = dateRange.value[1]
  }
  listPageList(queryParams.value).then(response => {
    dataList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}


/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = []
   queryParams.value.beginTime = null;
  queryParams.value.endTime = null;
  proxy.resetForm("queryRef")
  handleQuery()
}




getList()
</script>
