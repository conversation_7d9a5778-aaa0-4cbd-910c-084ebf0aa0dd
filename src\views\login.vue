<template>
	<div class="app">
		<img src="../assets/images/login/01.png" class="logo_title"/>
		<div class="copyright">
			<span class="copyright_title">Copyright © SKY 2025 All Rights Reserved</span>
			<span>中科仪智慧SUBFAB系统</span>
		</div>		
		<img src="../assets/images/login/03.jpg" class="right_img"/>
		<div class="login">
			<img src="../assets/images/login/04.png" alt="" />
			<label class="welcome">欢迎登录</label>
			<label class="input_user_icon">
				<input id="user" v-model="loginForm.username" type="text" placeholder="用户名" autocomplete="off" class="login_txtbx">
				<span id="user_icon" class="user_icon"></span>				
			</label>
			<label class="input_pwd_icon">				
				<input id="pwd" v-model="loginForm.password" type="password" placeholder="密码" autocomplete="off" @keyup.enter="handleLogin" class="login_txtbx">
				<span id="pwd_icon" class="pwd_icon"></span>
			</label>
			<div class="signup">
				<a class="gv" href="#" @click.prevent="handleLogin">登&nbsp;&nbsp;录</a>
			</div>			
		</div>
	</div>
</template>

<script setup>
	import {
		getCodeImg
	} from "@/api/login"
	import Cookies from "js-cookie"
	import {
		encrypt,
		decrypt
	} from "@/utils/jsencrypt"
	import useUserStore from '@/store/modules/user'
	import {
		onMounted
	} from "vue"

	const title = import.meta.env.VITE_APP_TITLE
	const userStore = useUserStore()
	const route = useRoute()
	const router = useRouter()
	const {
		proxy
	} = getCurrentInstance()

	const loginForm = ref({
		username: "",
		password: "",
		rememberMe: false,
		code: "",
		uuid: ""
	})

	const loginRules = {
		username: [{
			required: true,
			trigger: "blur",
			message: "请输入您的账号"
		}],
		password: [{
			required: true,
			trigger: "blur",
			message: "请输入您的密码"
		}],
		code: [{
			required: true,
			trigger: "change",
			message: "请输入验证码"
		}]
	}

	const codeUrl = ref("")
	const loading = ref(false)
	// 验证码开关
	const captchaEnabled = ref(false)
	// 注册开关
	const register = ref(false)
	const redirect = ref(undefined)

	watch(route, (newRoute) => {
		redirect.value = newRoute.query && newRoute.query.redirect
	}, {
		immediate: true
	})

	function handleLogin() {
		if (!loginForm.value.username || !loginForm.value.password) {
			return proxy.$modal.msgError("账号密码不能为空")
		}
		loading.value = true
		// 勾选了需要记住密码设置在 cookie 中设置记住用户名和密码
		if (loginForm.value.rememberMe) {
			Cookies.set("username", loginForm.value.username, {
				expires: 30
			})
			Cookies.set("password", encrypt(loginForm.value.password), {
				expires: 30
			})
			Cookies.set("rememberMe", loginForm.value.rememberMe, {
				expires: 30
			})
		} else {
			// 否则移除
			Cookies.remove("username")
			Cookies.remove("password")
			Cookies.remove("rememberMe")
		}
		// 调用action的登录方法
		userStore.login(loginForm.value).then(() => {
			const query = route.query
			const otherQueryParams = Object.keys(query).reduce((acc, cur) => {
				if (cur !== "redirect") {
					acc[cur] = query[cur]
				}
				return acc
			}, {})
			router.push({
				path: redirect.value || "/",
				query: otherQueryParams
			})
		}).catch(() => {
			loading.value = false
			// 重新获取验证码
			if (captchaEnabled.value) {
				getCode()
			}
		})
	}

	function getCode() {
		getCodeImg().then(res => {
			captchaEnabled.value = res.captchaEnabled === undefined ? true : res.captchaEnabled
			if (captchaEnabled.value) {
				codeUrl.value = "data:image/gif;base64," + res.img
				loginForm.value.uuid = res.uuid
			}
		})
	}

	function getCookie() {
		const username = Cookies.get("username")
		const password = Cookies.get("password")
		const rememberMe = Cookies.get("rememberMe")
		loginForm.value = {
			username: username === undefined ? loginForm.value.username : username,
			password: password === undefined ? loginForm.value.password : decrypt(password),
			rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)
		}
	}

	getCode()
	getCookie()

	onMounted(() => {
		
	})
</script>

<style lang='scss' scoped>
	.app {		
		height: 100%;
		width: 100%;
		background: url('../assets/images/login/02.jpg') no-repeat;
		background-size: 960px 100%;
	}
	
	.logo_title {
		position: absolute;
		top: 114px;
		left: 78px;
	}
	
	.copyright {
		position: absolute;
		bottom: 46px;
		left: 78px;		
		color: #fff;
		font-size: 14px;		
		display: grid;
	}
	
	.copyright_title {		
		font-family: Verdana;
		margin-bottom: 5px;
	}
	
	.right_img {
		float: right;
	}
	
	.signup a.gv {		
		background: url(../assets/images/login/05.png) repeat 0px 0px;
		width: 200px;
		display: inline-block;
		text-align: center;
		line-height: 56px;		
		color: #fff;
		font-size: 16px;
	}
	
	.login {
		
		display: grid;
		float: right;
		margin-top: 180px;
	}
	
	.welcome {
		font-size: 32px;
		margin: 20px 0 20px 0;
		color: #444444;
	}
		
	.login .login_txtbx {
		font-size: 14px;
		height: 56px;
		line-height: 26px;
		padding: 8px 9%;
		width: 420px;
		text-indent: 1em;
		border: 1px solid #ffff;
		background: rgba(121, 121, 121, 0.1);
		color: #444444;
		margin-bottom: 24px;
	}
	
	.login_txtbx::-webkit-input-placeholder {
		color: #444444;
	}
	
	.login_txtbx:-moz-placeholder {
		color: #444444;
	}
	
	.login_txtbx::-moz-placeholder {
		color: #444444;
	}
	
	.login_txtbx:-ms-input-placeholder {
		color: #444444;
	}
	
	.login_txtbx:focus {
		-webkit-box-shadow: 0 0 6px #0B7DFF;		
		outline: 1px solid #0B7DFF;
		background: #ffff;
	}
	
	.input_user_icon {
	    position: relative;
	}
	 
	.input_user_icon input {
	    padding-left: 30px;
	}
	 
	.input_user_icon .user_icon {
	    position: absolute;
	    left: 20px;
	    top: 36%;
	    transform: translateY(-50%);
	    background-image: url('../assets/images/login/t02.png');
	    background-size: contain;
	    background-repeat: no-repeat;
	    width: 14px;
	    height: 14px;
	}
	
	.login_txtbx:focus + .user_icon {
		background-image: url('../assets/images/login/t01.png');
	}
	
	.input_pwd_icon {
	    position: relative;
	}
	 
	.input_pwd_icon input {
	    padding-left: 30px;
	}
	
	.input_pwd_icon .pwd_icon {
	    position: absolute;
	    left: 20px;
	    top: 36%;
	    transform: translateY(-50%);
	    background-image: url('../assets/images/login/t04.png');
	    background-size: contain;
	    background-repeat: no-repeat;
	    width: 14px;
	    height: 14px;
	}
	
	.login_txtbx:focus + .pwd_icon {
		background-image: url('../assets/images/login/t03.png');
	}	
	
</style>