<template>
   <div class="app-container">
    <el-row :gutter="40">
        <el-col :span="20">
            地图
        </el-col>

           
        <el-col :span="4">
            <div class="demo-collapse">
                <el-collapse v-model="activeName" accordion expand-icon-position="left" v-for="robot in showRobotList" :key="robot.id" @change="handleChange()">
                    <el-collapse-item :title="robot.robotName" :name="robot.id" icon="Plus" :style="getItemStyle(robot)">
                        <div>
                            <div class="robot-status-container">
                                <p class="text item">状态：{{ checkStatus(robot) }}</p>
                                <el-checkbox-group v-model="robot.type">
                                    <el-checkbox value="Online activities" name="type">
                                        轨迹
                                    </el-checkbox>
                                </el-checkbox-group>
                            </div>

                            <p class="text item">序列号：  {{robot.sn}}</p>
                            <p class="text item">运行里程：{{robot.totalOdom}}</p>
                            <p class="text item">运行时间：{{robot.totalRunning}}</p>
                            <p class="text item">电池电量：{{robot.battery}}</p>
                            <p class="text item">当前任务：ETCH巡检</p>
                            <el-icon :size="20" style="cursor: pointer;" class="large-icon" @click="voiceClick(robot)"><Microphone/></el-icon>
                            <el-icon :size="20" style="cursor: pointer;" class="large-icon" @click="videoClick(robot)"><VideoCamera /></el-icon>
                            <el-icon :size="20" style="cursor: pointer;" class="large-icon"><Location /></el-icon>
                            <el-icon :size="20" style="cursor: pointer;" class="large-icon"><Lock /></el-icon>
                            <el-icon :size="20" style="cursor: pointer;" class="large-icon" @click="moreClick(robot)"><More /></el-icon>
                        </div>
                        <div v-if="moreOpen">
                            <el-row>
                                <el-col :span="6">
                                    <el-link type="primary" @click="faultClick">设为故障</el-link>
                                </el-col>
                                <el-col :span="6">
                                    <el-link type="primary" @click="getFultList">故障记录</el-link>
                                </el-col>
                                <el-col :span="6">
                                    <el-link type="primary" @click="resetClick">复位</el-link>
                                </el-col>
                            </el-row>
                        </div>
                    </el-collapse-item>
                </el-collapse>
            </div>
        </el-col>

    </el-row>

    <el-dialog title="故障记录" v-model="faultOpen" width="600px" append-to-body>
      <el-table :data="faultDataList">
         <el-table-column label="操作人" align="center" prop="createBy" />
         <el-table-column label="操作时间" align="center" prop="createTime">
            <template #default="scope">
               <span>{{ parseTime(scope.row.createTime) }}</span>
            </template>
         </el-table-column>
      </el-table>

      <pagination
         v-show="faultTotal > 0"
         :total="faultTotal"
         v-model:page="queryParams.pageNum"
         v-model:limit="queryParams.pageSize"
         @pagination="getFultList"
      />

         <template #footer>
         <div class="dialog-footer">
            <el-button @click="faultCancel">关 闭</el-button>
         </div>
         </template>
      </el-dialog>

      <el-dialog title="复位点位" v-model="resetOpen" width="600px" append-to-body>
        <el-form :model="bizPointForm" label-width="100px">
            <el-form-item label="复位点位" prop="bizPointId">
              <el-select v-model="bizPointForm.bizPointId" clearable filterable placeholder="请选择复位点位">
                <el-option v-for="item in bizPointList" :key="item.id" :label="item.instanceName" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
         </el-form>
         <template #footer>
            <div class="dialog-footer">
                <el-button type="primary" @click="submitForm">确 定</el-button>
                <el-button @click="resetCancel">关 闭</el-button>
            </div>
         </template>
      </el-dialog>

      <el-dialog title="语音" v-model="voiceOpen" width="550px" append-to-body>
            <div>
                <el-form label-width="auto" style="max-width: 600px">                                      
                    <el-row>
                        <el-col :span="8">
                            <el-form-item label="状态">
                                {{voiceConnect ? '已连接' : '未连接'}}
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="开启摄像头">
                                <el-switch v-model="turnOnCamera" @change="turnOnCameraChange()"/>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>                                                      
            </div>
            <div id="divPluginVoice" class="plugin"></div>
            <div style="display: flex; justify-content: center;margin-top:20px;">
                <el-button type="success" plain @click="clickStartVoiceTalk();" v-if="!voiceConnect">连接</el-button>
                <el-button type="danger" plain @click="clickStopVoiceTalk();" v-if="voiceConnect">挂断</el-button>
            </div>
         <template #footer>
            <div class="dialog-footer">
                <!-- <el-button @click="voiceCancel">关 闭</el-button> -->
            </div>
         </template>
      </el-dialog>

      <el-dialog title="视频" v-model="videoOpen" width="550px" append-to-body>
            <div id="divPluginVideo" class="plugin"></div>
            <div style="display: flex; justify-content: center;margin-top:20px;">
                <el-button type="primary" plain @click="clickCapturePicData();" >拍照</el-button>
                <el-button type="primary" @click="clickStartRecord('realplay');" :disabled="videoNumMap[wndIndex] != 0">开始录像</el-button>
				<el-button type="danger" @click="clickStopRecord('realplay');" :disabled="videoNumMap[wndIndex] == 0">停止录像</el-button>
                &nbsp;<span v-show="videoNumMap[wndIndex] != 0">{{ videoNumMap[wndIndex] }}</span>
                <el-button type="primary" plain @click="clickFullScreen();" >全屏</el-button>
            </div>
         <template #footer>
            <div class="dialog-footer">
                <!-- <el-button @click="voiceCancel">关 闭</el-button> -->
            </div>
         </template>
      </el-dialog>
   </div>
</template>

<script setup name="IntelligentRobotMap">
import {
    parseTime
} from '@/utils/hyc'
import {list as initData} from "@/api/intelligentRobot/intelligentRobotView"
import { listBizPoint } from "@/api/operation/robotChargingPile"

import logRobot from '@/api/log/logRobot';


const { proxy } = getCurrentInstance()
const { robot_status } = proxy.useDict("robot_status")

//故障窗口
const faultOpen = ref(false)
//故障列表
const faultDataList = ref([])
const faultTotal = ref(0)

//复位窗口
const resetOpen = ref(false)
//语音窗口
const voiceOpen = ref(false)
//视频窗口
const videoOpen = ref(false)


//所有机器人列表
const robotList = ref([])
//页面右侧展示机器人列表
const showRobotList = ref([])
//更多按钮
const moreOpen = ref(false)

//当前选中机器人id
const activeName = ref('');

//当前操作机器人
const currentRobot = ref({})
//复位点位列表
const bizPointList = ref([])

const data = reactive({
  bizPointForm: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    robotId: undefined,
    logType: undefined
  }
})

const { queryParams, bizPointForm} = toRefs(data)


let g_iWndIndex = 0; //可以不用设置这个变量，有窗口参数的接口中，不用传值，开发包会默认使用当前选择窗口
let g_oLocalConfig = null; //本地配置	
let ERROR_CODE_UNKNOWN = 1000; //未知错误
let ERROR_CODE_NETWORKERROR = 1001; //网络错误
let ERROR_CODE_PARAMERROR = 1002; //缺少插件元素		
let ERROR_CODE_LOGIN_NOLOGIN = 2000; // 未登录
let ERROR_CODE_LOGIN_REPEATLOGIN = 2001; //设备已登录，重复登录
let ERROR_CODE_LOGIN_NOSUPPORT = 2002; //当前设备不支持Digest登录
let ERROR_CODE_PLAY_PLUGININITFAIL = 3000; //插件初始化失败
let ERROR_CODE_PLAY_NOREPEATPLAY = 3001; //当前窗口已经在预览
let ERROR_CODE_PLAY_PLAYBACKABNORMAL = 3002; //回放异常
let ERROR_CODE_PLAY_PLAYBACKSTOP = 3003; //回放停止
let ERROR_CODE_PLAY_NOFREESPACE = 3004; //录像过程中，硬盘容量不足
let ERROR_CODE_TALK_FAIL = 5000; //语音对讲失败
let version = "V3.3.0build20230322";
let g_szRecordType = "";
let g_bPTZAuto = false;
let g_iSearchTimes = 0;
let g_iDownloadID = -1;
let g_tDownloadProcess = 0;
let g_tUpgrade = 0;
let g_bEnableDraw = false;


//获取机器人列表
function getList() {
  initData({}).then(res => {
    robotList.value = res.data;
    handleChange()
  })
}

//查询所有复位点位列表
function getBizPoint(){
  listBizPoint().then(res => {
    bizPointList.value = res.data;
  })
}

//机器人状态
function checkStatus(robot){
    var rs = robot_status.value.find(r=> r.value == robot.status)
    if(rs != null){
        return rs.label;
    }
}
//更多按钮点击
function moreClick(robot){
    moreOpen.value = !moreOpen.value;
}

//查询故障记录
function getFultList(){
    queryParams.value.robotId = currentRobot.value.id;
    queryParams.value.logType = '901';
    logRobot.getList(queryParams.value).then(response => {
        faultDataList.value = response.rows
        faultTotal.value = response.total
        faultOpen.value = true
    })
}



//设为故障点击
function faultClick(){
    if(currentRobot.value.status == '901'){
        proxy.$modal.msgError("【"+currentRobot.value.robotName+"】已经是故障状态")
        return false;
    }
    var form = {};
    form.robotId = currentRobot.value.id;
    form.logType = '901';
    form.content = "手动设置故障"
    proxy.$modal.confirm("是否确定把【"+currentRobot.value.robotName+"】设置为故障状态？").then(function () {
        return logRobot.fault(form)
    }).then(() => {
        getList()
        proxy.$modal.msgSuccess("设置成功")
    }).catch(() => {})
}

//故障记录关闭窗口
function faultCancel(){
    faultOpen.value = false;
    queryParams.value = {
        pageNum: 1,
        pageSize: 10,
        robotId: undefined,
        logType: undefined
    }
}

//复位按钮点击
function resetClick(){
    bizPointForm.value.bizPointId = null;
    resetOpen.value = true;
}

//复位窗口关闭
function resetCancel(){
    resetOpen.value = false;
}

/** 复位提交按钮 */
function submitForm() {
    if(bizPointForm.value.bizPointId == '' || bizPointForm.value.bizPointId == null || bizPointForm.value.bizPointId == undefined){
        proxy.$modal.msgError("请选择复位点位")
        return false;
    }
    var form = {};
    form.robotId = currentRobot.value.id;
    form.bizPointId = bizPointForm.value.bizPointId;
    form.content = "手动设置复位"
    proxy.$modal.confirm("是否确定把【"+currentRobot.value.robotName+"】复位？").then(function () {
        return logRobot.resetRobot(form)
    }).then(() => {
        resetOpen.value = false;
        getList()
        proxy.$modal.msgSuccess("复位成功")
    }).catch(() => {})
}

//展开/收缩事件
function handleChange(){
    if(activeName.value != '' && activeName.value != null){//展开
        let robot = robotList.value.find(item=> item.id == activeName.value)
        currentRobot.value = robot;
        showRobotList.value = [];
        showRobotList.value.push(robot)
    }else{//收缩
        moreOpen.value = false;
        currentRobot.value = {}
        showRobotList.value = [];
        showRobotList.value = robotList.value;
    }
}
function getItemStyle(item) {
   
    if(item.status == '1'){//工作
        return {
            "--header-text-color": "#67C23A",
        }
    }else if(item.status == '8'){//离线
        return {
            "--header-text-color": "#909399",
        }
    }else if(item.status == '901'){//故障
        return {
            "--header-text-color": "#E6A23C",
        }
    }else if(item.status == '902'){//硬件报警
        return {
            "--header-text-color": "#F56C6C",
        }
    }else{//其他
        return {
            "--header-text-color": "#409EFF",
        }
    }
}
  

getList()
getBizPoint()


//工控组件

//音频按钮点击
function voiceClick(robot){
    let cameraInfo = null
    if (robot.robotHardwareList.length > 0) {
	    selectionRobotHardwareList.value = robot.robotHardwareList
        robot.robotHardwareList.map(item => {
            // 6录像机 5可见光
            if ((!cameraInfo && item.hardwareType == '5')) {
                cameraInfo = item
            }
        })
    }
    if (!cameraInfo) {
        proxy.$modal.msgWarning('机器人没有配置可见光摄像机');
        return false;
    }

    loginip.value = cameraInfo.ip
    port.value = cameraInfo.port
    username.value = cameraInfo.account
    password.value = cameraInfo.pwd

    
    voiceOpen.value = true

    init();
}
//音频窗口关闭
function voiceCancel(){
    clickLogout();
    voiceOpen.value = false;

}

//视频按钮点击
function videoClick(robot){
    let cameraInfo = null
    if (robot.robotHardwareList.length > 0) {
	    selectionRobotHardwareList.value = robot.robotHardwareList
        robot.robotHardwareList.map(item => {
            // 6录像机 5可见光
            if ((!cameraInfo && item.hardwareType == '5')) {
                cameraInfo = item
            }
        })
    }
    if (!cameraInfo) {
        proxy.$modal.msgWarning('机器人没有配置可见光摄像机');
        return false;
    }

    loginip.value = cameraInfo.ip
    port.value = cameraInfo.port
    username.value = cameraInfo.account
    password.value = cameraInfo.pwd

    
    videoOpen.value = true;

    init();
}
//视频窗口关闭
function videoCancel(){
    clickLogout();
    videoOpen.value = false;
}


//开启摄像头switch改变
function turnOnCameraChange(){
    if(turnOnCamera.value == true){
        clickStartRealPlay()
    }else{
        clickStopRealPlay()
    }
}



const turnOnCamera = ref(false)

const ip = ref(null)

const loginip = ref(null)
const port = ref(null)
const username = ref(null)
const password = ref(null)

const ipList = ref([])
const channels = ref(null)
const channelsList = ref([])
const selectionRobotHardwareList = ref([])
const audiochannelsList = ref([])
const audiochannels = ref(null)
const videoNumMap= ref({
					'0': 0,
					'1': 0,
					'2': 0,
					'3': 0
				})
const wndIndex = ref(0)
const voiceConnect = ref(false)


function showCBInfo(szInfo) {
    console.log(parseTime(new Date()) + ' ' + szInfo)
}

function showOPInfo(szInfo, status, xmlDoc) {
    console.log(parseTime(new Date()) + ' ' + szInfo)
}

function init() {
    // 初始化插件参数及插入插件
    WebVideoCtrl.I_InitPlugin({
        bWndFull: true, //是否支持单窗口双击全屏，默认支持 true:支持 false:不支持
        iWndowType: 1,
        // aIframe: ["test"],
        cbSelWnd: function(xmlDoc) {
            g_iWndIndex = parseInt($(xmlDoc).find("SelectWnd").eq(0).text(), 10);
            var szInfo = "当前选择的窗口编号：" + g_iWndIndex;
            showCBInfo(szInfo);
        },
        cbDoubleClickWnd: function(iWndIndex, bFullScreen) {
            var szInfo = "当前放大的窗口编号：" + iWndIndex;
            if (!bFullScreen) {
                szInfo = "当前还原的窗口编号：" + iWndIndex;
            }
            showCBInfo(szInfo);
        },
        cbEvent: function(iEventType, iParam1, iParam2) {
            if (2 == iEventType) { // 回放正常结束
                showCBInfo("窗口" + iParam1 + "回放结束！");
            } else if (-1 == iEventType) {
                showCBInfo("设备" + iParam1 + "网络错误！");
            } else if (3001 == iEventType) {
                clickStopRecord(g_szRecordType, iParam1);
            }
        },
        cbInitPluginComplete: function() {
            var divPlugin = videoOpen.value ? 'divPluginVideo' : 'divPluginVoice'
            WebVideoCtrl.I_InsertOBJECTPlugin(divPlugin).then(() => {
                // 检查插件是否最新
                WebVideoCtrl.I_CheckPluginVersion().then((bFlag) => {
                    if (bFlag) {
                        alert("检测到新的插件版本，请安装确认后下载的HCWebSDKPluginsUserSetup.exe");
                    } else {
                        changeWndNum(1)
                        clickLogin(1)
                    }

                });
            }, () => {
                alert("插件初始化失败，请确认是否已安装插件；如果未安装，请安装确认后下载的HCWebSDKPluginsUserSetup.exe");
                goOnLink()
            });
        }

    });



    // 窗口事件绑定
    $(window).bind({
        resize: function() {
            //WebVideoCtrl.I_Resize($("body").width(), $("body").height());
        }
    });

}

function goOnLink() {
    const a = document.createElement('a') // 创建一个<a></a>标签
    a.href = '/HCWebSDKPluginsUserSetup.exe' // 给a标签的href属性值加上地址
    a.download = 'HCWebSDKPluginsUserSetup.exe' // 设置下载文件文件名，这里加上.xlsx指定文件类型，pdf文件就指定.fpd即可
    a.style.display = 'none' // 障眼法藏起来a标签
    document.body.appendChild(a) // 将a标签追加到文档对象中
    a.click() // 模拟点击了a标签，会触发a标签的href的读取，浏览器就会自动下载了
    a.remove() // 一次性的，用完就删除a标签

}

// 窗口分割数
function changeWndNum(iType) {
    if ("1*2" === iType || "2*1" === iType) {
        WebVideoCtrl.I_ArrangeWindow(iType).then(() => {
            showOPInfo("窗口分割成功！");
        }, (oError) => {
            var szInfo = "窗口分割失败！";
            showOPInfo(szInfo, oError.errorCode, oError.errorMsg);
        });
    } else {
        iType = parseInt(iType, 10);
        WebVideoCtrl.I_ChangeWndNum(iType).then(() => {
            showOPInfo("窗口分割成功！");
        }, (oError) => {
            var szInfo = "窗口分割失败！";
            showOPInfo(szInfo, oError.errorCode, oError.errorMsg);
        });
    }
}

// 登录			
function clickLogin(szProtoType) {
    let szIP = loginip.value
    let szPort = port.value
    let szUsername = username.value
    let szPassword = password.value

    if (!szIP || !szPort || !szUsername || !szPassword) {
        proxy.$modal.msgWarning('摄像机配置信息不完整');
        return false;
    }

    var szDeviceIdentify = szIP + "_" + szPort;

    WebVideoCtrl.I_Login(szIP, szProtoType, szPort, szUsername, szPassword, {
        success: function(xmlDoc) {
            showOPInfo(szDeviceIdentify + " 登录成功！");
            ipList.value.push(szDeviceIdentify)
            ip.value = szDeviceIdentify

            getChannelInfo();
        },
        error: function(oError) {
            if (ERROR_CODE_LOGIN_REPEATLOGIN === oError.errorCode) {
                showOPInfo(szDeviceIdentify + " 已登录过！");

            } else {
                if (oError.errorCode === 401) {
                    showOPInfo(szDeviceIdentify + " 登录失败，已自动切换认证方式！");
                } else {
                    showOPInfo(szDeviceIdentify + " 登录失败！", oError.errorCode, oError
                    .errorMsg);
                }
            }
        }
    });
}

// 退出			
function clickLogout(val) {
    var szDeviceIdentify = val || ip.value

    if (null == szDeviceIdentify) {
        return;
    }
    WebVideoCtrl.I_Logout(szDeviceIdentify).then(() => {
        ip.value = null
        channels.value = null;
        showOPInfo(szDeviceIdentify + " " + "退出成功！");
    }, () => {
        showOPInfo(szDeviceIdentify + " " + "退出失败！");
    });
}


// 获取通道			
function getChannelInfo() {
    let szDeviceIdentify = ip.value
    let oSel = channels.value
    channelsList.value = []

    if (null == szDeviceIdentify) {
        return;
    }

    // 模拟通道
    WebVideoCtrl.I_GetAnalogChannelInfo(szDeviceIdentify, {
        success: function(xmlDoc) {
            var oChannels = $(xmlDoc).find("VideoInputChannel");

            $.each(oChannels, function(i) {
                var id = $(this).find("id").eq(0).text(),
                    name = $(this).find("name").eq(0).text();
                if ("" == name) {
                    name = "Camera " + (i < 9 ? "0" + (i + 1) : (i + 1));
                }
                channelsList.value.push({
                    id: id,
                    name: name
                })
            });
            channels.value = '1'
            showOPInfo(szDeviceIdentify + " 获取模拟通道成功！");
            
            //如果是视频，直接打开预览
            if(videoOpen.value){
                clickStartRealPlay()
            }
            //如果是音频，获取音频通道
            if(voiceOpen.value){
                clickGetAudioInfo()
            }

        },
        error: function(oError) {
            showOPInfo(szDeviceIdentify + " 获取模拟通道失败！", oError.errorCode, oError.errorMsg);
        }
    });
    // 数字通道
    WebVideoCtrl.I_GetDigitalChannelInfo(szDeviceIdentify, {
        success: function(xmlDoc) {
            var oChannels = $(xmlDoc).find("InputProxyChannelStatus");

            $.each(oChannels, function(i) {
                var id = $(this).find("id").eq(0).text(),
                    name = $(this).find("name").eq(0).text(),
                    ipAddress = $(this).find("ipAddress").eq(0).text(),
                    online = $(this).find("online").eq(0).text();

                let hardwares = selectionRobotHardwareList.value.filter(f => f.ip ==
                    ipAddress)

                if ("false" == online || hardwares.length < 1) { // 过滤禁用的数字通道
                    return true;
                }
                if ("" == name) {
                    name = "IPCamera " + (i < 9 ? "0" + (i + 1) : (i + 1));
                }

                channelsList.value.push({
                    id: id,
                    name: name
                })

            });
            channels.value = channelsList.value[0].id
            showOPInfo(szDeviceIdentify + " 获取数字通道成功！");
        },
        error: function(oError) {
            showOPInfo(szDeviceIdentify + " 获取数字通道失败！", oError.errorCode, oError.errorMsg);
        }
    });
}
// 开始预览	
function clickStartRealPlay(iStreamType) {
    let oWndInfo = WebVideoCtrl.I_GetWindowStatus(g_iWndIndex)
    let szDeviceIdentify = ip.value
    let iChannelID = channels.value
    let szInfo = "";

    if (null == szDeviceIdentify) {
        return;
    }
    var startRealPlay = function() {
        WebVideoCtrl.I_StartRealPlay(szDeviceIdentify, {
            iChannelID: iChannelID,
            success: function() {
                szInfo = "开始预览成功！";
                showOPInfo(szDeviceIdentify + " " + szInfo);
            },
            error: function(oError) {
                showOPInfo(szDeviceIdentify + " 开始预览失败！", oError.errorCode, oError
                    .errorMsg);
            }
        });
    };

    if (oWndInfo != null) { // 已经在播放了，先停止
        WebVideoCtrl.I_Stop({
            success: function() {
                startRealPlay();
            }
        });
    } else {
        startRealPlay();
    }
}

// 停止预览			
function clickStopRealPlay() {
    let oWndInfo = WebVideoCtrl.I_GetWindowStatus(g_iWndIndex)
    let szInfo = "";
    if (oWndInfo != null) {
        WebVideoCtrl.I_Stop({
            success: function() {
                szInfo = "停止预览成功！";
                showOPInfo(oWndInfo.szDeviceIdentify + " " + szInfo);
            },
            error: function(oError) {
                showOPInfo(szDeviceIdentify + " 停止预览失败！", oError.errorCode, oError.errorMsg);
            }
        });
    }
}

// 打开声音			
function clickOpenSound() {
    var oWndInfo = WebVideoCtrl.I_GetWindowStatus(g_iWndIndex),
        szInfo = "";
    if (oWndInfo != null) {

        WebVideoCtrl.I_OpenSound().then(() => {
            showOPInfo(oWndInfo.szDeviceIdentify + " " + "打开声音成功！");
        }, (oError) => {
            var szInfo = " 打开声音失败！";
            showOPInfo(oWndInfo.szDeviceIdentify + szInfo, oError.errorCode, oError.errorMsg);
        });
    }
}
// 关闭声音			
function clickCloseSound() {
    var oWndInfo = WebVideoCtrl.I_GetWindowStatus(g_iWndIndex),
        szInfo = "";

    if (oWndInfo != null) {
        WebVideoCtrl.I_CloseSound().then(() => {
            showOPInfo(oWndInfo.szDeviceIdentify + " " + "关闭声音成功！");
        }, (oError) => {
            var szInfo = " 关闭声音失败！";
            showOPInfo(oWndInfo.szDeviceIdentify + szInfo, oError.errorCode, oError.errorMsg);
        });
    }
}

// 获取对讲通道			
function clickGetAudioInfo() {
    var szDeviceIdentify = ip.value

    if (null == szDeviceIdentify) {
        return;
    }
    audiochannelsList.value = []
    WebVideoCtrl.I_GetAudioInfo(szDeviceIdentify, {
        success: function(xmlDoc) {
            var oAudioChannels = $(xmlDoc).find("TwoWayAudioChannel")
            $.each(oAudioChannels, function() {
                var id = $(this).find("id").eq(0).text();
                audiochannelsList.value.push({
                    id: id
                })
            });
            audiochannels.value = audiochannelsList.value[0].id
            showOPInfo(szDeviceIdentify + " 获取对讲通道成功！");
        },
        error: function(oError) {
            showOPInfo(szDeviceIdentify + " 获取对讲通道失败！", oError.errorCode, oError.errorMsg);
        }
    });
}

// 开始对讲			
function clickStartVoiceTalk() {
    var szDeviceIdentify = ip.value
    let iAudioChannel = audiochannels.value
    let szInfo = "";

    if (null == szDeviceIdentify) {
        return;
    }

    if (isNaN(iAudioChannel)) {
        proxy.$modal.msgWarning("请选择对讲通道！");
        return false;
    }
    WebVideoCtrl.I_StartVoiceTalk(szDeviceIdentify, iAudioChannel).then(() => {
        szInfo = "开始对讲成功！";
        voiceConnect.value = true;
        showOPInfo(szDeviceIdentify + " " + szInfo);
    }, (oError) => {
        var szInfo = " 开始对讲失败！";
        showOPInfo(szDeviceIdentify + szInfo, oError.errorCode, oError.errorMsg);
    });
}

// 停止对讲			
function clickStopVoiceTalk() {
    let szInfo = "";
    var szDeviceIdentify = ip.value
    WebVideoCtrl.I_StopVoiceTalk().then(() => {
        szInfo = "停止对讲成功！";
        voiceConnect.value = false;
        showOPInfo(szDeviceIdentify + " " + szInfo);
    }, (oError) => {
        var szInfo = " 停止对讲失败！";
        showOPInfo(szDeviceIdentify + szInfo, oError.errorCode, oError.errorMsg);
    });
}


// 抓图				
function clickCapturePicData() {
    var oWndInfo = WebVideoCtrl.I_GetWindowStatus(g_iWndIndex),
        szInfo = "";
    if (oWndInfo != null) {
        WebVideoCtrl.I_CapturePicData().then(function(res) {
            // console.log(res);
            const data = window.atob(res)
            const ia = new Uint8Array(data.length)
            for (let i = 0; i < data.length; i++) {
                ia[i] = data.charCodeAt(i)
            }
            const blob = new Blob([ia], {
                type: 'image/png'
            })
            const downloadUrl = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = downloadUrl;
            link.download = 'image_' + Date.now() + '.png';
            link.click();
            URL.revokeObjectURL(downloadUrl);

            szInfo = "抓图上传成功！";
            showOPInfo(oWndInfo.szDeviceIdentify + " " + szInfo);
        }, function() {
            szInfo = "抓图失败！";
            showOPInfo(oWndInfo.szDeviceIdentify + " " + szInfo);
        });
    }
}



function clickStartRecord(szType) {
    videoNumMap.value[g_iWndIndex] = 1
    
    var oWndInfo = WebVideoCtrl.I_GetWindowStatus(g_iWndIndex),
        szInfo = "";

    if (oWndInfo != null) {
        let robotName = currentRobot.value.robotName;
        let hardwareName = '';

       currentRobot.value.robotHardwareList.map(item2 => {
            if (item2.ip == loginip) {
                robotName = item.robotName
                hardwareName = item2.hardwareName
            }
        })
        alert(robotName)
        alert(hardwareName)

        let szFileName = robotName + '_' + hardwareName + "_" + new Date().getTime();
        // let szFileName = 'robotName' + '_' + 'hardwareName' + "_" + new Date().getTime();

        WebVideoCtrl.I_StartRecord(szFileName, {
            success: function() {
                if ('realplay' === szType) {
                    videoNumPlus(g_iWndIndex)
                    szInfo = "开始录像成功！";
                } else if ('playback' === szType) {
                    szInfo = "开始剪辑成功！";
                }
                console.log(oWndInfo.szDeviceIdentify + " " + szInfo);
            },
            error: function(oError) {
                if ('realplay' === szType) {
                    szInfo = " 开始录像失败！";
                } else if ('playback' === szType) {
                    szInfo = " 开始剪辑失败！";
                }
                console.log(oWndInfo.szDeviceIdentify + szInfo, oError.errorCode, oError.errorMsg);
            }
        });
    }
}
function clickStopRecord(szType, iWndIndex) {
    if ("undefined" === typeof iWndIndex) {
        iWndIndex = g_iWndIndex;
    }
    var oWndInfo = WebVideoCtrl.I_GetWindowStatus(iWndIndex),
        szInfo = "";

    if (oWndInfo != null) {
        WebVideoCtrl.I_StopRecord({
            success: function() {
                if ('realplay' === szType) {
                    videoNumMap.value[iWndIndex] = 0;
                    szInfo = "停止录像成功！";
                    proxy.$modal.msgSuccess(
                        '录像文件在 C:\\Users\\<USER>\\HCWebSDKPlugins\\PlaybackFiles 下，请用专用播放器（可在摄像机页下载）'
                        );
                } else if ('playback' === szType) {
                    szInfo = "停止剪辑成功！";
                }
                console.log(oWndInfo.szDeviceIdentify + " " + szInfo);
            },
            error: function(oError) {
                if ('realplay' === szType) {
                    szInfo = "停止录像失败！";
                } else if ('playback' === szType) {
                    szInfo = "停止剪辑失败！";
                }
                console.log(oWndInfo.szDeviceIdentify + szInfo, oError.errorCode, oError.errorMsg);
            }
        });
    }
}
function videoNumPlus(index) {
    setTimeout(() => {
        if (videoNumMap.value[index] != 0) {
            videoNumMap.value[index] ++;
            videoNumPlus.value(index)
        }
    }, 1000)
}

// 全屏			
function clickFullScreen() {
    WebVideoCtrl.I_FullScreen(true).then(() => {
        showOPInfo("全屏成功");
    }, (oError) => {
        showOPInfo("全屏失败！", oError.errorCode, oError.errorMsg);
    });
}

</script>
<style>
    .large-icon {
        margin-left: 15px;
    }
</style>
<style scoped>
	.PTZ .el-input {
		width: 160px;
	}

	.PTZ .el-select {
		width: 160px;
	}

	.plugin {
		width: 100%;
		height: 400px;
	}

	.PTZ_direction .el-button {
		width: 60px;
		margin: 0;
	}
</style>
<style>
    /* 使用 CSS 变量 */
    .el-collapse-item__header {
    /* background-color: var(--header-bg-color) !important; */
        color: var(--header-text-color) !important;
    }

    /* 将展开图标移动到左侧 */
    .el-collapse-item__header {
        flex-direction: row-reverse; /* 反转标题内容的排列方向 */
        justify-content: flex-end;   /* 将内容对齐到左侧 */
    }

    /* 调整图标与文本的间距 */
    .el-collapse-item__arrow {
        margin-right: 8px;           /* 图标右侧间距 */
        margin-left: 0;             /* 清除默认左侧间距 */
    }

    .robot-status-container {
        display: flex;          /* 启用 Flex 布局 */
        align-items: center;    /* 垂直居中 */
        gap: 20px;              /* 元素间距 */
        margin-bottom: -10px;
    }
</style>
