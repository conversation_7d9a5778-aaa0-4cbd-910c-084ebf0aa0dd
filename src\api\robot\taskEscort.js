import request from '@/utils/request'

export function list(query) {
    return request({
        url: '/robot/taskEscort/list',
        method: 'get',
        params: query
    })
}

export function getDict(query) {
    return request({
        url: '/robot/taskEscort/getDict',
        method: 'get',
        params: query
    })
}


export function addTaskEscort(data) {
    return request({
        url: '/robot/taskEscort/addTaskEscort',
        method: 'post',
        data: data
    })
}


export function editTaskEscort(data) {
    return request({
        url: '/robot/taskEscort/editTaskEscort',
        method: 'post',
        data: data
    })
}


export function removeTaskEscort(data) {
    return request({
        url: '/robot/taskEscort/removeTaskEscort',
        method: 'post',
        data: data
    })

}
export function startTask(data) {
    return request({
        url: '/robot/taskEscort/startTask',
        method: 'post',
        data: data
    })

}

export function getLogs(query) {
    return request({
        url: '/robot/taskEscort/getLogs',
        method: 'get',
        params: query
    })
}


export function closeAlarm(data) {
    return request({
        url: '/robot/taskEscort/closeAlarm',
        method: 'post',
        data: data
    })
}

export function openAlarm(data) {
    return request({
        url: '/robot/taskEscort/openAlarm',
        method: 'post',
        data: data
    })
}
export function endTask(data) {
    return request({
        url: '/robot/taskEscort/endTask',
        method: 'post',
        data: data
    })
}
