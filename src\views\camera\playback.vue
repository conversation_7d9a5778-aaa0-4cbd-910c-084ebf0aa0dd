<template>
	<div class="app-container">
		<el-row>
			<el-col :span="6">
				<el-date-picker v-model="starttime" type="datetimerange" start-placeholder="开始时间" end-placeholder="结束时间"
					value-format="YYYY-MM-DD HH:mm:ss" style="margin-bottom: 10px;" :teleported="false" />

				<el-tree style="width: 100%; height: calc(100vh - 210px); overflow-y: auto;" :data="robotList" :props="props"
					show-checkbox @check-change="checkChange" default-expand-all />

			</el-col>
			<el-col :span="18">
				<div id="divPlugin" class="plugin"></div>
				<el-button @click="pause = !pause; clickPause();">暂停/播放</el-button>
				<el-button @click="clickPlaySlow();" :disabled="playFastMap[wndIndex] <= -4">慢放</el-button>
				<el-button @click="clickPlayFast();" :disabled="playFastMap[wndIndex] >= 4">快放</el-button>&nbsp;
				<span v-show="playFastMap[wndIndex] != 0">x {{ playFastMap[wndIndex] }}</span>&nbsp;
				<el-button @click="clickCapturePicData();">抓图</el-button>
				<el-button @click="clickStartRecord('realplay');"
					:disabled="videoNumMap[wndIndex] != 0">开始录像</el-button>
				<el-button @click="clickStopRecord('realplay');" :disabled="videoNumMap[wndIndex] == 0">停止录像</el-button>
				&nbsp;<span v-show="videoNumMap[wndIndex] != 0">{{ videoNumMap[wndIndex] }}</span>
			</el-col>
		</el-row>
	</div>
</template>

<script>
	import robot from '@/api/robot/robot';
	import {
		parseTime
	} from '@/utils/hyc'

	let g_iWndIndex = 0;
	let g_szRecordType = "";

	export default {
		data() {
			return {
				wndIndex: 0,
				playFastMap: {
					'0': 0,
					'1': 0,
					'2': 0,
					'3': 0
				},
				videoNumMap: {
					'0': 0,
					'1': 0,
					'2': 0,
					'3': 0
				},
				videoNum: 0,
				playFast: 0,
				pause: false,
				starttime: null,
				robotList: [],
				props: {
					value: 'id',
					label: 'label',
					children: 'children',
				},
				loginip: null,
				port: null,
				username: null,
				password: null,
				channels: null,
				channelsList: [],
				ip: null,
				selectionRobotHardwareList: [],
				selectionRobotCamera: null,
				selectinRobotId: null,
				selectinCameraIP: null,
				playbackIWndIndexMap: {},
				channelInfoList: [],
				VCRInitNum: {}
			}
		},
		created() {
			this.loadRobot()
		},
		mounted() {
			this.init()
			this.defaultTime()
			this.initIWndIndexMap()
		},
		methods: {
			initIWndIndexMap() {
				for (var i = 0; i < 4; i++) {
					this.playbackIWndIndexMap[i] = null
				}
			},
			defaultTime() {
				let date = new Date
				date.setDate(date.getDate() - 1)
				this.starttime = [parseTime(date), parseTime(new Date)]
			},
			checkChange(data, checked) {
				console.log(data, checked)
				let vcr = null
				if (checked && data.hardwareType) {
					let robot = this.robotList.filter(f => f.id == data.robotId)
					vcr = robot[0].robotHardwareList.filter(f => f.hardwareType == '6');
					if (vcr.length < 1) {
						return this.$modal.msgWarning('机器人没有配置录像机');
					}
				}
				if (checked && data.hardwareType) {
					let vin = this.VCRInitNum[vcr[0].ip.replaceAll('.', '')];
					if (vin && vin > 0) {
						this.clickLogin(1, vcr[0], data, true)
					} else {
						this.VCRInitNum[vcr[0].ip.replaceAll('.', '')] = 1
						console.log(this.VCRInitNum)
						for (var i = 0; i < 2; i++) {
							this.clickLogin(1, vcr[0], data, i == 0 ? false : true)
						}
					}
				} else if (!checked && data.hardwareType) {

					for (var i = 0; i < 4; i++) {
						let ip = this.playbackIWndIndexMap[i]

						if (ip != null) {
							if (data.hardwareType != '5' && ip == data.ip) {
								this.clickStopPlayback(i)
							} else if (ip == (data.ip + '_（' + data.label.split('（')[1])) {
								this.clickStopPlayback(i)
							}
						}
					}
				}
			},
			loadRobot() {
				robot.getAll().then(res => {
					res.data.map(item => {
						item.children = []
						item.label = item.robotName
						item.disabled = true
						item.robotHardwareList.map(item2 => {
							item2.label = item2.hardwareName
							if (item2.hardwareType == '1') {
								item.children.push(item2)
							} else if (item2.hardwareType == '5') {
								for (var i = 0; i < 2; i++) {
									let camera = JSON.parse(JSON.stringify(item2))
									if (i == 0) {
										camera.label += '（可见光）'
										item.children.push(camera)
									} else {
										camera.label += '（热成像）'
										item.children.push(camera)
									}
								}
							}
						})
					})
					console.log(res.data)
					this.robotList = res.data
				})
			},
			init() {
				let that = this
				// 初始化插件参数及插入插件
				WebVideoCtrl.I_InitPlugin({
					bWndFull: true,
					iWndowType: 1,
					cbSelWnd: function(xmlDoc) {
						g_iWndIndex = parseInt($(xmlDoc).find("SelectWnd").eq(0).text(), 10);
						that.wndIndex = g_iWndIndex;
						var szInfo = "当前选择的窗口编号：" + g_iWndIndex;
						console.log(szInfo);
					},
					cbDoubleClickWnd: function(iWndIndex, bFullScreen) {
						var szInfo = "当前放大的窗口编号：" + iWndIndex;
						if (!bFullScreen) {
							szInfo = "当前还原的窗口编号：" + iWndIndex;
						}
						console.log(szInfo);
					},
					cbEvent: function(iEventType, iParam1, iParam2) {
						if (2 == iEventType) { // 回放正常结束
							console.log("窗口" + iParam1 + "回放结束！");
						} else if (-1 == iEventType) {
							console.log("设备" + iParam1 + "网络错误！");
						} else if (3001 == iEventType) {
							that.clickStopRecord(g_szRecordType, iParam1);
						}
					},
					cbInitPluginComplete: function() {
						WebVideoCtrl.I_InsertOBJECTPlugin("divPlugin").then(() => {
							// 检查插件是否最新
							WebVideoCtrl.I_CheckPluginVersion().then((bFlag) => {
								if (bFlag) {
									alert("检测到新的插件版本，请安装确认后下载的HCWebSDKPluginsUserSetup.exe");
								} else {
									that.changeWndNum(2)
								}

							});
						}, () => {
							alert("插件初始化失败，请确认是否已安装插件；如果未安装，请安装确认后下载的HCWebSDKPluginsUserSetup.exe");
							that.goOnLink()
						});
					}

				});

			},
			changeWndNum(iType) {
				if ("1*2" === iType || "2*1" === iType) {
					WebVideoCtrl.I_ArrangeWindow(iType).then(() => {
						console.log("窗口分割成功！");
					}, (oError) => {
						var szInfo = "窗口分割失败！";
						console.log(szInfo, oError.errorCode, oError.errorMsg);
					});
				} else {
					iType = parseInt(iType, 10);
					WebVideoCtrl.I_ChangeWndNum(iType).then(() => {
						console.log("窗口分割成功！");
					}, (oError) => {
						var szInfo = "窗口分割失败！";
						console.log(szInfo, oError.errorCode, oError.errorMsg);
					});
				}
			},
			goOnLink() {
				const a = document.createElement('a') // 创建一个<a></a>标签
				a.href = '/HCWebSDKPluginsUserSetup.exe' // 给a标签的href属性值加上地址
				a.download = 'HCWebSDKPluginsUserSetup.exe' // 设置下载文件文件名，这里加上.xlsx指定文件类型，pdf文件就指定.fpd即可
				a.style.display = 'none' // 障眼法藏起来a标签
				document.body.appendChild(a) // 将a标签追加到文档对象中
				a.click() // 模拟点击了a标签，会触发a标签的href的读取，浏览器就会自动下载了
				a.remove() // 一次性的，用完就删除a标签

			},
			clickLogout(val) {
				let that = this
				var szDeviceIdentify = val || this.ip

				if (null == szDeviceIdentify) {
					return;
				}

				WebVideoCtrl.I_Logout(szDeviceIdentify).then(() => {
					that.ip = null
					console.log(szDeviceIdentify + " " + "退出成功！");
				}, () => {
					console.log(szDeviceIdentify + " " + "退出失败！");
				});
			},
			clickLogin(szProtoType, vcr, data, witch) {
				let that = this
				let szIP = vcr.ip
				let szPort = vcr.port
				let szUsername = vcr.account
				let szPassword = vcr.pwd

				if (!szIP || !szPort || !szUsername || !szPassword) {
					return this.$modal.msgWarning('录像机配置信息不完整');
				}

				var szDeviceIdentify = szIP + "_" + szPort;

				WebVideoCtrl.I_Login(szIP, szProtoType, szPort, szUsername, szPassword, {
					success: function(xmlDoc) {
						console.log(szDeviceIdentify + " 登录成功！");
						// that.ip = szDeviceIdentify
						if (witch) {
							that.getChannelInfo(szDeviceIdentify, data);
						}
					},
					error: function(oError) {
						if (2001 === oError.errorCode) {
							console.log(szDeviceIdentify + " 已登录过！");
							// that.ip = szDeviceIdentify
							if (witch) {
								that.getChannelInfo(szDeviceIdentify, data);
							}
						} else {
							if (oError.errorCode === 401) {
								console.log(szDeviceIdentify + " 登录失败，已自动切换认证方式！");
							} else {
								console.log(szDeviceIdentify + " 登录失败！", oError.errorCode, oError.errorMsg);
							}
						}
					}
				});
			},
			getChannelInfo(szDeviceIdentify, data) {
				let that = this
				// let szDeviceIdentify = this.ip
				// let oSel = this.channels
				let channelsList = []

				// 数字通道
				WebVideoCtrl.I_GetDigitalChannelInfo(szDeviceIdentify, {
					success: function(xmlDoc) {
						var oChannels = $(xmlDoc).find("InputProxyChannelStatus");

						$.each(oChannels, function(i) {
							var id = $(this).find("id").eq(0).text(),
								name = $(this).find("name").eq(0).text(),
								ipAddress = $(this).find("ipAddress").eq(0).text(),
								srcInputPort = $(this).find("srcInputPort").eq(0).text(),
								online = $(this).find("online").eq(0).text();

							if ("false" == online || ipAddress != data.ip) { // 过滤禁用的数字通道
								return true;
							}
							if ("" == name) {
								name = "IPCamera " + (i < 9 ? "0" + (i + 1) : (i + 1));
							}

							channelsList.push({
								VCRIP: szDeviceIdentify,
								iChannelID: id,
								cameraIP: ipAddress
							})

						});

						if (data.hardwareType == '5' && data.label.includes('（可见光）')) {
							channelsList.splice(1, 1)
							channelsList[0].label = '（可见光）'
						} else if (data.hardwareType == '5' && data.label.includes('（热成像）')) {
							channelsList.splice(0, 1)
							channelsList[0].label = '（热成像）'
						}
						console.log('通道集合', channelsList)

						// that.channels = that.channelsList[0].id
						console.log(szDeviceIdentify + " 获取数字通道成功！");
						// g_iWndIndex = 0;
						if (channelsList.length == 0) {
							return that.$modal.msgWarning(data.hardwareName + ' 没有可回放的视频');
						}

						let num = 0
						for (var i = 0; i < 4; i++) {
							if (that.playbackIWndIndexMap[i] == null) {
								num++;
							}
						}
						if (num == 0) {
							return that.$modal.msgWarning('预览回放窗口已满');
						} else if (channelsList.length > num) {
							return that.$modal.msgWarning('预览回放窗口不足');
						}

						channelsList.map(item => {
							for (var i = 0; i < 4; i++) {
								if (that.playbackIWndIndexMap[i] == null) {
									if (data.hardwareType != '5') {
										that.playbackIWndIndexMap[i] = item.cameraIP;
									} else {
										that.playbackIWndIndexMap[i] = item.cameraIP + '_' + item
											.label;
									}
									that.clickStartPlayback(szDeviceIdentify, item.iChannelID, i, item
										.cameraIP)
									break
								}
							}

						})
					},
					error: function(oError) {
						console.log(szDeviceIdentify + " 获取数字通道失败！", oError.errorCode, oError.errorMsg);
					}
				});
			},
			clickStartPlayback(szDeviceIdentify, iChannelID, index, cameraIP) {
				let that = this
				console.log('g_iWndIndex：', index, ' | channelID：', iChannelID)

				var oWndInfo = WebVideoCtrl.I_GetWindowStatus(index)

				let szStartTime = this.starttime[0]
				let szEndTime = this.starttime[1]
				let szInfo = ""

				if (!szStartTime || !szEndTime) {
					return this.$modal.msgWarning("开始或结束时间不能为空！");
				}
				if (Date.parse(szEndTime.replace(/-/g, "/")) - Date.parse(szStartTime.replace(/-/g, "/")) < 0) {
					return this.$modal.msgWarning("开始时间大于结束时间");
				}

				var startPlayback = function() {
					WebVideoCtrl.I_StartPlayback(szDeviceIdentify, {
						iWndIndex: index,
						iChannelID: iChannelID,
						szStartTime: szStartTime,
						szEndTime: szEndTime,
						success: function() {
							szInfo = "开始回放成功！";
							console.log(szDeviceIdentify + " " + szInfo);
							// that.playbackIWndIndexMap[index] = cameraIP

						},
						error: function(oError) {
							szInfo = " 开始回放失败！";
							console.log(szDeviceIdentify + szInfo, oError.errorCode, oError.errorMsg);
							that.playbackIWndIndexMap[index] = null
						}
					});
				};

				if (oWndInfo != null) { // 已经在播放了，先停止
					WebVideoCtrl.I_Stop({
						iWndIndex: index,
						success: function() {
							startPlayback();
						}
					});
				} else {
					startPlayback();
				}
			},
			clickStopPlayback(index) {
				let that = this
				var oWndInfo = WebVideoCtrl.I_GetWindowStatus(g_iWndIndex),
					szInfo = "";

				if (oWndInfo != null) {
					WebVideoCtrl.I_Stop({
						iWndIndex: index,
						success: function() {
							szInfo = "停止回放成功！";
							console.log(oWndInfo.szDeviceIdentify + " " + szInfo);
							that.playbackIWndIndexMap[index] = null
						},
						error: function(oError) {
							szInfo = "停止回放失败！";
							console.log(szDeviceIdentify + szInfo, oError.errorCode, oError.errorMsg);
						}
					});
				}
			},
			clickPause() {
				var oWndInfo = WebVideoCtrl.I_GetWindowStatus(g_iWndIndex),
					szInfo = "";

				if (this.pause) {
					if (oWndInfo != null) {
						WebVideoCtrl.I_Pause({
							success: function() {
								szInfo = "暂停成功！";
								console.log(oWndInfo.szDeviceIdentify + " " + szInfo);
							},
							error: function(oError) {
								szInfo = "暂停失败！";
								console.log(oWndInfo.szDeviceIdentify + szInfo, oError.errorCode, oError
									.errorMsg);
							}
						});
					}
				} else {
					if (oWndInfo != null) {
						WebVideoCtrl.I_Resume({
							success: function() {
								szInfo = "恢复成功！";
								console.log(oWndInfo.szDeviceIdentify + " " + szInfo);
							},
							error: function(oError) {
								szInfo = "恢复失败！";
								console.log(oWndInfo.szDeviceIdentify + szInfo, oError.errorCode, oError
									.errorMsg);
							}
						});
					}
				}

			},
			clickPlaySlow() {
				if (this.playFastMap[g_iWndIndex] <= -4) {
					return this.$modal.msgWarning("慢放已经最大！");
				}
				this.playFastMap[g_iWndIndex]--;
				var oWndInfo = WebVideoCtrl.I_GetWindowStatus(g_iWndIndex),
					szInfo = "";

				if (oWndInfo != null) {
					WebVideoCtrl.I_PlaySlow({
						success: function() {
							szInfo = "慢放成功！";
							console.log(oWndInfo.szDeviceIdentify + " " + szInfo);
						},
						error: function(oError) {
							szInfo = "慢放失败！";
							console.log(oWndInfo.szDeviceIdentify + szInfo, oError.errorCode, oError.errorMsg);
						}
					});
				}
			},
			// 快放
			clickPlayFast() {
				if (this.playFastMap[g_iWndIndex] >= 4) {
					return this.$modal.msgWarning("快放已经最大！");
				}
				this.playFastMap[g_iWndIndex]++;

				var oWndInfo = WebVideoCtrl.I_GetWindowStatus(g_iWndIndex),
					szInfo = "";

				if (oWndInfo != null) {
					WebVideoCtrl.I_PlayFast({
						success: function() {
							szInfo = "快放成功！";
							console.log(oWndInfo.szDeviceIdentify + " " + szInfo);
						},
						error: function(oError) {
							szInfo = "快放失败！";
							console.log(oWndInfo.szDeviceIdentify + szInfo, oError.errorCode, oError.errorMsg);
						}
					});
				}
			},
			clickCapturePicData() {
				var oWndInfo = WebVideoCtrl.I_GetWindowStatus(g_iWndIndex),
					szInfo = "";
				if (oWndInfo != null) {
					WebVideoCtrl.I_CapturePicData().then(function(res) {
						// console.log(res);
						const data = window.atob(res)
						const ia = new Uint8Array(data.length)
						for (let i = 0; i < data.length; i++) {
							ia[i] = data.charCodeAt(i)
						}
						const blob = new Blob([ia], {
							type: 'image/png'
						})
						const downloadUrl = URL.createObjectURL(blob);
						const link = document.createElement('a');
						link.href = downloadUrl;
						link.download = 'image_' + Date.now() + '.png';
						link.click();
						URL.revokeObjectURL(downloadUrl);

						szInfo = "抓图上传成功！";
						console.log(oWndInfo.szDeviceIdentify + " " + szInfo);
					}, function() {
						szInfo = "抓图失败！";
						console.log(oWndInfo.szDeviceIdentify + " " + szInfo);
					});
				}
			},
			clickStartRecord(szType) {
				let that = this
				that.videoNumMap[g_iWndIndex] = 1

				var oWndInfo = WebVideoCtrl.I_GetWindowStatus(g_iWndIndex),
					szInfo = "";

				if (oWndInfo != null) {
					let robotName = ''
					let hardwareName = ''
					let cameraIP = this.playbackIWndIndexMap[oWndInfo.iIndex]

					this.robotList.map(item => {
						item.robotHardwareList.map(item2 => {
							if (item2.ip == cameraIP) {
								robotName = item.robotName
								hardwareName = item2.hardwareName
							}
						})
					})

					let szFileName = robotName + '_' + hardwareName + "_" + new Date().getTime();

					WebVideoCtrl.I_StartRecord(szFileName, {
						success: function() {
							if ('realplay' === szType) {
								that.videoNumPlus(g_iWndIndex)
								szInfo = "开始录像成功！";
							} else if ('playback' === szType) {
								szInfo = "开始剪辑成功！";
							}
							console.log(oWndInfo.szDeviceIdentify + " " + szInfo);
						},
						error: function(oError) {
							if ('realplay' === szType) {
								szInfo = " 开始录像失败！";
							} else if ('playback' === szType) {
								szInfo = " 开始剪辑失败！";
							}
							console.log(oWndInfo.szDeviceIdentify + szInfo, oError.errorCode, oError.errorMsg);
						}
					});
				}
			},
			clickStopRecord(szType, iWndIndex) {
				let that = this
				if ("undefined" === typeof iWndIndex) {
					iWndIndex = g_iWndIndex;
				}
				var oWndInfo = WebVideoCtrl.I_GetWindowStatus(iWndIndex),
					szInfo = "";

				if (oWndInfo != null) {
					WebVideoCtrl.I_StopRecord({
						success: function() {
							if ('realplay' === szType) {
								that.videoNumMap[iWndIndex] = 0;
								szInfo = "停止录像成功！";
								that.$modal.msgSuccess(
									'录像文件在 C:\\Users\\<USER>\\HCWebSDKPlugins\\PlaybackFiles 下，请用专用播放器（可在摄像机页下载）'
								);
							} else if ('playback' === szType) {
								szInfo = "停止剪辑成功！";
							}
							console.log(oWndInfo.szDeviceIdentify + " " + szInfo);
						},
						error: function(oError) {
							if ('realplay' === szType) {
								szInfo = "停止录像失败！";
							} else if ('playback' === szType) {
								szInfo = "停止剪辑失败！";
							}
							console.log(oWndInfo.szDeviceIdentify + szInfo, oError.errorCode, oError.errorMsg);
						}
					});
				}
			},
			videoNumPlus(index) {
				setTimeout(() => {
					if (this.videoNumMap[index] != 0) {
						this.videoNumMap[index]++;
						this.videoNumPlus(index)
					}
				}, 1000)
			},
		}
	}
</script>

<style scoped>
	.plugin {
		width: 100%;
		height: 700px;
		margin-bottom: 10px;
	}

	/deep/.el-date-range-picker__time-header {
		display: none;
		width: 400px;
	}

	/deep/.el-date-range-picker__content.is-left {
		width: 200px;
	}

	/deep/.el-date-range-picker .el-picker-panel__content {
		width: 200px;
	}

	/deep/.el-picker-panel__footer {
		width: 400px;
	}

	/deep/.el-date-range-picker .el-picker-panel__body {
		width: 400px;
	}

	/deep/.el-date-range-picker {
		width: 400px;
	}

	/deep/.el-date-range-picker__content .el-date-range-picker__header div {
		font-size: 10px;
	}

	/deep/.el-checkbox__input.is-disabled {
		display: none;
	}
		
	::-webkit-scrollbar {
	  width: 0;
	}
	::-webkit-scrollbar-track {
	  background: transparent; 
	}
</style>