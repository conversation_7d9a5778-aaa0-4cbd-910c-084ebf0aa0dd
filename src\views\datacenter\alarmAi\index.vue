<template>
   <div class="app-container">
        <el-row :gutter="40" style="height: 100%">
            <el-col span="4">
                <el-card shadow="always">
                    <leftTree></leftTree>
                </el-card>
            </el-col>
            <el-col :span="20">
                <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="88px">
                    <el-form-item label="报警名称" prop="alarmName">
                        <el-input
                        v-model="queryParams.alarmName"
                        placeholder="请输入报警名称"
                        clearable
                        style="width: 240px"
                        @keyup.enter="handleQuery"
                        />
                    </el-form-item>
                    <el-form-item label="机器人名称" prop="robotName">
                        <el-input
                        v-model="queryParams.robotName"
                        placeholder="请输入机器人名称"
                        clearable
                        style="width: 240px"
                        @keyup.enter="handleQuery"
                        />
                    </el-form-item>
                    <el-form-item label="识别类型" prop="idenType">
                        <el-select v-model="queryParams.idenType" placeholder="请选择识别类型" clearable style="width: 240px">
                        <el-option
                            v-for="dict in iden_type"
                            :key="dict.value"
                            :label="dict.label"
                            :value="dict.value"
                        />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="确认状态" prop="confirmStatus">
                      <el-select v-model="queryParams.confirmStatus" placeholder="请选择确认状态" clearable style="width: 240px">
                        <el-option
                            v-for="dict in confirm_status"
                            :key="dict.value"
                            :label="dict.label"
                            :value="dict.value"
                        />
                      </el-select>
                    </el-form-item>
                    <el-form-item label="报警等级" prop="alarmLevel">
                        <el-select v-model="queryParams.alarmLevel" placeholder="请选择报警等级" clearable style="width: 240px">
                        <el-option
                            v-for="dict in rule_result"
                            :key="dict.value"
                            :label="dict.label"
                            :value="dict.value"
                        />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="报警时间" style="width: 438px;">
                        <el-date-picker
                        v-model="dateRange"
                        value-format="YYYY-MM-DD HH:mm:ss"
                        type="datetimerange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        ></el-date-picker>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-form>


                <el-table v-loading="loading" :data="dataList">
                    <el-table-column label="所属部门" align="center" prop="deptName" />
                    <el-table-column label="报警名称" align="center" prop="alarmName" :show-overflow-tooltip="true" />
                    <el-table-column label="机器人名称" align="center" prop="robotName" :show-overflow-tooltip="true" />
                    <el-table-column label="识别类型" align="center" prop="idenType">
                        <template #default="scope">
                        <dict-tag :options="iden_type" :value="scope.row.idenType" />
                        </template>
                    </el-table-column>
                    <el-table-column label="标记" align="center" prop="signType">
                        <template #default="scope">
                        <dict-tag :options="sign_type" :value="scope.row.signType" />
                        </template>
                    </el-table-column>
                    <el-table-column label="识别结果" align="center" prop="idenResult" :show-overflow-tooltip="true" />
                    <el-table-column label="报警等级" align="center" prop="alarmLevel">
                        <template #default="scope">
                          <div class="pointBall" :style="{background: typeColor[scope.row.alarmLevel]}"></div>
                          <dict-tag :options="rule_result" :value="scope.row.alarmLevel" />
                        </template>
                    </el-table-column>
                    <el-table-column label="报警时间" align="center" prop="triggerTime" width="180">
                        <template #default="scope">
                        <span>{{ parseTime(scope.row.triggerTime) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="确认状态" align="center" prop="confirmStatus">
                        <template #default="scope">
                        <dict-tag :options="confirm_status" :value="scope.row.confirmStatus" />
                        </template>
                    </el-table-column>

                    <el-table-column label="操作" align="center" width="150" class-name="small-padding fixed-width">
                        <template #default="scope">
                        <el-button size="mini" type="text" icon="Select" v-if="scope.row.confirmStatus=='0'" @click="handleConfirm(scope.row)" >确认</el-button>
                        <el-button size="mini" type="text" icon="Search" @click="handleDetail(scope.row)"  >查看详情</el-button>
                        </template>
                    </el-table-column>
                </el-table>

                <pagination
                    v-show="total > 0"
                    :total="total"
                    v-model:page="queryParams.pageNum"
                    v-model:limit="queryParams.pageSize"
                    @pagination="getList"
                />

            </el-col>
        </el-row>
      


    <el-dialog title="确认" v-model="confirmOpen" width="600px" append-to-body>
      <el-form :model="confirmForm" ref="confirmRef" label-width="120px">
        <el-row>
            <el-col :span="24">
                <el-form-item label="报警名称">
                    <el-input v-model="confirmForm.alarmName"  placeholder="请输入内容" disabled></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="24">
                <el-form-item label="机器人名称">
                    <el-input v-model="confirmForm.robotName"  placeholder="请输入内容" disabled></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="24">
                <el-form-item label="识别结果">
                    <el-input v-model="confirmForm.idenResult"  placeholder="请输入内容" disabled></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="24">
                <el-form-item label="报警时间">
                    <el-input v-model="confirmForm.triggerTime"  placeholder="请输入内容" disabled></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="24">
                <el-form-item label="备注" prop="confirmContent">
                    <el-input v-model="confirmForm.confirmContent" type="textarea" placeholder="请输入内容"></el-input>
                </el-form-item>
            </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitConfirmForm">确 定</el-button>
          <el-button @click="confirmCancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

      <el-dialog title="报警详情" v-model="open" width="600px" append-to-body>
         <el-descriptions class="margin-top" title="" :column="2" border>
            <el-descriptions-item label="报警名称">{{alarmInfo.alarmName}}</el-descriptions-item>
            <el-descriptions-item label="报警等级"><dict-tag :options="rule_result" :value="alarmInfo.alarmLevel"/></el-descriptions-item>

            <el-descriptions-item label="识别类型"><dict-tag :options="iden_type" :value="alarmInfo.idenType"/></el-descriptions-item>
            <el-descriptions-item label="机器人名称">{{alarmInfo.robotName}}</el-descriptions-item>

            <el-descriptions-item label="所属部门">{{alarmInfo.deptName}}</el-descriptions-item>
            <el-descriptions-item label="标记"><dict-tag :options="sign_type" :value="alarmInfo.signType"/></el-descriptions-item>

            <el-descriptions-item label="报警时间">{{parseTime(alarmInfo.triggerTime)}}</el-descriptions-item>
            <el-descriptions-item label="确认状态"><dict-tag :options="confirm_status" :value="alarmInfo.confirmStatus"/></el-descriptions-item>

            <el-descriptions-item label="确认人">{{alarmInfo.confirmPersonName}}</el-descriptions-item>
            <el-descriptions-item label="确认时间">{{parseTime(alarmInfo.confirmTime)}}</el-descriptions-item>

            <el-descriptions-item label="备注">{{alarmInfo.confirmContent}}</el-descriptions-item>

            <el-descriptions-item label="识别图片">
                  <el-image style="width: 100px; height: 100px" :src="imageUrl" :preview-src-list="imageUrlList"/>
            </el-descriptions-item>
         </el-descriptions>

         <template #footer>
         <div class="dialog-footer">
            <el-button @click="cancel">关 闭</el-button>
         </div>
         </template>
      </el-dialog>

   </div>
</template>

<script setup name="AlarmAi">


import { listPageList , confirmAlarm} from "@/api/alarm/alarmAi"
import leftTree from "@/views/datacenter/tree/index";
import {dataCenterTreeStore} from '@/store/modules/DataCenterTree.js'



const { proxy } = getCurrentInstance()
const globalStore = dataCenterTreeStore();

const { sys_yes_no, rule_result, iden_type, sign_type ,confirm_status} = proxy.useDict("sys_yes_no", "rule_result", "iden_type", "sign_type", "confirm_status")

const dataList = ref([])
const loading = ref(false)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const dateRange = ref([])


const open = ref(false)
const alarmInfo = ref({})
const url= import.meta.env.VITE_APP_BASE_API;

const imageUrl = ref(null)
const imageUrlList = ref([])


const confirmOpen = ref(false)
const typeColor = ref(["", "#ff0000","#ff722b","#ffcd00", "#67C23A","#409EFF","#909399"])


const data = reactive({
  confirmForm: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    deptId: '', 
    toolGroupId: '',
    deptName: undefined,
    alarmName: undefined,
    robotName: undefined,
    idenType: undefined,
    alarmLevel: undefined
  },
  confirmRules: {
    confirmContent: [{ required: true, message: "备注不能为空", trigger: "blur" }]
  }
})

const { queryParams, confirmForm, confirmRules} = toRefs(data)


function handleDetail(row){

    open.value = true;
    alarmInfo.value = row;
    if(row.mediaFileList.length > 0){
        imageUrl.value = url + row.mediaFileList[0].thumbnailUrl;
    }

    var imageList = [];
    row.mediaFileList.forEach(element => {
        imageList.push(url + element.thumbnailUrl)
    });
    imageUrlList.value = imageList;

}

function cancel(){
    open.value = false;
    alarmInfo.value = {};

    imageUrl.value = null;
    imageUrlList.value = [];

}


function handleConfirm(row){

    confirmOpen.value = true;
    confirmForm.value.id = row.id;
    confirmForm.value.confirmContent = undefined;

    confirmForm.value.alarmName = row.alarmName;
    confirmForm.value.robotName = row.robotName;
    confirmForm.value.idenResult = row.idenResult;   
    confirmForm.value.triggerTime = row.triggerTime;
}

function confirmCancel(){
    confirmOpen.value = false;
    confirmForm.value = {
        id: undefined,
        confirmContent: undefined
    }
    proxy.resetForm("confirmRef")
}

/** 查询智能报警列表 */
function getList() {
  if (queryParams.value.deptId === '' && queryParams.value.toolGroupId === '') {
    proxy.$modal.msgError("请选择左侧部门信息！");
    return;
  }
  loading.value = true
  if(dateRange.value.length > 0){
    queryParams.value.beginTime = dateRange.value[0]
    queryParams.value.endTime = dateRange.value[1]
  }
  listPageList(queryParams.value).then(response => {
    dataList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}


/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = []
  queryParams.value.beginTime = null;
  queryParams.value.endTime = null;
  proxy.resetForm("queryRef")
  handleQuery()
}


/** 提交按钮 */
function submitConfirmForm() {
  proxy.$refs["confirmRef"].validate(valid => {
    if (valid) {
        confirmAlarm(confirmForm.value).then(response => {
            proxy.$modal.msgSuccess("修改成功")
            confirmOpen.value = false
            getList()
        })
    }
  })
}

//监听树点击
watch(() => globalStore.treeId, (newValue) => {
  queryParams.value.treeId = newValue;
  let treeNode = globalStore.treeNode;
  if (treeNode.nodeType == '0') {
    queryParams.value.toolGroupId = '';
    queryParams.value.deptId = treeNode.id;
      getList()
  }else {
    dataList.value = [];
    total.value = 0;
  }
})

//进去页面加载
onMounted(() => {
  let treeNode = globalStore.treeNode;
  if (Object.keys(treeNode).length != 0) {
    if (treeNode.nodeType == '0') {
      queryParams.value.toolGroupId = '';
      queryParams.value.deptId = treeNode.id;
      getList()
    }else {
      dataList.value = [];
      total.value = 0;
    }
  }
})

</script>
<style scoped>
  .pointBall{
    width: 14px;
    height: 14px;
    border-radius: 20px;
    margin-left: -50px;
    display: inline-block;
    position: absolute;
    margin-top: 4px;
  }
</style>