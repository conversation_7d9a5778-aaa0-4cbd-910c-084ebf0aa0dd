<template>
  <div class="app-container">
    <el-row :gutter="20">
      <splitpanes :horizontal="appStore.device === 'mobile'" class="default-theme">
        <!--识别模型数据-->
        <pane size="16">
          <el-col>
            <div class="head-container">
              <el-input v-model="idenModelName" placeholder="请输入点位模型名称" clearable prefix-icon="Search" style="margin-bottom: 20px" />
            </div>
            <div class="head-container">
              <el-tree :data="idenModelOptions" 
              :props="{ label: 'idenModelName', children: 'children' }" 
              :expand-on-click-node="false" 
              :filter-node-method="filterNode" ref="idenModelTreeRef" node-key="id" highlight-current default-expand-all @node-click="handleNodeClick"  @node-contextmenu="floderOption"/>
            </div>
          </el-col>
        </pane>
        <pane size="14" >

            <div class="demo-image__error">
              <div class="block">
                <el-image v-if="currentIdenModelId != 0" :src="url + currentImageUrl" :preview-src-list="currentImageUrl!=null && currentImageUrl!='' ? [url+currentImageUrl] : []">
                  <template #error>
                    <div class="image-slot">
                      <el-icon><icon-picture /></el-icon>
                      <p style="font-size: 14px;">暂无上传图例</p>
                    </div>
                  </template>
                </el-image>
                <p v-if="currentIdenModelId != 0" style="font-size: 14px;">参考图例</p>
              </div>

              <div  v-if="currentIdenModelId != 0">
                <el-row :gutter="10" class="mb8">
                    <el-col :span="1.5">
                        <el-button type="primary" plain icon="Plus" @click="handleAddIdenType">新增识别类型</el-button>
                    </el-col>
                </el-row>

                <el-table :data="idenTypeDataList">
                    <el-table-column label="识别类型" align="center" key="idenTypeName" prop="idenTypeName"/>
                    <el-table-column label="数量" align="center" key="idenTypeNum" prop="idenTypeNum"/>
                    <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                        <template #default="scope">
                        <el-tooltip content="删除" placement="top" v-if="scope.row.userId !== 1">
                            <el-button link type="primary" icon="Delete" @click="handleDeleteIdenType(scope.row)"></el-button>
                        </el-tooltip>
                        </template>
                    </el-table-column>
                </el-table>

              </div>
            </div>

        </pane>
        <!--识别模型参数数据-->
        <pane size="70">
          <el-col v-if="currentIdenModelId != 0">
            <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
              <el-form-item label="参数名称" prop="paramName">
                <el-input v-model="queryParams.paramName" placeholder="请输入参数名称" clearable style="width: 240px" @keyup.enter="handleQuery" />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>&emsp;
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>

            <el-row :gutter="10" class="mb8">
              <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate">修改</el-button>
              </el-col>
              <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
            </el-row>

            <el-table v-loading="loading" :data="paramList" @selection-change="handleSelectionChange">
              <el-table-column type="selection" width="50" align="center" />
              <el-table-column label="参数名称" align="center" key="paramName" prop="paramName"  v-if="columns[0].visible"/>
              <el-table-column label="识别名称" align="center" key="idenTypeName" prop="idenTypeName"  v-if="columns[1].visible"/>
              <el-table-column label="识别类型" align="center" key="idenName" prop="idenName"  v-if="columns[2].visible"/>
              <el-table-column label="数据类型" align="center" key="dataType" prop="dataType" v-if="columns[3].visible">
                <template #default="scope">
                  <dict-tag :options="data_type" :value="scope.row.dataType" />
                </template>
              </el-table-column>
              <el-table-column label="索引" align="center" key="seq" prop="seq"  v-if="columns[4].visible"/>
              <el-table-column label="单位" align="center" key="unit" prop="unit"  v-if="columns[5].visible"/>
              <el-table-column label="偏移量" align="center" key="offsetValue" prop="offsetValue"  v-if="columns[6].visible"/>
              <el-table-column label="小数点" align="center" key="precisionNum" prop="precisionNum"  v-if="columns[7].visible"/>
              <el-table-column label="缩放位" align="center" key="scaling" prop="scaling"  v-if="columns[8].visible"/>
              <el-table-column label="量程（最小）" align="center" key="minLimit" prop="minLimit"  v-if="columns[9].visible"/>
              <el-table-column label="量程（最大）" align="center" key="maxLimit" prop="maxLimit"  v-if="columns[10].visible"/>
              <el-table-column label="操作" align="center" width="150" class-name="small-padding fixed-width">
                <template #default="scope">
                  <el-tooltip content="修改" placement="top">
                    <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"></el-button>
                  </el-tooltip>
                </template>
              </el-table-column>
            </el-table>
            <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
          </el-col>
        </pane>
      </splitpanes>
    </el-row>

    <!-- 添加或修改参数配置对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form :model="form" :rules="rules" ref="paramRef" label-width="120px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="参数名称" prop="paramName">
              <el-input v-model="form.paramName" placeholder="请输入参数名称" maxlength="200" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="识别名称" prop="idenTypeName">
              <el-input v-model="form.idenTypeName" placeholder="请输入识别名称" maxlength="200" />
            </el-form-item>
          </el-col>
        </el-row>


        <el-row>
          <el-col :span="24">
            <el-form-item label="索引" prop="seq">
              <el-input v-model="form.seq" placeholder="请输入索引" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="单位" prop="unit">
              <el-input v-model="form.unit" placeholder="请输入单位"  />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="form.dataType == 1">
          <el-col :span="24">
            <el-form-item label="偏移量" prop="offsetValue">
              <el-input v-model="form.offsetValue" placeholder="请输入偏移量" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="form.dataType == 1">
          <el-col :span="24">
            <el-form-item label="小数点" prop="precisionNum">
              <el-input v-model="form.precisionNum" placeholder="请输入小数点"  />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="form.dataType == 1">
          <el-col :span="24">
            <el-form-item label="缩放位" prop="scaling">
              <el-input v-model="form.scaling" placeholder="请输入缩放位"  />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="form.dataType == 1">
          <el-col :span="24">
            <el-form-item label="里程（最小）" prop="minLimit">
              <el-input v-model="form.minLimit" placeholder="请输入里程（最小）" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="form.dataType == 1">
          <el-col :span="24">
            <el-form-item label="里程（最大）" prop="maxLimit">
              <el-input v-model="form.maxLimit" placeholder="请输入里程（最大）"  />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="备注">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入内容"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>



    <!-- 添加或修改识别模型配置对话框 -->
    <el-dialog :title="idenModelTitle" v-model="idenModelOpen" width="600px" append-to-body>
      <el-form :model="idenModelForm" :rules="idenModelRules" label-width="120px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="点位模型名称" prop="idenModelName">
              <el-input v-model="idenModelForm.idenModelName" placeholder="请输入点位模型名称" maxlength="200" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="点位模型图标">
              <image-upload v-model="idenModelForm.samplePhotoUrl" limit="1"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="备注">
              <el-input v-model="idenModelForm.remark" type="textarea" placeholder="请输入内容"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitIdenModelForm">确 定</el-button>
          <el-button @click="idenModelCancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 添加或修改识别模型配置对话框 -->
    <el-dialog :title="idenTypeTitle" v-model="idenTypeOpen" width="600px" append-to-body>
      <el-form :model="idenTypeForm" :rules="idenTypeFormRules"  ref="idenTypeRef" label-width="120px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="识别类型" prop="idenTypeId">
              <el-select v-model="idenTypeForm.idenTypeId" clearable filterable placeholder="请选择识别类型">
                <el-option v-for="item in idenTypeList" :key="item.id" :label="item.idenTypeName" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="数量" prop="idenTypeNum">
              <el-input-number v-model="idenTypeForm.idenTypeNum" :min="1" :max="9" />
              <el-tooltip content="数量范围1-9" placement="top" style="margin-left:5px">
                <el-icon><WarningFilled /></el-icon>
              </el-tooltip>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitIdenTypeForm">确 定</el-button>
          <el-button @click="idenTypeCancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>


    <div :style="{'z-index': 9999, position: 'fixed',left: optionCardX + 'px',
            top: optionCardY + 'px', width: '100px', background: 'white',
            'box-shadow': '0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04)'}" v-show="optionCardShow"
      id="option-button-group">
      <el-button @click="handleCreateIdenModel" icon="Plus">创建点位模型
      </el-button>
      <el-button @click="closeOptionCard" icon="Close">关闭当前窗口
      </el-button>
    </div>

    <div :style="{'z-index': 9999, position: 'fixed',left: optionCardX + 'px',
            top: optionCardY + 'px', width: '100px', background: 'white',
            'box-shadow': '0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04)'}" v-show="idenModelCardShow"
      id="option-button-group1">
      <el-button @click="handleUpdateIdenModel" icon="Edit">编辑点位模型
      </el-button>
      <el-button @click="handleDeleteIdenModel" icon="Delete">删除点位模型
      </el-button>
      <el-button @click="closeOptionCard" icon="Close">关闭当前窗口
      </el-button>
    </div>

  </div>
</template>

<script setup name="IdenModel">
import { getToken } from "@/utils/auth"
import useAppStore from '@/store/modules/app'

import { listAllIdenType } from "@/api/operation/idenType"
import { idenModelTreeSelect, getIdenModel, addIdenModel, updateIdenModel, deleteIdenModel } from "@/api/operation/idenModel"
import { listParam, changeParamStatus, getParam, addParam, updateParam, delParam } from "@/api/operation/idenModelParam"
import { listIdenModelParamGroup, addIdenModelParamGroup, delIdenModelParamGroup } from "@/api/operation/idenModelParamGroup"

import { Picture as IconPicture } from '@element-plus/icons-vue'


import { Splitpanes, Pane } from "splitpanes"
import "splitpanes/dist/splitpanes.css"

const router = useRouter()
const appStore = useAppStore()
const { proxy } = getCurrentInstance()
const { sys_normal_disable, sys_yes_no, data_type } = proxy.useDict("sys_normal_disable", "sys_yes_no" , "data_type")

const paramList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")
const idenModelName = ref("")
const idenModelOptions = ref(undefined)

const url= import.meta.env.VITE_APP_BASE_API;

const idenTypeList = ref([])
const idenTypeDataList = ref([])


//当前选中左侧树(识别模型)id
const currentIdenModelId = ref(0)
const currentImageUrl = ref("")


const idenModelTitle = ref("")
const idenModelOpen = ref(false)

const idenTypeTitle = ref("")
const idenTypeOpen = ref(false)


const optionCardShow = ref(false)
const idenModelCardShow = ref(false)
const optionCardX = ref("")
const optionCardY = ref("")
const optionData = ref("")

// 列显隐信息
const columns = ref([
  { key: 0, label: `参数名称`, visible: true },
  { key: 1, label: `识别名称`, visible: true },
  { key: 2, label: `识别类型`, visible: true },
  { key: 3, label: `数据类型`, visible: true },
  { key: 4, label: `索引`, visible: true },
  { key: 5, label: `单位`, visible: true },
  { key: 6, label: `偏移量`, visible: true },
  { key: 7, label: `小数点`, visible: true },
  { key: 8, label: `缩放位`, visible: true },
  { key: 9, label: `量程（最小）`, visible: true },
  { key: 10, label: `量程（最大）`, visible: true }
])

const data = reactive({
  form: {},
  idenModelForm: {},
  idenTypeForm: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    paramName: undefined,
    idenModelId: undefined
  },
  rules: {
    paramName: [{ required: true, message: "参数名称不能为空", trigger: "blur" }, { min: 2, max: 200, message: "参数名称长度必须介于 2 和 20 之间", trigger: "blur" }],
    idenTypeName: [{ required: true, message: "识别类型不能为空", trigger: "blur" }],
    seq: [ {pattern:/^[0-9]*$/, message: "索引必须输入合法的数字", trigger: "blur" }],
    offsetValue: [ {pattern: /(^[\-0-9][0-9]*(\.[0-9]+)?)$/, message: "偏移量必须输入合法的数字", trigger: "blur" }],
    precisionNum: [ {pattern: /^[0-9]*$/, message: "小数点必须输入合法的数字", trigger: "blur" }],
    scaling: [ {pattern: /(^[\-0-9][0-9]*(\.[0-9]+)?)$/, message: "缩放位必须输入合法的数字", trigger: "blur" }],
    minLimit: [ {pattern: /(^[\-0-9][0-9]*(\.[0-9]+)?)$/, message: "里程（最小）必须输入合法的数字", trigger: "blur" }],
    maxLimit: [ {pattern: /(^[\-0-9][0-9]*(\.[0-9]+)?)$/, message: "里程（最大）必须输入合法的数字", trigger: "blur" }]
  }
  ,
  idenTypeFormRules: {
    idenTypeId: [{ required: true, message: "识别类型不能为空", trigger: "change" }],
    idenTypeNum: [{ required: true, message: "数量不能为空", trigger: "blur" }, {pattern: /^[1-9]d*$/, message: "数量必须输入合法的数字", trigger: "blur" }],
  }
  ,
  idenModelRules: {
    idenModelName: [{ required: true, message: "点位模型名称不能为空", trigger: "blur" }, { min: 1, max: 200, message: "点位模型名称长度必须介于 1 和 20 之间", trigger: "blur" }]
  }
})

const { queryParams, form, rules,idenTypeFormRules, idenModelForm, idenTypeForm, idenModelRules } = toRefs(data)


function floderOption(e, data, n, t){
  
  optionCardX.value = e.x + 30 // 让右键菜单出现在鼠标右键的位置
  optionCardY.value = e.y + 20
  optionData.value = data
  //this.node = n // 将当前节点保存
  //this.tree = t
  if (data.id == 0) {
    optionCardShow.value = true // 
    idenModelCardShow.value = false // 
  } else {
    optionCardShow.value = false // 
    idenModelCardShow.value = true // 
  }

}

function idenModelCancel(){
  idenModelOpen.value = false;
}

function closeOptionCard(){
  optionCardShow.value = false // 
  idenModelCardShow.value = false // 
}

//创建识别模型
function handleCreateIdenModel(){
  idenModelForm.value = {
    id: undefined,
    idenModelName: undefined,
    samplePhotoUrl: undefined,
    samplePhotoPhysical: undefined,
    remark: undefined
  }
  idenModelTitle.value = '新增点位模型';
  idenModelOpen.value = true;
  closeOptionCard()
}
function handleUpdateIdenModel(){

  getIdenModel(optionData.value.id).then(response => {
    idenModelForm.value = response.data
    idenModelTitle.value = '编辑点位模型';
    idenModelOpen.value = true;
    closeOptionCard()
  })

}
function handleDeleteIdenModel(){
  closeOptionCard()
  proxy.$modal.confirm('是否确认删除点位模型数据项？').then(function () {
    return deleteIdenModel(optionData.value.id)
  }).then(() => {
    getIdenModelTree()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
  
}


/** 通过条件过滤节点  */
const filterNode = (value, data) => {
  if (!value) return true
  return data.idenModelName.indexOf(value) !== -1
}

/** 根据名称筛选部门树 */
watch(idenModelName, val => {
  proxy.$refs["idenModelTreeRef"].filter(val)
})

/** 查询参数列表 */
function getList() {
  loading.value = true
  listParam(queryParams.value).then(res => {
    loading.value = false
    paramList.value = res.rows
    total.value = res.total
  })
}

/** 查询识别模型下拉树结构 */
function getIdenModelTree() {
  idenModelTreeSelect().then(response => {
    idenModelOptions.value = response.data

        //处理默认选择第一个节点
    if(response.data[0].children != null && response.data[0].children.length > 0 && currentIdenModelId.value == 0){
      var dataNode = response.data[0].children[0]
      handleNodeClick(dataNode)
    }else {
      if(response.data[0].children != null && response.data[0].children.length > 0){
        var dataNode = response.data[0].children.find(r => r.id == currentIdenModelId.value);
        if(dataNode == null){//如果等于空，相当与把当前点中的给删除了
          var dataNode = response.data[0].children[0]
          handleNodeClick(dataNode)
        }
      }
    }

  })
}


/** 节点单击事件 */
function handleNodeClick(data) {
  queryParams.value.idenModelId = data.id
  currentIdenModelId.value  = data.id;
  currentImageUrl.value = data.samplePhotoUrl;
  handleQuery()
  getIdenTypeDataList();
}



/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
//   queryParams.value.idenModelId = undefined
//   proxy.$refs.idenModelTreeRef.setCurrentKey(null)
  handleQuery()
}




/** 参数状态修改  */
function handleStatusChange(row) {
  let text = row.status === "0" ? "启用" : "停用"
  proxy.$modal.confirm('确认要"' + text + '""' + row.paramName + '"参数吗?').then(function () {
    return changeParamStatus(row.id, row.status)
  }).then(() => {
    proxy.$modal.msgSuccess(text + "成功")
  }).catch(function () {
    row.status = row.status === "0" ? "1" : "0"
  })
}




/** 选择条数  */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length != 1
  multiple.value = !selection.length
}


/** 重置操作表单 */
function reset() {
  form.value = {
    id: undefined,
    idenModelId: undefined,
    paramName: undefined,
    dataType: undefined,
    minValue: undefined,
    maxValue: undefined,
    enumValue: undefined,
    remark: undefined
  }
  proxy.resetForm("paramRef")
}

/** 取消按钮 */
function cancel() {
  open.value = false
  reset()
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const id = row.id || ids.value
  getParam(id).then(response => {
    form.value = response.data
    open.value = true
    title.value = "修改参数"
  })
}

/** 提交按钮 */
function submitForm() {

  if(currentIdenModelId.value == null || currentIdenModelId.value == 0 ){
    proxy.$modal.msgError("请选择点位模型");
    return false;
  }


  proxy.$refs["paramRef"].validate(valid => {
    if (valid) {
      form.value.idenModelId = currentIdenModelId;
      if (form.value.id != undefined) {
        updateParam(form.value).then(response => {
          proxy.$modal.msgSuccess("修改点位模型参数成功")
          open.value = false
          getList()
        })
      } else {
        addParam(form.value).then(response => {
          proxy.$modal.msgSuccess("新增点位模型参数成功")
          open.value = false
          getList()
        })
      }
    }
  })
}


function submitIdenModelForm(){

  if(idenModelForm.value.idenModelName == '' || idenModelForm.value.idenModelName == undefined){
    proxy.$modal.msgError("点位模型名称不能为空");
    return false;
  }

  if (idenModelForm.value.id != undefined) {
    updateIdenModel(idenModelForm.value).then(response => {
      proxy.$modal.msgSuccess("修改点位模型成功")
      idenModelOpen.value = false
      getIdenModelTree()
    })
  } else {
    addIdenModel(idenModelForm.value).then(response => {
      proxy.$modal.msgSuccess("新增点位模型成功")
      idenModelOpen.value = false
      getIdenModelTree()
    })
  }

}




function handleAddIdenType(){
    idenTypeTitle.value = '新增识别类型';
    idenTypeOpen.value = true;
    idenTypeForm.value = {
        id: undefined,
        idenTypeId: undefined,
        idenTypeNum: 1
    }
    proxy.resetForm("idenTypeRef")
}

function idenTypeCancel(){
    idenTypeOpen.value = false;
    idenTypeForm.value = {
        id: undefined,
        idenTypeId: undefined,
        idenTypeNum: 1
    }
    proxy.resetForm("idenTypeRef")
}




function getIdenTypeDataList() {
  listIdenModelParamGroup(queryParams.value).then(res => {
    idenTypeDataList.value = res.rows
  })
}

function submitIdenTypeForm(){

  proxy.$refs["idenTypeRef"].validate(valid => {
    if (valid) {
      idenTypeForm.value.idenModelId = currentIdenModelId;
      addIdenModelParamGroup(idenTypeForm.value).then(response => {
        proxy.$modal.msgSuccess("新增识别类型成功")
        idenTypeOpen.value = false
        getIdenTypeDataList()
        getList()
      })
    }
  })

}

function handleDeleteIdenType(row){
  const ids = row.id || ids.value
  proxy.$modal.confirm('是否确认删除识别类型数据项？').then(function () {
    return delIdenModelParamGroup(ids)
  }).then(() => {
    getIdenTypeDataList()
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

function getAllIdenType() {
  loading.value = true
  listAllIdenType().then(res => {
    idenTypeList.value = res.data;
  })
}

getIdenModelTree()
getList()
getAllIdenType()
</script>

<style scoped>
  ::v-deep .row-expand-cover .el-table__expand-icon {
    visibility: hidden;
  }
  .folder-box {
    height: 100%;
  }

  .option-card-button {
    width: 100%;
    margin-left: 0;
    font-size: 10px;
    border-radius: 0;
  }

  .el-button+.el-button {
    margin-left: 0px;
  }

  .el-tree-node:focus>.el-tree-node__content {
    background-color: #66b1ff87 !important;
  }

  .el-tree-node__content:hover {
    background-color: #66b1ff87;
  }

  .el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
    background-color: #66b1ff87;
  }

  .expand .el-table__expand-column .cell {
    display: none;
  }
</style>

<style scoped>
.demo-image__error .block {
  /* padding: 30px 0; */
  text-align: center;
  border-right: solid 1px var(--el-border-color);
  display: inline-block;
  width: 99%;
  box-sizing: border-box;
  vertical-align: top;
}
.demo-image__error .demonstration {
  display: block;
  color: var(--el-text-color-secondary);
  font-size: 14px;
  margin-bottom: 20px;
}
.demo-image__error .el-image {
  padding: 0 5px;
  max-width: 300px;
  max-height: 200px;
  width: 100%;
  height: 200px;
}

.demo-image__error .image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: var(--el-fill-color-light);
  color: var(--el-text-color-secondary);
  font-size: 30px;
}
.demo-image__error .image-slot .el-icon {
  font-size: 30px;
}
</style>