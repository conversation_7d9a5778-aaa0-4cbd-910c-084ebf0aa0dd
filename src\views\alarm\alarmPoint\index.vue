<template>
   <div class="app-container">
        <el-row :gutter="6">
            <el-col :span="24" >
                <el-form>
                    <el-form-item style="float:right" >
                        <el-radio-group v-model="alarmType" aria-label="label position" @change="alarmTypeChange">
                            <el-radio-button value="实时报警">实时报警</el-radio-button>
                            <el-radio-button value="历史报警">历史报警</el-radio-button>
                        </el-radio-group>
                    </el-form-item>
                </el-form>
            </el-col>

            <el-col :span="24" >
                <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="88px">
                    <el-form-item label="任务名称" prop="taskName">
                        <el-input
                        v-model="queryParams.taskName"
                        placeholder="请输入任务名称"
                        clearable
                        style="width: 240px"
                        @keyup.enter="handleQuery"
                        />
                    </el-form-item>
                    <el-form-item label="所属部门" prop="deptName">
                        <el-input
                        v-model="queryParams.deptName"
                        placeholder="请输入所属部门"
                        clearable
                        style="width: 240px"
                        @keyup.enter="handleQuery"
                        />
                    </el-form-item>
                    <el-form-item label="点位名称" prop="pointName">
                        <el-input
                        v-model="queryParams.pointName"
                        placeholder="请输入点位名称"
                        clearable
                        style="width: 240px"
                        @keyup.enter="handleQuery"
                        />
                    </el-form-item>
                    <el-form-item label="点位类型" prop="className">
                        <el-select v-model="queryParams.className" placeholder="请选择点位类型" clearable style="width: 240px">
                          <el-option label="识别" key="IdenPoint" value="IdenPoint"></el-option>
                          <el-option label="红外测温" key="InfraredDetection" value="InfraredDetection"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="点位模型" prop="idenModelName">
                        <el-input
                        v-model="queryParams.idenModelName"
                        placeholder="请输入点位模型"
                        clearable
                        style="width: 240px"
                        @keyup.enter="handleQuery"
                        />
                    </el-form-item>
                    <el-form-item label="机器人名称" prop="robotName">
                        <el-input
                        v-model="queryParams.robotName"
                        placeholder="请输入机器人名称"
                        clearable
                        style="width: 240px"
                        @keyup.enter="handleQuery"
                        />
                    </el-form-item>
                    <el-form-item label="报警等级" prop="ruleResultDict">
                        <el-select v-model="queryParams.ruleResultDict" placeholder="请选择报警等级" clearable style="width: 240px">
                          <el-option
                              v-for="dict in rule_result"
                              :key="dict.value"
                              :label="dict.label"
                              :value="dict.value"
                          />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="报警时间" style="width: 438px;">
                        <el-date-picker
                        v-model="dateRange"
                        value-format="YYYY-MM-DD HH:mm:ss"
                        type="datetimerange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        ></el-date-picker>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>






      <el-table v-loading="loading" :data="dataList">
         <el-table-column label="任务名称" align="center" prop="taskName" />
         <el-table-column label="所属部门" align="center" prop="deptName" />
         <el-table-column label="点位名称" align="center" prop="pointName" />
         <el-table-column label="点位类型" align="center" prop="className" >
            <template #default="scope">
               <dict-tag :options="point_class_name" :value="scope.row.className" />
            </template>
         </el-table-column>
         <el-table-column label="机器人名称" align="center" prop="robotName" />
         <el-table-column label="点位模型" align="center" prop="idenModelName" />
         <!-- <el-table-column label="识别结果" align="center" prop="ruleResult" /> -->
         <el-table-column label="报警等级" align="center" prop="ruleResultDict">
            <template #default="scope">
               <div class="pointBall" :style="{background: typeColor[scope.row.ruleResultDict]}"></div>
               <dict-tag :options="rule_result" :value="scope.row.ruleResultDict"/>
            </template>
         </el-table-column>
         <el-table-column label="报警时间" align="center" prop="idenTime" width="180">
            <template #default="scope">
               <span>{{ parseTime(scope.row.idenTime) }}</span>
            </template>
         </el-table-column>
         <el-table-column label="操作" align="center" width="150" class-name="small-padding fixed-width">
            <template #default="scope">
               <el-button size="mini" type="text" icon="Search" @click="handleDetail(scope.row)"  >查看详情</el-button>
            </template>
         </el-table-column>
      </el-table>

    <el-dialog title="报警详情" v-model="open" width="800px" append-to-body>
      <el-descriptions class="margin-top" title="" :column="2" border>
          <el-descriptions-item label="任务名称">{{alarmInfo.taskName}}</el-descriptions-item>
          <el-descriptions-item label="所属部门">{{alarmInfo.deptName}}</el-descriptions-item>

          <el-descriptions-item label="点位名称">{{alarmInfo.pointName}}</el-descriptions-item>
          <el-descriptions-item label="点位类型"><dict-tag :options="point_class_name" :value="alarmInfo.className"/></el-descriptions-item>

          <el-descriptions-item label="点位模型">{{alarmInfo.idenModelName}}</el-descriptions-item>
          <el-descriptions-item label="规则配置">{{alarmInfo.ruleExpressionText}}</el-descriptions-item>

          <el-descriptions-item label="识别结果">{{alarmInfo.idenRuleResult}}</el-descriptions-item>
          <el-descriptions-item label="报警等级"><dict-tag :options="rule_result" :value="alarmInfo.ruleResultDict"/></el-descriptions-item>

          <el-descriptions-item label="机器人名称">{{alarmInfo.robotName}}</el-descriptions-item>
          <el-descriptions-item label="报警时间">{{parseTime(alarmInfo.idenTime)}}</el-descriptions-item>

          <el-descriptions-item label="识别图片">
                <el-image style="width: 100px; height: 100px" :src="imageUrl" :preview-src-list="imageUrlList"/>
          </el-descriptions-item>
      </el-descriptions>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">关 闭</el-button>
        </div>
      </template>
    </el-dialog>

      <pagination
         v-show="total > 0"
         :total="total"
         v-model:page="queryParams.pageNum"
         v-model:limit="queryParams.pageSize"
         @pagination="getList"
      />


   </div>
</template>

<script setup name="AlarmEscort">


import { listAlarmPageList } from "@/api/alarm/alarmPoint"


const { proxy } = getCurrentInstance()
const { sys_yes_no, rule_result, iden_type, sign_type ,confirm_status, point_class_name} = proxy.useDict("sys_yes_no", "rule_result", "iden_type", "sign_type", "confirm_status", "point_class_name")

const dataList = ref([])
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const dateRange = ref([])

const open = ref(false)
const alarmInfo = ref({})
const url= import.meta.env.VITE_APP_BASE_API;

const imageUrl = ref(null)
const imageUrlList = ref([])


const alarmType = ref('实时报警')
const typeColor = ref(["", "#ff0000","#ff722b","#ffcd00", "#67C23A","#409EFF","#909399"])


const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    hisFlag: undefined,
    taskName: undefined,
    pointName: undefined,
    robotName: undefined,
    ruleResultDict: undefined,
    deptName: undefined,
    className: undefined,
    idenModelName: undefined
  }
})

const { queryParams} = toRefs(data)



function handleDetail(row){

    open.value = true;
    alarmInfo.value = row;
    if(row.mediaFileList.length > 0){
        imageUrl.value = url + row.mediaFileList[0].thumbnailUrl;
    }

    var imageList = [];
    row.mediaFileList.forEach(element => {
        imageList.push(url + element.thumbnailUrl)
    });
    imageUrlList.value = imageList;

}

function cancel(){
    open.value = false;
    alarmInfo.value = {};

    imageUrl.value = null;
    imageUrlList.value = [];

}

function alarmTypeChange(){
    getList();
}


/** 查询报警列表 */
function getList() {
  loading.value = true
  if(dateRange.value.length > 0){
    queryParams.value.beginTime = dateRange.value[0]
    queryParams.value.endTime = dateRange.value[1]
  }
  if(alarmType.value == '实时报警'){
    queryParams.value.hisFlag = '0';
  }else{
    queryParams.value.hisFlag = '1';
  }
  listAlarmPageList(queryParams.value).then(response => {
    dataList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}


/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = []
   queryParams.value.beginTime = null;
  queryParams.value.endTime = null;
  proxy.resetForm("queryRef")
  handleQuery()
}




getList()
</script>
<style scoped>
  .pointBall{
    width: 14px;
    height: 14px;
    border-radius: 20px;
    margin-left: -50px;
    display: inline-block;
    position: absolute;
    margin-top: 4px;
  }
</style>
