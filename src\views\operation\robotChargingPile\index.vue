<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="88px">
         <el-form-item label="充电站名称" prop="location">
            <el-input
               v-model="queryParams.location"
               placeholder="请输入充电站名称"
               clearable
               style="width: 240px"
               @keyup.enter="handleQuery"
            />
         </el-form-item>
         <el-form-item label="IP地址" prop="ip">
            <el-input
               v-model="queryParams.ip"
               placeholder="请输入IP"
               clearable
               style="width: 240px"
               @keyup.enter="handleQuery"
            />
         </el-form-item>
         <el-form-item label="状态" prop="status">
            <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 240px">
               <el-option
                  v-for="dict in robot_charging_pile_status"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
               />
            </el-select>
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
         </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
         <el-col :span="1.5">
            <el-button
               type="primary"
               plain
               icon="Plus"
               @click="handleAdd"
            >新增</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button
               type="success"
               plain
               icon="Edit"
               :disabled="single"
               @click="handleUpdate"
            >修改</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button
               type="danger"
               plain
               icon="Delete"
               :disabled="multiple"
               @click="handleDelete"
            >删除</el-button>
         </el-col>

         <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="robotChargingPileList" @selection-change="handleSelectionChange">
         <el-table-column type="selection" width="55" align="center" />
         <el-table-column label="充电站名称" align="center" prop="location" />
         <el-table-column label="IP" align="center" prop="ip" :show-overflow-tooltip="true" />
         <el-table-column label="端口" align="center" prop="port" :show-overflow-tooltip="true" />
         <el-table-column label="状态" align="center" prop="status">
            <template #default="scope">
               <dict-tag :options="robot_charging_pile_status" :value="scope.row.status" />
            </template>
         </el-table-column>
         <el-table-column label="备注" align="center" prop="remark" :show-overflow-tooltip="true" />
         <el-table-column label="创建时间" align="center" prop="createTime" width="180">
            <template #default="scope">
               <span>{{ parseTime(scope.row.createTime) }}</span>
            </template>
         </el-table-column>
         <el-table-column label="操作" align="center" width="150" class-name="small-padding fixed-width">
            <template #default="scope">
               <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" >修改</el-button>
               <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" >删除</el-button>
            </template>
         </el-table-column>
      </el-table>

      <pagination
         v-show="total > 0"
         :total="total"
         v-model:page="queryParams.pageNum"
         v-model:limit="queryParams.pageSize"
         @pagination="getList"
      />

      <!-- 添加或修改充电站配置对话框 -->
      <el-dialog :title="title" v-model="open" width="600px" append-to-body>
         <el-form ref="robotChargingPileRef" :model="form" :rules="rules" label-width="100px">
            <el-form-item label="充电站点位" prop="bizPointId">
              <el-select v-model="form.bizPointId" clearable filterable placeholder="请选择充电站点位" @change="bizPointSelectChange">
                <el-option v-for="item in bizPointList" :key="item.id" :label="item.instanceName" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="充电站名称" prop="location">
               <el-input v-model="form.location" placeholder="请输入充电站名称" />
            </el-form-item>
            <el-form-item label="IP地址" prop="ip">
               <el-input v-model="form.ip" placeholder="请输入IP地址" />
            </el-form-item>
            <el-form-item label="端口" prop="port">
               <el-input v-model="form.port" placeholder="请输入端口" />
            </el-form-item>
            <el-form-item label="备注" prop="remark">
               <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
            </el-form-item>
         </el-form>
         <template #footer>
            <div class="dialog-footer">
               <el-button type="primary" @click="submitForm">确 定</el-button>
               <el-button @click="cancel">取 消</el-button>
            </div>
         </template>
      </el-dialog>
   </div>
</template>

<script setup name="RobotChargingPile">

import { listRobotChargingPile, getRobotChargingPile, delRobotChargingPile, addRobotChargingPile, updateRobotChargingPile, listBizPoint } from "@/api/operation/robotChargingPile"


const { proxy } = getCurrentInstance()
const { sys_yes_no, robot_charging_pile_status } = proxy.useDict("sys_yes_no", "robot_charging_pile_status")

const robotChargingPileList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")
const dateRange = ref([])

const bizPointList = ref([])


const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    location: undefined,
    ip: undefined,
    status: undefined
  },
  rules: {
    bizPointId: [{ required: true, message: "充电站点位不能为空", trigger: "blur" }],
    location: [{ required: true, message: "充电站名称不能为空", trigger: "blur" }],   
    ip: [{ required: true, message: "ip不能为空", trigger: "blur" }],
    port: [{ required: true, message: "端口不能为空", trigger: "blur" },{pattern: /^[0-9]*$/, message: "端口必须输入合法的数字", trigger: "blur" }]
  }
})

const { queryParams, form, rules } = toRefs(data)


//查询所有机器人
function getBizPoint(){
  listBizPoint().then(res => {
    bizPointList.value = res.data;
  })
}

function bizPointSelectChange(value) {
    
    var bizPoint = bizPointList.value.find(item=> item.id == value)
    console.log(bizPoint)
    if(bizPoint != null){
        form.value.location = bizPoint.instanceName;
    }
}

/** 查询充电站列表 */
function getList() {
  loading.value = true
  listRobotChargingPile(proxy.addDateRange(queryParams.value, dateRange.value)).then(response => {
    robotChargingPileList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

/** 取消按钮 */
function cancel() {
  open.value = false
  reset()
}

/** 表单重置 */
function reset() {
  form.value = {
    id: undefined,
    bizPointId: undefined,
    location: undefined,
    ip: undefined,
    port: undefined,
    remark: undefined
  }
  proxy.resetForm("robotChargingPileRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = []
  proxy.resetForm("queryRef")
  handleQuery()
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加充电站"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const id = row.id || ids.value
  getRobotChargingPile(id).then(response => {
    form.value = response.data
    open.value = true
    title.value = "修改充电站"
  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["robotChargingPileRef"].validate(valid => {
    if (valid) {
      if (form.value.id != undefined) {
        updateRobotChargingPile(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        addRobotChargingPile(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const id = row.id || ids.value
  proxy.$modal.confirm('是否确认删除充电站数据项？').then(function () {
    return delRobotChargingPile(id)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

getBizPoint()
getList()
</script>
