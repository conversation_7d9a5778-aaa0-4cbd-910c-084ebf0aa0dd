import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/hyc";


// 查询任务列表
export function listTask(query) {
    return request({
      url: '/task/def/list',
      method: 'get',
      params: query
    })
}

// 查询任务详细
export function getTask(id) {
    return request({
      url: '/task/def/getById/' + parseStrEmpty(id),
      method: 'get'
    })
  }



// 新增任务
export function addTask(data) {
    return request({
      url: '/task/def/add',
      method: 'post',
      data: data
    })
  }
  
  // 修改任务
  export function updateTask(data) {
    return request({
      url: '/task/def/update',
      method: 'put',
      data: data
    })
  }

// 删除任务
export function delTask(id) {
    return request({
      url: '/task/def/delete/' + id,
      method: 'delete'
    })
  }

  export function listAllRobot() {
    return request({
      url: '/task/def/getAllRobot',
      method: 'get'
    })
  }
  
  export function pointTreeSelect(deptId) {
    return request({
      url: '/task/def/getPointTree/' + parseStrEmpty(deptId),
      method: 'get'
    })
  }


  
  export function listAllDept() {
    return request({
      url: '/task/def/getAllDept',
      method: 'get'
    })
  }
  
