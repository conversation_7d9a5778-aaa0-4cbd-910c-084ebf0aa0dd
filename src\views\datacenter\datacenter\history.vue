<template>
  <el-dialog v-model="dialogVisible" title="历史曲线" @opened="onDialogOpened" style="width: 1200px;">
    <el-row>
      <el-col :span="4">
        <el-card style="height: calc(100vh - 400px);">
          <template #header>
            <div class="card-header">
              <span>参数列表</span>
            </div>
          </template>
          <el-tree
              :data="paramData"
              @node-click="treeCLick"
          />
        </el-card>
      </el-col>
      <el-col :span="20" style="padding-left: 10px">
        <el-card>
          <el-form ref="queryForm" :inline="true">
            <el-form-item label="时间" prop="alias">
              <el-date-picker
                  v-model="times"
                  type="datetimerange"
                  range-separator="-"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间",
                  value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" :icon="Search" @click="search">搜索</el-button>
            </el-form-item>
          </el-form>
        </el-card>
        <el-card style="margin-top: 10px">
          <div id="historyChart" ref="historyChart" style="height: 420px; "/>
        </el-card>

      </el-col>
    </el-row>

  </el-dialog>
</template>


<script setup>
import {getIdenModelParam, getHistoryCurve} from '@/api/datacenter/datacenter'
import * as echarts from 'echarts'
import {Search} from "@element-plus/icons-vue";
import dayjs from "dayjs";

const {proxy} = getCurrentInstance()
const dialogVisible = ref(false);
const historyChart = ref(null)
const baseItem = ref([])
let chartInstance = null;
const paramData = ref([])
const times = ref([]);
const option = ref({
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  title: {
    text: '曲线图'
  },
  tooltip: {
    trigger: 'item',
    formatter: function (params) {

      let tooltipContent = '<div>';
      tooltipContent += params.marker;
      tooltipContent += '时间: ' + echarts.format.formatTime('yyyy-MM-dd hh:mm', params.value[0]) + '&nbsp;'; // 显示时间
      tooltipContent += '值: ' + params.value[1] + '<br>'; // 显示数值
      tooltipContent += '</div>';
      return tooltipContent;
    },

  },
  xAxis: {
    type: 'time',
    axisLabel: {
      show: true,    // 显示标签
      interval: 0,   // 每隔 5 个数据点显示一个标签
      rotate: 0,    // 标签旋转
      formatter: function (value) {
        return echarts.format.formatTime('MM-dd hh:mm', value); // 格式化时间
      }
    }
  },
  yAxis: {
    type: 'category',
    data: [], // Y轴的字符串分类数据
  },
  series: [],
  dataZoom: [
    {
      type: 'slider',  // 这是滑动条
      show: true,      // 显示滑动条
      xAxisIndex: [0], // 与 x 轴绑定
      start: 0,        // 初始滑动位置
      end: 100         // 滑动条的结束位置（百分比）
    }
  ]
})
const treeNode = ref({})

function history(item) {
  baseItem.value = item;
  treeNode.value = {};
  paramData.value = [];
  times.value = [dayjs(new Date()).format('YYYY-MM-DD') + " 00:00:00", dayjs(new Date(Date.parse(new Date()) + 86400000)).format('YYYY-MM-DD') + " 00:00:00"]
  getIdenModelParam({id: baseItem.value.id, idenModelId: baseItem.value.idenModelId}).then(res => {
    if (baseItem.value.className == 'InfraredDetection') {
      paramData.value.push({
        id: 9999,
        label: "红外测温",
        dataType: 1,
        valueScopeJson: []
      })
    } else {
      paramData.value = res.data.map(item => ({
        id: item.id,
        label: item.paramName,
        dataType: item.dataType,
        valueScopeJson: item.idenTypeParam == null || item.idenTypeParam.valueScopeJson == undefined ? [] : item.idenTypeParam.valueScopeJson
      }))
    }
    dialogVisible.value = true;
  })
}

function onDialogOpened() {
  if (chartInstance == null) {
    option.value.title.text = baseItem.value.instanceName
    chartInstance = echarts.init(historyChart.value)
    chartInstance.setOption(option.value)
    window.addEventListener('resize', chartInstance.resize)
  } else {
    resetCharts();
    chartInstance.setOption(option.value, true);
  }
}

function treeCLick(node) {
  treeNode.value = node;
  resetCharts();
  search();
}

function search() {
  if (treeNode.value.id == undefined) {
    proxy.$modal.msgError("请先选择参数");
    return;
  }
  if (times.value == null || times.value.length == 0) {
    proxy.$modal.msgError("请先选择查询时间");
    return;
  }

  let params = {
    bizPointId: baseItem.value.id,
    idenModelParamId: treeNode.value.id,
    className: baseItem.value.className,
    startTime: times.value[0],
    endTime: times.value[1]
  }

  console.log(params);

  getHistoryCurve(params).then(res => {

    let ser = {name: treeNode.value.label, type: 'line', data: []}
    if (treeNode.value.dataType == '2') {
      option.value.yAxis.type = 'category'
      if (treeNode.value.valueScopeJson != '') {
        option.value.yAxis.data = JSON.parse(treeNode.value.valueScopeJson)
      }
    } else {
      option.value.yAxis.type = 'value'
    }
    res.data.forEach(item => {
      ser.data.push([new Date(item.idenTime).getTime(), item.dataType == 1 ? item.floatValue : item.enumValue])
    })
    option.value.series.push(ser)
    chartInstance.setOption(option.value, true);
  })
}

function resetCharts() {
  option.value.title.text = baseItem.value.instanceName;
  option.value.yAxis.data = [];
  option.value.xAxis.data = [];
  option.value.series = [];
}

defineExpose({history})

</script>
