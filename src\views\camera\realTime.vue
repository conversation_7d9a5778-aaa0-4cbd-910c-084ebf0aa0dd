<template>
	<div class="app-container">
		<el-row>
			<el-col :span="6">
				<el-tree style="width: 100%; height: calc(100vh - 160px); overflow-y: auto;" :data="robotList" :props="props"
					show-checkbox @check-change="checkChange" default-expand-all />
			</el-col>
			<el-col :span="18">
				<div id="divPlugin" class="plugin"></div>
			</el-col>
		</el-row>
	</div>
</template>

<script>
	import robot from '@/api/robot/robot';

	export default {
		data() {
			return {
				g_iWndIndex: 0,
				robotList: [],
				props: {
					value: 'id',
					label: 'label',
					children: 'children',
				},
				loginip: null,
				port: null,
				username: null,
				password: null,
				channels: null,
				channelsList: [],
				ip: null,
				hardwareType: null,
				indexMap: {}
			}
		},
		created() {
			this.loadRobot()
		},
		mounted() {
			this.init()
			this.initIndexMap()
		},
		methods: {
			checkChange(data, checked) {
				console.log(data, checked)
				if (checked && data.hardwareType) {
					this.clickLogin(1, data)
				} else if (!checked && data.hardwareType) {
					// this.clickLogout(data.ip + '_' + data.port)
					for (var i = 0; i < 16; i++) {
						let indexMapInfo = this.indexMap[i]
						if (indexMapInfo != null) {
							if (data.hardwareType != '5' && indexMapInfo == (data.ip + '_' + data.port)) {
								this.clickStopRealPlay(i)
							} else if (indexMapInfo == (data.ip + '_' + data.port + '_（' + data.label.split('（')[1])) {
								this.clickStopRealPlay(i)
							}
						}
					}
				}
			},
			initIndexMap() {
				for (var i = 0; i < 16; i++) {
					this.indexMap[i] = null
				}
			},
			loadRobot() {
				robot.getAll().then(res => {
					res.data.map(item => {
						item.children = []
						item.label = item.robotName
						item.disabled = true
						item.robotHardwareList.map(item2 => {
							item2.label = item2.hardwareName
							if (item2.hardwareType == '1') {
								item.children.push(item2)
							} else if (item2.hardwareType == '5') {
								for (var i = 0; i < 2; i++) {
									let camera = JSON.parse(JSON.stringify(item2))
									if (i == 0) {
										camera.label += '（可见光）'
										item.children.push(camera)
									} else {
										camera.label += '（热成像）'
										item.children.push(camera)
									}
								}
							}
						})
					})
					console.log(res.data)
					this.robotList = res.data
				})
			},
			init() {
				let that = this
				// 初始化插件参数及插入插件
				WebVideoCtrl.I_InitPlugin({
					bWndFull: true,
					iWndowType: 1,
					cbSelWnd: function(xmlDoc) {
						// g_iWndIndex = parseInt($(xmlDoc).find("SelectWnd").eq(0).text(), 10);
						var szInfo = "当前选择的窗口编号：" + parseInt($(xmlDoc).find("SelectWnd").eq(0).text(), 10);
						console.log(szInfo);
					},
					cbDoubleClickWnd: function(iWndIndex, bFullScreen) {
						var szInfo = "当前放大的窗口编号：" + iWndIndex;
						if (!bFullScreen) {
							szInfo = "当前还原的窗口编号：" + iWndIndex;
						}
						console.log(szInfo);
					},
					cbInitPluginComplete: function() {
						WebVideoCtrl.I_InsertOBJECTPlugin("divPlugin").then(() => {
							// 检查插件是否最新
							WebVideoCtrl.I_CheckPluginVersion().then((bFlag) => {
								if (bFlag) {
									alert("检测到新的插件版本，请安装确认后下载的HCWebSDKPluginsUserSetup.exe");
								} else {
									that.changeWndNum(4)
								}

							});
						}, () => {
							alert("插件初始化失败，请确认是否已安装插件；如果未安装，请安装确认后下载的HCWebSDKPluginsUserSetup.exe");
							that.goOnLink()
						});
					}

				});

			},
			changeWndNum(iType) {
				if ("1*2" === iType || "2*1" === iType) {
					WebVideoCtrl.I_ArrangeWindow(iType).then(() => {
						console.log("窗口分割成功！");
					}, (oError) => {
						var szInfo = "窗口分割失败！";
						console.log(szInfo, oError.errorCode, oError.errorMsg);
					});
				} else {
					iType = parseInt(iType, 10);
					WebVideoCtrl.I_ChangeWndNum(iType).then(() => {
						console.log("窗口分割成功！");
					}, (oError) => {
						var szInfo = "窗口分割失败！";
						console.log(szInfo, oError.errorCode, oError.errorMsg);
					});
				}
			},
			goOnLink() {
				const a = document.createElement('a') // 创建一个<a></a>标签
				a.href = '/HCWebSDKPluginsUserSetup.exe' // 给a标签的href属性值加上地址
				a.download = 'HCWebSDKPluginsUserSetup.exe' // 设置下载文件文件名，这里加上.xlsx指定文件类型，pdf文件就指定.fpd即可
				a.style.display = 'none' // 障眼法藏起来a标签
				document.body.appendChild(a) // 将a标签追加到文档对象中
				a.click() // 模拟点击了a标签，会触发a标签的href的读取，浏览器就会自动下载了
				a.remove() // 一次性的，用完就删除a标签

			},
			clickLogout(val) {
				let that = this
				var szDeviceIdentify = val || this.ip

				if (null == szDeviceIdentify) {
					return;
				}

				WebVideoCtrl.I_Logout(szDeviceIdentify).then(() => {
					that.ip = null
					console.log(szDeviceIdentify + " " + "退出成功！");
				}, () => {
					console.log(szDeviceIdentify + " " + "退出失败！");
				});
			},
			clickLogin(szProtoType, data) {
				let that = this
				let szIP = data.ip
				let szPort = data.port
				let szUsername = data.account
				let szPassword = data.pwd

				if (!szIP || !szPort || !szUsername || !szPassword) {
					return this.$modal.msgWarning('摄像机配置信息不完整');
				}

				var szDeviceIdentify = szIP + "_" + szPort;

				WebVideoCtrl.I_Login(szIP, szProtoType, szPort, szUsername, szPassword, {
					success: function(xmlDoc) {
						console.log(szDeviceIdentify + " 登录成功！");
						// that.ip = szDeviceIdentify
						that.getChannelInfo(szDeviceIdentify, data);
					},
					error: function(oError) {
						if (2001 === oError.errorCode) {
							console.log(szDeviceIdentify + " 已登录过！");
							// that.ip = szDeviceIdentify
							that.getChannelInfo(szDeviceIdentify, data);
						} else {
							if (oError.errorCode === 401) {
								that.$modal.msgWarning(hardwareName + " 登录失败！");
							} else {
								console.log(szDeviceIdentify + " 登录失败！", oError.errorCode, oError.errorMsg);
							}
						}
					}
				});
			},
			getChannelInfo(szDeviceIdentify, data) {
				let that = this
				// let szDeviceIdentify = this.ip
				// let oSel = this.channels
				let channelsList = []

				// 模拟通道
				WebVideoCtrl.I_GetAnalogChannelInfo(szDeviceIdentify, {
					success: function(xmlDoc) {
						var oChannels = $(xmlDoc).find("VideoInputChannel");

						$.each(oChannels, function(i) {
							var id = $(this).find("id").eq(0).text(),
								name = $(this).find("name").eq(0).text();
							if ("" == name) {
								name = "Camera " + (i < 9 ? "0" + (i + 1) : (i + 1));
							}
							if (data.hardwareType == '5' && data.label.includes('（可见光）') && i == 0) {
								channelsList.push({
									id: id,
									name: name,
									label: '（可见光）'
								})
							} else if (data.hardwareType == '5' && data.label.includes('（热成像）') && i ==
								1) {
								channelsList.push({
									id: id,
									name: name,
									label: '（热成像）'
								})
							} else if (data.hardwareType == '1') {
								channelsList.push({
									id: id,
									name: name
								})
							}

						});
						// that.channels = that.channelsList[0].id
						console.log(szDeviceIdentify + " 获取模拟通道成功！");

						let num = 0
						for (var i = 0; i < 16; i++) {
							if (that.indexMap[i] == null) {
								num++;
							}
						}
						if (num == 0) {
							return that.$modal.msgWarning('预览播放窗口已满');
						} else if (channelsList.length > num) {
							return that.$modal.msgWarning('预览回放窗口不足');
						}

						channelsList.map((item, index) => {
							for (var i = 0; i < 16; i++) {
								if (that.indexMap[i] == null) {
									if (data.hardwareType != '5') {
										that.indexMap[i] = szDeviceIdentify;
									} else {
										that.indexMap[i] = szDeviceIdentify + '_' + item.label;
									}
									that.clickStartRealPlay(szDeviceIdentify, item.id, i)
									break
								}
							}

						})

					},
					error: function(oError) {
						console.log(szDeviceIdentify + " 获取模拟通道失败！", oError.errorCode, oError.errorMsg);
					}
				});

			},
			clickStartRealPlay(szDeviceIdentify, iChannelID, index) {
				let that = this
				console.log('g_iWndIndex：', index, ' | channelID：', iChannelID)

				let oWndInfo = WebVideoCtrl.I_GetWindowStatus(index)

				let startRealPlay = function() {
					WebVideoCtrl.I_StartRealPlay(szDeviceIdentify, {
						iChannelID: iChannelID,
						iWndIndex: index,
						iPort: 554,
						success: function() {
							console.log(szDeviceIdentify + " " + "开始预览成功！");
							// that.indexMap[index] = szDeviceIdentify
						},
						error: function(oError) {
							that.indexMap[index] = null
							console.log(szDeviceIdentify + " 开始预览失败！", oError.errorCode, oError
								.errorMsg);
						}
					});
				}

				console.log('oWndInfo：', oWndInfo)

				if (oWndInfo != null) {
					WebVideoCtrl.I_Stop({
						iWndIndex: index,
						success: function() {
							console.log("已经在播放了，停止预览成功")
							startRealPlay()
						},
						error: function(oError) {
							console.log("停止预览失败！", oError.errorCode, oError.errorMsg);
						}
					});
				} else {
					startRealPlay()
				}


			},
			clickStopRealPlay(index) {
				let that = this
				let szInfo = "";
				console.log('准备停止播放的窗口', index)
				WebVideoCtrl.I_Stop({
					iWndIndex: index,
					success: function() {
						szInfo = "停止预览成功！";
						console.log(szInfo);
						that.indexMap[index] = null
					},
					error: function(oError) {
						console.log("停止预览失败！", oError.errorCode, oError.errorMsg);
					}
				});
			},
		}
	}
</script>

<style scoped>
	.plugin {
		width: 100%;
		height: 750px;
	}
	/deep/.el-checkbox__input.is-disabled {
		display: none;
	}
	::-webkit-scrollbar {
	  width: 0;
	}
	::-webkit-scrollbar-track {
	  background: transparent; 
	}
</style>