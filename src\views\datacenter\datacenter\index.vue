<template>
  <div class="app-container" style="height: 100%">

  <el-row :gutter="20">
   <el-col :span="4" >
     <el-card style="height: calc(100vh - 160px);">
       <leftTree></leftTree>
     </el-card>
   </el-col>
    <el-col :span="20">
      <el-card>
        <el-form ref="queryForm" :inline="true" label-width="68px">
          <el-form-item label="点位名称" prop="alias">
            <el-input  placeholder="请输入点位名称" clearable v-model="queryParams.instanceName"/>
          </el-form-item>
          <el-form-item label="点位类型" prop="alias">
            <el-select style="width: 140px;"  placeholder="请选择点位类型" clearable v-model="queryParams.className">
              <el-option  key="IdenPoint" label="识别" value="IdenPoint"></el-option>
              <el-option  key="InfraredDetection" label="红外测温" value="InfraredDetection"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="点位状态" prop="alias">
            <el-select style="width: 140px;"  placeholder="请选择点位状态" clearable v-model="queryParams.taskInstanceNodeStatus">
              <el-option v-for="dict in task_instance_node_status" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="是否审核" prop="alias">
            <el-select style="width: 140px;"  placeholder="请选择审核状态" clearable v-model="queryParams.taskInstanceNodeAuditStatus">
              <el-option v-for="dict in audit_status" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" :icon="Search" @click="searchBizPointList">搜索</el-button>
            <el-button :icon="Refresh"  @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <el-card style="margin-top: 10px;">
        <div style="text-align: center; font-size: 14px;" v-if="bizPointTable.length==0">暂无数据</div>
        <div class="content_box" v-if="bizPointTable.length>0">
          <div class="content_table_item" v-for="item in bizPointTable">
            <el-card :body-style="{ padding: '0px !important' }" class="ft-card" :style="getStyle(item)">
              <div class="ft-head">
                <div class="ft-tag">
                  <span class="ml-title">{{ item.instanceName }}</span>
                  <el-tag class="ml-3" :type="'primary'" style="float: right;">{{elTagLabel(item)}}</el-tag>
                </div>
                <div class="ft-body">
                  <div class="ft-body-image">
                    <el-image style="height: 100%" src="/point.png" :initial-index="0" :zoom-rate="1.2" fit="fill"></el-image>
                  </div>
                  <div class="ft-body-item">
                    <div class="item-mb">点位类型：{{ item.className=='IdenPoint'?'识别':'红外测温' }}</div>
                    <div class="item-mb">采集时间： {{ item.taskInstanceNodeIdenTime }}</div>
                    <div class="item-mb">是否审核：  {{
                        item.taskInstanceNodeAuditStatus == null ? '' : item.taskInstanceNodeAuditStatus == '0' ? '待审核' : item.taskInstanceNodeAuditStatus == '1' ? '审核通过' : item.taskInstanceNodeAuditStatus == '2' ? '审核不通过' : ''
                      }}</div>
                  </div>
                </div>
              </div>
              <div class="ft-foot">
                <el-button :icon="Edit" size="small" type="primary" plain @click="approvalShow(item)">审核</el-button>
                <el-button :icon="TrendCharts" size="small" type="primary" plain @click="historyShow(item)">历史曲线</el-button>
                <el-button :icon="Histogram" size="small" type="primary" plain @click="historyTableShow(item)">历史数据</el-button>
              </div>
            </el-card>
          </div>
        </div>

      </el-card>
    </el-col>
  </el-row>
    <Approval ref="approvalRef"></Approval>
    <History ref="historyRef"></History>
    <HistoryTable ref="historyTableRef"></HistoryTable>
</div>
</template>

<script setup>
import leftTree from "@/views/datacenter/tree/index";
import {Edit, Histogram, Refresh, Search, TrendCharts} from "@element-plus/icons-vue";
import {getBizPointList} from '@/api/datacenter/datacenter'
import Approval from "@/views/datacenter/datacenter/approval";
import History from "@/views/datacenter/datacenter/history";
import HistoryTable from "@/views/datacenter/datacenter/historyTable";
import {dataCenterTreeStore} from '@/store/modules/DataCenterTree'
const {proxy} = getCurrentInstance()
const {task_instance_node_status} = proxy.useDict("task_instance_node_status")
const {audit_status} = proxy.useDict("audit_status")


const globalStore = dataCenterTreeStore();
const approvalRef = ref(null);
const historyRef = ref(null);
const historyTableRef = ref(null);
const bizPointTable = ref([]);
const queryParams = ref({deptId: '', toolGroupId: '', instanceName: '',taskInstanceNodeStatus:'',taskInstanceNodeAuditStatus:'',className:''})

function approvalShow(item) {
  approvalRef.value.approval(item);
}

function historyShow(item) {
  historyRef.value.history(item);
}

function historyTableShow(item) {
  historyTableRef.value.historyTable(item);
}

function resetSearch(){
  queryParams.value.instanceName= '';
  queryParams.value.taskInstanceNodeStatus='';
  queryParams.value.taskInstanceNodeAuditStatus='';
  queryParams.value.className='';
}

function searchBizPointList() {
  bizPointTable.value = [];
  if (queryParams.value.deptId === '' && queryParams.value.toolGroupId === '') {
    proxy.$modal.msgError("请选择左侧部门信息！");
    return;
  }
  getBizPointList(queryParams.value).then(res => {
    bizPointTable.value = res.data;
  })
}

function elTagLabel(item) {

  if (item.taskInstanceNodeStatus == null || item.taskInstanceNodeStatus == '') {
    return '待执行'
  } else if (item.taskInstanceNodeStatus == '0') {
    return '未抵达'
  } else if (item.taskInstanceNodeStatus == '1') {
    return '巡检正常'
  } else if (item.taskInstanceNodeStatus == '7') {
    return '识别异常'
  } else if (item.taskInstanceNodeStatus == '8') {
    return '不可达'
  } else if (item.taskInstanceNodeStatus == '901') {
    return '三级报警'
  } else if (item.taskInstanceNodeStatus == '902') {
    return '二级报警'
  } else if (item.taskInstanceNodeStatus == '903') {
    return '一级报警'
  } else {
    return '无';
  }

}

function getStyle(item) {

  if (item.taskInstanceNodeStatus == null || item.taskInstanceNodeStatus == '') {
    return 'background:linear-gradient(0deg,#e8f3ff 0%,rgba(255,255,255,0) 70%)'
  } else if (item.taskInstanceNodeStatus == '0') {
    return 'background:linear-gradient(0deg,#e8f3ff 0%,rgba(255,255,255,0) 70%)'
  } else if (item.taskInstanceNodeStatus == '1') {
    return 'background:linear-gradient(0deg,rgb(216.2, 246, 240) 0%,rgba(255,255,255,0) 70%)'
  } else if (item.taskInstanceNodeStatus == '7') {
    return 'background:linear-gradient(0deg,rgb(253, 225.6, 225.6) 0%,rgba(255,255,255,0) 70%)'
  } else if (item.taskInstanceNodeStatus == '8') {
    return 'background:linear-gradient(0deg,#e8f3ff 0%,rgba(255,255,255,0) 70%)'
  } else if (item.taskInstanceNodeStatus == '901') {
    return 'background:linear-gradient(0deg, #ffff99 0%,rgba(255,255,255,0) 70%)'
  } else if (item.taskInstanceNodeStatus == '902') {
    return 'background:linear-gradient(0deg, #e6a23c 0%,rgba(255,255,255,0) 70%)'
  } else if (item.taskInstanceNodeStatus == '903') {
    return 'background:linear-gradient(0deg,rgb(253, 225.6, 225.6) 0%,rgba(255,255,255,0) 70%)'
  } else {
    return '';
  }
}

//监听树点击
watch(() => globalStore.treeId, (newValue) => {
  queryParams.value.treeId = newValue;
  let treeNode = globalStore.treeNode;
  if (treeNode.nodeType == '1') {
    queryParams.value.toolGroupId = treeNode.id;
    queryParams.value.deptId = treeNode.parentId;
  } else {
    queryParams.value.toolGroupId = '';
    queryParams.value.deptId = treeNode.id;
  }
  searchBizPointList();
})

onMounted(() => {
  let treeNode = globalStore.treeNode;
  if (Object.keys(treeNode).length != 0) {
    if (treeNode.nodeType == '1') {
      queryParams.value.toolGroupId = treeNode.id;
      queryParams.value.deptId = treeNode.parentId;
    } else {
      queryParams.value.toolGroupId = '';
      queryParams.value.deptId = treeNode.id;
    }
    searchBizPointList();
  }
})

</script>

<style scoped>

.content_box {
  width: 100%;
  display: flex;
  flex-wrap: wrap;

  .content_table_item {
    position: relative;
    margin-left: 48px;
    margin-top: 10px;
    width: 400px;
    height: 210px;
    box-sizing: border-box;
    flex-direction: column;
  }

}

.el-card.ft-card {
  width: 100%;
  height: 100%;

  .ft-tag {
    padding: 10px;
    height: 40px;

    .ml-3 {
      margin-left: 6px;
    }

    .ml-title {
      font-size: 14px;
    }

    border-bottom: 1px solid var(--el-card-border-color);
  }

  .ft-head {
    width: 100%;
    height: 170px;
    border-bottom: 1px solid var(--el-card-border-color);
  }

  .ft-body {
    margin-top: 5px;
    width: 400px;
    height: 130px;
    padding: 10px;
    display: flex;
    justify-content: space-between;

    .ft-body-image {
      width: 40%;
      height: 100px;
      text-align: center;
      margin-right: 10px;
    }

    .ft-body-item {
      width: 60%;
    }

    .item-mb {
      width: 100%;
      margin-bottom: 8px;
      text-overflow: ellipsis;
      font-size: 14px;
    }
  }


  .ft-foot{
    margin-top: 5px;
    padding: 0 10px;
    height: 30px;
    line-height: 50px;
    display: flex;
    justify-content: space-between;

    .ft-item-name{
      font-size: 16px;
      font-weight: bold;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis
    }
  }
}


</style>