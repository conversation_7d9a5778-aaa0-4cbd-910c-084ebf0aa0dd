<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
         <el-form-item label="任务名称" prop="taskName">
            <el-input
               v-model="queryParams.taskName"
               placeholder="请输入任务名称"
               clearable
               style="width: 240px"
               @keyup.enter="handleQuery"
            />
         </el-form-item>
         <el-form-item label="计划规则" prop="planType">
            <el-select v-model="queryParams.planType" placeholder="请选择计划规则" clearable style="width: 240px">
               <el-option
                  v-for="dict in plan_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
               />
            </el-select>
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            <el-button type="success" icon="Plus" @click="handleAdd">新增</el-button>
         </el-form-item>
      </el-form>

      <!-- <el-row :gutter="10" class="mb8">
         <el-col :span="1.5">
            <el-button
               type="primary"
               plain
               icon="Plus"
               @click="handleAdd"
            >新增</el-button>
         </el-col>
         <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row> -->
      <el-row :gutter="20">
        <div class="content_box">
          <div class="content_table_item" v-for="taskDef in taskDefList" :key="taskDef.id">
            <el-card :body-style="{ padding: '0px !important' }" class="ft-card" :style="getStyle(taskDef.planType)" >
              <div class="ft-head">

                <div class="ft-tag">
                  <el-tooltip :content="taskDef.taskName" placement="top">
                    <el-link  @click="handleUpdate(taskDef, 1)" class="ellipsis-link" :underline="false">{{taskDef.taskName.length > 15 ? taskDef.taskName.slice(0, 15) + '...' : taskDef.taskName}}</el-link>
                  </el-tooltip>
                  <div style="float: right;">
                    <el-button type="primary" plain size="small">立即执行</el-button>
                    <el-button type="danger" plain size="small" @click="handleDelete(taskDef)">删除</el-button>
                  </div>
                </div>
                <div class="ft-body">
                  <div class="ft-body-image">
                    <el-image style="height: 100%" :src=" taskDef.planType == '1' ? '/task1.png' : '/task2.png'" :initial-index="0" :zoom-rate="1.2" fit="fill"></el-image>
                  </div>
                  <div class="ft-body-item">
                    <div class="item-mb">巡检点数： {{taskDef.pointNum}}</div>
                    <div class="item-mb">机器人数量：<el-link style="margin-top:-2px" type="primary" :underline="false" @click="handleUpdate(taskDef, 3)">{{taskDef.robotNum}}</el-link></div>
                    <div class="item-mb">管理部门： {{taskDef.deptName}}</div>
                    <div class="item-mb">计划规则： <el-link style="margin-top:-3px" type="primary" :underline="false" @click="handleUpdate(taskDef, 4)">{{taskDef.planType == '1' ? '周期计划' : '循环计划'}}</el-link></div>
                  </div>
                </div>
              </div>

            </el-card>
          </div>
        </div>
      </el-row>

      <!-- 添加或修改参数配置对话框 -->
      <el-dialog :title="title" v-model="open" width="800px" append-to-body>
        <el-steps style="max-width: 800px" :active="stepIndex" align-center finish-status="success">
            <el-step title="基础信息" description="" />
            <el-step title="选择点位" description="" />
            <el-step title="选择机器人" description="" />
            <el-step title="规则配置" description="" />
        </el-steps>
         <el-form ref="taskRef" :model="form" :rules="rules" label-width="80px" >
            <div v-if="stepIndex == 1" style="margin-top:30px">
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="任务名称" prop="taskName">
                        <el-input v-model="form.taskName" placeholder="请输入任务名称" maxlength="200" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="优先级" prop="priorty">
                        <el-input v-model="form.priorty" placeholder="请输入优先级" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="管理部门" prop="deptId">
                            <!-- <el-tree-select v-model="form.deptId" :data="enabledDeptOptions" :props="{ value: 'id', label: 'label', children: 'children' }" value-key="id" placeholder="请选择归属部门" check-strictly/> -->
                            <el-select v-model="form.deptId" clearable filterable placeholder="请选择部门">
                              <el-option v-for="item in bizDeviceTreeList" :key="item.id" :label="item.deviceName" :value="item.id"></el-option>
                            </el-select>
                        </el-form-item>
                       
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="24">
                        <el-form-item label="备注">
                        <el-input v-model="form.remark" type="textarea" placeholder="请输入内容"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </div>

            <div v-if="stepIndex == 2" class="tree-container"  style="margin-top:30px">
                <el-tree
                    class="tree-border"
                    :data="pointOptions"
                    show-checkbox
                    node-key="id"
                    :check-strictly="false"
                    empty-text="加载中，请稍候"
                    ref="tree"
                    :props="pointDefaultProps"
                    :default-expanded-keys="defaultExpandedKeys"
                    :default-checked-keys="selectedKeys"
                    @check-change="handleCheckChange"
                    style="height: 280px;overflow-y: auto;"
                ></el-tree>
 
            </div>

            <div v-if="stepIndex == 3"  style="margin-top:30px">
                <el-row :gutter="10" class="mb8">
                <el-col :span="1.5">
                    <el-button type="primary" plain icon="Plus" @click="handleAddRobot">添加机器人</el-button>
                </el-col>
                </el-row>
                <el-table :data="robotDataList">
                    <el-table-column label="机器人名称" align="center" key="robotName" prop="robotName"/>
                    <el-table-column label="主备类型" align="center" key="masterFlag" prop="masterFlag">
                        <template #default="scope">
                            <dict-tag :options="master_flag" :value="scope.row.masterFlag" />
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                        <template #default="scope">
                        <el-tooltip content="删除" placement="top">
                            <el-button link type="primary" icon="Delete" @click="handleDeleteRobot(scope.row)"></el-button>
                        </el-tooltip>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <div v-if="stepIndex == 4"  style="margin-top:30px">
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="计划规则" prop="planType">
                            <el-select v-model="form.planType" placeholder="请选择计划规则" clearable>
                                <el-option v-for="dict in plan_type" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24" v-if="form.planType == 0">
                        <el-form-item label="开始时间" prop="beginTime">
                            <el-date-picker v-model="form.beginTime" type="datetime" value-format="YYYY-MM-DD HH:mm:ss">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24" v-if="form.planType == 0">
                        <el-form-item label="结束时间" prop="endTime">
                            <el-date-picker v-model="form.endTime" type="datetime" value-format="YYYY-MM-DD HH:mm:ss">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <!-- <el-col :span="24" v-if="form.planType == 1">
                        <el-form-item label="周期类型" prop="cycleType">
                            <el-select v-model="form.cycleType" placeholder="请选择周期类型" clearable>
                                <el-option v-for="dict in cycle_type" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col> -->
                    <el-col :span="24" v-if="form.planType == 1">
                        <el-form-item label="执行时间" prop="cycleTrigger">
                            <el-time-picker v-model="form.cycleTrigger" type="datetime"  value-format="HH:mm" format="HH:mm">
                            </el-time-picker>
                            <el-tooltip content="执行时间，单位（每天），例如9:00，相当于每天上午9点执行" placement="top" style="margin-left:5px">
                            <el-icon><WarningFilled /></el-icon>
                          </el-tooltip>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24" v-if="form.planType == 1">
                        <el-form-item label="周期范围" prop="cycleScope">
                            <el-select v-model="form.cycleScope" placeholder="请选择周期范围" clearable>
                                <el-option v-for="dict in cycle_scope" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <!-- <el-col :span="24" v-if="form.planType == 1">
                      <el-form-item label="时间间隔" prop="cycleInterval">
                        <el-input-number v-model="form.cycleInterval" :min="1" :max="100" />
                        <el-tooltip content="时间间隔，单位（小时）" placement="top" style="margin-left:5px">
                          <el-icon><WarningFilled /></el-icon>
                        </el-tooltip>
                      </el-form-item>
                    </el-col> -->


                </el-row>
            </div>
         </el-form>
         <template #footer>
            <div class="dialog-footer">
                <el-button v-if="stepIndex != 1" @click="skipTo('pre')">上一步</el-button>&nbsp;
                <el-button v-if="stepIndex != 4" @click="skipTo('next')" >下一步</el-button>&nbsp;
                <el-button v-if="stepIndex == 4 || form.id" type="primary" @click="submitForm" >保 存</el-button>
            </div>
         </template>
      </el-dialog>

    <el-dialog title="添加机器人" v-model="robotOpen" width="600px" append-to-body>
      <el-form :model="robotForm" :rules="robotRules"  ref="robotRef" label-width="120px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="机器人" prop="robotId">
              <el-select v-model="robotForm.robotId" clearable filterable placeholder="请选择机器人">
                <el-option v-for="item in robotList" :key="item.id" :label="item.robotName" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="主备类型" prop="masterFlag">
              <el-select v-model="robotForm.masterFlag" placeholder="请选择主备类型" clearable>
                <el-option v-for="dict in master_flag" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitRobotForm">确 定</el-button>
          <el-button @click="robotCancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>



   </div>
</template>

<script setup name="TaskDef">
import { deptTreeSelect } from "@/api/system/user"
import { listTask, getTask, addTask, updateTask, delTask, listAllRobot, listAllDept, pointTreeSelect} from "@/api/operation/taskDef"

const { proxy } = getCurrentInstance()
const { sys_yes_no, plan_type ,master_flag, cycle_type, cycle_scope } = proxy.useDict("sys_yes_no", "plan_type", "master_flag", "cycle_type", "cycle_scope")

const taskDefList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")
const dateRange = ref([])

const stepIndex = ref(1)

const url= import.meta.env.VITE_APP_BASE_API;


// const deptOptions = ref(undefined)
// const enabledDeptOptions = ref(undefined)
const bizDeviceTreeList = ref([])



const tree = ref(null);

//点位树
const pointOptions = ref([])
//所有选中的节点
const selectedKeys = ref([])
//所有选中的点位
const selectedPoints = ref([])
//默认展开
const defaultExpandedKeys = ref([])

const pointDefaultProps= ref({
    children: "children",
    label: "name"
})



//机器人选择下拉
const robotList = ref([])
//机器人table列表
const robotDataList = ref([])
//添加机器人弹窗
const robotOpen = ref(false)



const data = reactive({
  form: {},
  robotForm: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    taskName: undefined,
    planType: undefined
  },
  rules: {
    taskName: [{ required: true, message: "任务名称不能为空", trigger: "blur" }],
    priorty: [{ required: true, message: "优先级不能为空", trigger: "blur" }, {pattern: /(^[\-0-9][0-9]*(\.[0-9]+)?)$/, message: "优先级必须输入合法的数字", trigger: "blur" }],
    deptId: [{ required: true, message: "管理部门不能为空", trigger: "blur" }],
    configValue: [{ required: true, message: "参数键值不能为空", trigger: "blur" }]
  },
  robotRules: {
    robotId: [{ required: true, message: "机器人不能为空", trigger: "blur" }],
    masterFlag: [{ required: true, message: "主备类型不能为空", trigger: "blur" }]
  }
})

const { queryParams, form, robotForm, rules, robotRules } = toRefs(data)


function getStyle(planType) {
   planType = Number(planType);
   if (planType ==0){
     return ' background:linear-gradient(0deg,rgb(197.7, 225.9, 255) 0%,rgba(255,255,255,0) 70%)';
   }
  if (planType == 1){
      return ' background:linear-gradient(0deg,rgb(209.4, 236.7, 195.9) 0%,rgba(255,255,255,0) 70%)';
  }
  // if (status==4||status==6){
  //   return ' background:linear-gradient(0deg,#e8f3ff 0%,rgba(255,255,255,0) 70%)';
  // }
  // if (status==701||status==702||status==703||status==9){
  //   return ' background:linear-gradient(0deg,rgb(253, 225.6, 225.6) 0%,rgba(255,255,255,0) 70%)';
  // }
}


function  skipTo(type) {
    if (type == "pre") {
        if (stepIndex.value == 1) return;
        stepIndex.value--;
    } else if (type == "next") {

        if(form.value.taskName == null || form.value.taskName == undefined ||  form.value.taskName == ''){
            proxy.$modal.msgError("请输入任务名称")
            return false;
        }
        if(form.value.priorty == null || form.value.priorty == undefined || form.value.taskName == ''){
            proxy.$modal.msgError("请输入优先级")
            return false;
        }

        const regex = /(^[\-0-9][0-9]*(\.[0-9]+)?)$/;
        if(!regex.test(form.value.priorty)){
            proxy.$modal.msgError("优先级须输入合法的数字");
            return false;
        }

        if(form.value.deptId == null || form.value.deptId == undefined){
            proxy.$modal.msgError("请选择管理部门")
            return false;
        }


        if(stepIndex.value == 1){
            getPointTree();
        }
        if(stepIndex.value == 2){

            if(selectedPoints.value.length == 0){
                proxy.$modal.msgError("请选择点位")
                return false;
            }
        }

        if(stepIndex.value == 3){
            if(robotDataList.value.length == 0){
                proxy.$modal.msgError("请选择机器人")
                return false;
            }

            var masterRobot = robotDataList.value.find(r => r.masterFlag == 1 );
            if(masterRobot == null){
                proxy.$modal.msgError("请添加一个主机器人")
                return false;
            }       
        }

        stepIndex.value++;
    }
}

//获取点位树
function getPointTree() {
  pointTreeSelect(form.value.deptId).then(response => {
    pointOptions.value = response.data
    defaultExpandedKeys.value = pointOptions.value.map(item => item.id)
  })
}
//树节点选中事件
function handleCheckChange(data, checked, node) {
    const checkedKeys = tree.value.getCheckedKeys();
    const checkedNodes = tree.value.getCheckedNodes();

    selectedKeys.value = checkedKeys;
    var checkPoints = []
    checkedNodes.forEach(node=> {
      if(node.nodeType == '2'){
        checkPoints.push(node.id);
      }
    })
    selectedPoints.value = checkPoints;
}


// /** 查询部门下拉树结构 */
// function getDeptTree() {
//   deptTreeSelect().then(response => {
//     deptOptions.value = response.data
//     enabledDeptOptions.value = filterDisabledDept(JSON.parse(JSON.stringify(response.data)))
//   })
// }

// /** 过滤禁用的部门 */
// function filterDisabledDept(deptList) {
//   return deptList.filter(dept => {
//     if (dept.disabled) {
//       return false
//     }
//     if (dept.children && dept.children.length) {
//       dept.children = filterDisabledDept(dept.children)
//     }
//     return true
//   })
// }


//添加机器人按钮事件
function handleAddRobot(){

    robotOpen.value = true;
    robotForm.value.robotId = undefined;
    robotForm.value.masterFlag = undefined;
}

//机器人删除
function handleDeleteRobot(row){

    robotDataList.value = robotDataList.value.filter(obj => obj.robotId !== row.robotId);

}

//查询所有机器人
function getAllRobot(){
  listAllRobot().then(res => {
    robotList.value = res.data;
  })
}

//查询设备树的一级（相当于部门）
function getAllDept(){
  listAllDept().then(res => {
    bizDeviceTreeList.value = res.data;
  })
}


//添加机器人提交
function submitRobotForm(){

  proxy.$refs["robotRef"].validate(valid => {
    if (valid) {

        var oldRobot = robotDataList.value.find(r => r.robotId == robotForm.value.robotId);
        if(oldRobot != null){
            proxy.$modal.msgError("机器人：【"+oldRobot.robotName+"】已在列表中")
            return false;
        }

        // var masterRobot = robotDataList.value.find(r => r.masterFlag == 1 && robotForm.value.masterFlag == 1);
        // if(masterRobot != null){
        //     proxy.$modal.msgError("列表已经有一个主机器人")
        //     return false;
        // }

        var robot = robotList.value.find(r => r.id == robotForm.value.robotId);
        if(robot != null){
            var o = {
                robotId: robotForm.value.robotId,
                robotName: robot.robotName,
                masterFlag: robotForm.value.masterFlag
            }
            robotDataList.value.push(o);
        }   
        robotOpen.value = false
  
    }
  })
}

function robotCancel(){
    robotOpen.value = false;
    robotForm.value = {
        robotId: undefined,
        masterFlag: undefined
    }
    proxy.resetForm("robotRef")
}






/** 查询参数列表 */
function getList() {
  loading.value = true
  listTask(proxy.addDateRange(queryParams.value, dateRange.value)).then(response => {
    taskDefList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

/** 取消按钮 */
function cancel() {
  open.value = false
  reset()
}

/** 表单重置 */
function reset() {
  form.value = {
    id: undefined,
    taskName: undefined,
    priorty: undefined,
    deptId: undefined,
    remark: undefined,

    planType: undefined,
    beginTime: undefined,
    endTime: undefined,
    cycleType: undefined,
    cycleTrigger: undefined,
    cycleScope: undefined,
    cycleInterval: 1
  }
  stepIndex.value = 1;
  selectedKeys.value = [];
  selectedPoints.value = [];
  robotDataList.value = [];
  proxy.resetForm("taskRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = []
  proxy.resetForm("queryRef")
  handleQuery()
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加任务"
}

/** 修改按钮操作 */
function handleUpdate(row, step) {
  reset()
  const id = row.id || ids.value
  getTask(id).then(response => {
    form.value = response.data
    open.value = true
    title.value = "修改任务"

    selectedKeys.value = response.data.bizPointIds;
    selectedPoints.value = response.data.bizPointIds;
    getPointTree();



    robotDataList.value = []
    response.data.taskRobotList.forEach(element => {
        var robot = robotList.value.find(r => r.id == element.robotId);
        if(robot != null){
            var o = {
                robotId: element.robotId,
                robotName: robot.robotName,
                masterFlag: element.masterFlag
            }
            robotDataList.value.push(o);
        } 
    });
    stepIndex.value = step;

  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["taskRef"].validate(valid => {
    if (valid) {

        //第1步验证
        if(form.value.taskName == null || form.value.taskName == undefined ||  form.value.taskName == ''){
            proxy.$modal.msgError("请输入任务名称")
            return false;
        }
        if(form.value.priorty == null || form.value.priorty == undefined || form.value.taskName == ''){
            proxy.$modal.msgError("请输入优先级")
            return false;
        }

        const regex = /(^[\-0-9][0-9]*(\.[0-9]+)?)$/;
        if(!regex.test(form.value.priorty)){
            proxy.$modal.msgError("优先级须输入合法的数字");
            return false;
        }

        if(form.value.deptId == null || form.value.deptId == undefined){
            proxy.$modal.msgError("请选择管理部门")
            return false;
        }
        //第2步验证

        if(selectedPoints.value.length == 0){
            proxy.$modal.msgError("请选择点位")
            return false;
        }

        //第3步验证
        if(robotDataList.value.length == 0){
            proxy.$modal.msgError("请选择机器人")
            return false;
        }

        var masterRobot = robotDataList.value.find(r => r.masterFlag == 1 );
        if(masterRobot == null){
            proxy.$modal.msgError("请添加一个主机器人")
            return false;
        }     

        //第4步验证
        if(form.value.planType == 0 ){
            if(form.value.beginTime == null || form.value.beginTime == ''){
                proxy.$modal.msgError("请输入开始时间");
                return false;
            }
            if(form.value.endTime == null || form.value.endTime == ''){
                proxy.$modal.msgError("请输入结束时间");
                return false;
            }

        }

        if(form.value.planType == 1 ){
            // if(form.value.cycleType == null || form.value.cycleType == ''){
            //     proxy.$modal.msgError("请选择周期类型");
            //     return false;
            // }
            if(form.value.cycleTrigger == null || form.value.cycleTrigger == ''){
                proxy.$modal.msgError("请输入执行时间");
                return false;
            }
            if(form.value.cycleScope == null || form.value.cycleScope == ''){
                proxy.$modal.msgError("请选择周期范围");
                return false;
            }
            // if(form.value.cycleInterval == null || form.value.cycleInterval == ''){
            //     proxy.$modal.msgError("请填写时间间隔");
            //     return false;
            // }
            

        }

        form.value.bizPointIds = selectedKeys.value;
        form.value.taskRobotList = robotDataList.value;
        if (form.value.id != undefined) {
            updateTask(form.value).then(response => {
            proxy.$modal.msgSuccess("修改成功")
            open.value = false
            getList()
            })
        } else {
            addTask(form.value).then(response => {
            proxy.$modal.msgSuccess("新增成功")
            open.value = false
            getList()
            })
        }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const id = row.id || ids.value
  proxy.$modal.confirm('是否确认删除任务数据项？').then(function () {
    return delTask(id)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}


// getDeptTree()
getAllDept()
getAllRobot()

getList()
</script>

<style>
    /* 或者通过外层容器设置 */
    .tree-container {
        height: 300px;
    }
    .tree-container .el-tree {
        height: 100%;
    }
</style>

<style scoped>
.content_box {
  width: 100%;
  display: flex;
  flex-wrap: wrap;

  .content_table_item {
    position: relative;
    margin-left: 48px;
    margin-top: 20px;
    width: 400px;
    height: 195px;
    box-sizing: border-box;
    flex-direction: column;
  }

}

.el-card.ft-card {
  width: 100%;
  height: 100%;

  .ft-tag {
    padding: 10px;
    height: 40px;

    .ml-3 {
      margin-left: 6px;
    }

    .ml-title {
      font-size: 14px;
    }

    border-bottom: 1px solid var(--el-card-border-color);
  }

  .ft-head {
    width: 100%;
    height: 170px;

  }

  .ft-body {
    margin-top: 5px;
    width: 400px;
    height: 130px;
    padding: 10px;
    display: flex;
    justify-content: space-between;

    .ft-body-image {
      width: 40%;
      height: 100px;
      text-align: center;
      margin-right: 10px;
    }

    .ft-body-item {
      width: 60%;
    }

    .item-mb {
      width: 100%;
      margin-bottom: 8px;
      text-overflow: ellipsis;
      font-size: 14px;
    }
  }
}


</style>
<style scoped>
.ellipsis-link {
  display: inline-block;  /* 关键：inline-block 才能设置宽度 */
  max-width: 230px;      /* 限制宽度 */
  white-space: nowrap;    /* 禁止换行 */
  overflow: hidden;      /* 隐藏超出部分 */
  text-overflow: ellipsis; /* 显示省略号 */
}
</style>