import request from '@/utils/request'

export function list(query) {
    return request({
        url: '/intelligentRobot/intelligentRobotView/list',
        method: 'get',
        params: query
    })
}

export function getRunStatus(data) {
    return request({
        url: '/intelligentRobot/intelligentRobotView/getRunStatus',
        method: 'get',
        params: query
    })
}

// region 地图相关api

/**
 * 获取地图CAD图层
 */
export function getMapCadLayer(query) {
    return request({
        url: '/dispatch/getMapCadLayer',
        method: 'get',
        params: query
    })
}


// endregion
