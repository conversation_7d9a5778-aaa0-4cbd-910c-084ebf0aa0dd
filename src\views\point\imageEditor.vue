<template>
	<div>
		<div id="tui-image-editor"></div>
	</div>
</template>
<script>
	import 'tui-image-editor/dist/tui-image-editor.css'
	import 'tui-color-picker/dist/tui-color-picker.css'
	import ImageEditor from 'tui-image-editor'
	import point from '@/api/point/point';
	import {
		markStr
	} from './js/markData';

	const locale_zh = {
		ZoomIn: '放大',
		ZoomOut: '缩小',
		Hand: '手掌',
		History: '历史',
		Resize: '调整宽高',
		Crop: '裁剪',
		DeleteAll: '全部删除',
		Delete: '删除',
		Undo: '撤销',
		Redo: '反撤销',
		Reset: '重置',
		Flip: '镜像',
		Rotate: '旋转',
		Draw: '画',
		Shape: '形状标注',
		Icon: '图标标注',
		Text: '文字标注',
		Mask: '遮罩',
		Filter: '滤镜',
		Bold: '加粗',
		Italic: '斜体',
		Underline: '下划线',
		Left: '左对齐',
		Center: '居中',
		Right: '右对齐',
		Color: '颜色',
		'Text size': '字体大小',
		Custom: '自定义',
		Square: '正方形',
		Apply: '应用',
		Cancel: '取消',
		'Flip X': 'X 轴',
		'Flip Y': 'Y 轴',
		Range: '区间',
		Stroke: '描边',
		Fill: '填充',
		Circle: '圆',
		Triangle: '三角',
		Rectangle: '矩形',
		Free: '曲线',
		Straight: '直线',
		Arrow: '箭头',
		'Arrow-2': '箭头2',
		'Arrow-3': '箭头3',
		'Star-1': '星星1',
		'Star-2': '星星2',
		Polygon: '多边形',
		Location: '定位',
		Heart: '心形',
		Bubble: '气泡',
		'Custom icon': '自定义图标',
		'Load Mask Image': '加载蒙层图片',
		Grayscale: '灰度',
		Blur: '模糊',
		Sharpen: '锐化',
		Emboss: '浮雕',
		'Remove White': '除去白色',
		Distance: '距离',
		Brightness: '亮度',
		Noise: '噪音',
		'Color Filter': '彩色滤镜',
		Sepia: '棕色',
		Sepia2: '棕色2',
		Invert: '负片',
		Pixelate: '像素化',
		Threshold: '阈值',
		Tint: '色调',
		Multiply: '正片叠底',
		Blend: '混合色',
		Width: '宽度',
		Height: '高度',
		'Lock Aspect Ratio': '锁定宽高比例'
	}

	const customTheme = {
		"common.bi.image": "", // 左上角logo图片
		"common.bisize.width": "0px",
		"common.bisize.height": "0px",
		"common.backgroundImage": "none",
		"common.backgroundColor": "#f3f4f6",
		"common.border": "1px solid #333",

		// header
		"header.backgroundImage": "none",
		"header.backgroundColor": "#f3f4f6",
		"header.border": "0px",

		// load button
		"loadButton.backgroundColor": "#fff",
		"loadButton.border": "1px solid #ddd",
		"loadButton.color": "#222",
		"loadButton.fontFamily": "NotoSans, sans-serif",
		"loadButton.fontSize": "12px",
		"loadButton.display": "none", // 隐藏

		// download button
		"downloadButton.backgroundColor": "#fdba3b",
		"downloadButton.border": "1px solid #fdba3b",
		"downloadButton.color": "#fff",
		"downloadButton.fontFamily": "NotoSans, sans-serif",
		"downloadButton.fontSize": "12px",
		"downloadButton.display": "none", // 隐藏

		// icons default
		"menu.normalIcon.color": "#8a8a8a",
		"menu.activeIcon.color": "#555555",
		"menu.disabledIcon.color": "#ccc",
		"menu.hoverIcon.color": "#e9e9e9",
		"submenu.normalIcon.color": "#8a8a8a",
		"submenu.activeIcon.color": "#e9e9e9",

		"menu.iconSize.width": "24px",
		"menu.iconSize.height": "24px",
		"submenu.iconSize.width": "32px",
		"submenu.iconSize.height": "32px",

		// submenu primary color
		"submenu.backgroundColor": "#1e1e1e",
		"submenu.partition.color": "#858585",

		// submenu labels
		"submenu.normalLabel.color": "#858585",
		"submenu.normalLabel.fontWeight": "lighter",
		"submenu.activeLabel.color": "#fff",
		"submenu.activeLabel.fontWeight": "lighter",

		// checkbox style
		"checkbox.border": "1px solid #ccc",
		"checkbox.backgroundColor": "#fff",

		// rango style
		"range.pointer.color": "#fff",
		"range.bar.color": "#666",
		"range.subbar.color": "#d1d1d1",

		"range.disabledPointer.color": "#414141",
		"range.disabledBar.color": "#282828",
		"range.disabledSubbar.color": "#414141",

		"range.value.color": "#fff",
		"range.value.fontWeight": "lighter",
		"range.value.fontSize": "11px",
		"range.value.border": "1px solid #353535",
		"range.value.backgroundColor": "#151515",
		"range.title.color": "#fff",
		"range.title.fontWeight": "lighter",

		// colorpicker style
		"colorpicker.button.border": "1px solid #1e1e1e",
		"colorpicker.title.color": "#fff",
	};

	export default {
		props: {
			imgUrl: {
				type: String,
				default: "",
			}
		},
		data() {
			return {
				instance: null
			}
		},
		watch: {
			'$props.imgUrl'(newVal) {
				this.init()
			}
		},
		mounted() {
			this.init()
		},
		methods: {
			init() {
				this.instance = new ImageEditor(document.querySelector('#tui-image-editor'), {
					includeUI: {
						loadImage: {
							path: import.meta.env.VITE_APP_BASE_API + this.$props.imgUrl,
							name: 'image'
						},
						menu: ['shape', 'text', 'draw'], // 底部菜单按钮列表 隐藏镜像flip和遮罩mask
						// initMenu: 'shape', // 默认打开的菜单项
						menuBarPosition: 'bottom', // 菜单所在的位置
						locale: locale_zh, // 本地化语言为中文
						theme: customTheme // 自定义样式
					},
					cssMaxWidth: 600, // canvas 最大宽度
					cssMaxHeight: 300 // canvas 最大高度
				})
				document.getElementsByClassName('tui-image-editor-main')[0].style.top = '45px' // 调整图片显示位置
				document.getElementById('tui-image-editor').style.height = '500px'
				document.getElementsByClassName('tie-btn-reset tui-image-editor-item help')[0].style.display = 'none' // 隐藏顶部重置按钮
				document.getElementsByClassName('tie-btn-history tui-image-editor-item help')[0].style.display = 'none'
				document.getElementsByClassName('tie-btn-hand tui-image-editor-item help')[0].style.display = 'none'
				document.getElementsByClassName('tie-btn-zoomOut tui-image-editor-item help')[0].style.display = 'none'
				document.getElementsByClassName('tie-btn-zoomIn tui-image-editor-item help')[0].style.display = 'none'				
				document.getElementsByClassName('tui-image-editor-icpartition')[0].style.display = 'none'
								
			},
			infoKeysFor(lastIdenConfInfo, infoKeys, index) {
				let info = lastIdenConfInfo[infoKeys[index]]
				if(info.type == 'rect') {
					this.instance.addShape('rect', {
						fill: '',
						stroke: 'red',
						strokeWidth: info.strokeWidth,
						width: info.width,
						height: info.height,
						left: info.left,
						top: info.top,
						isRegular: true
					}).then(() => {
						this.instance.discardSelection();
						document.getElementsByClassName('tie-btn-shape')[0].click()
						if (index + 1 != infoKeys.length) {
							this.infoKeysFor(lastIdenConfInfo, infoKeys, index + 1)
						}
					})
				} else if(info.type == 'i-text') {
					this.instance.addText(info.text, {
						styles: {
							fill: 'red',
							fontSize: info.fontSize
						},
						position: {
							x: info.left,
							y: info.top
						}
					}).then(() => {
						this.instance.discardSelection();
						document.getElementsByClassName('tie-btn-text')[0].click()
						if (index + 1 != infoKeys.length) {
							this.infoKeysFor(lastIdenConfInfo, infoKeys, index + 1)
						}
					})
				}
			},
			markClick(data, index, idenModel) {
				if(index == 0) console.log(data, index, idenModel);
				
				if(idenModel && idenModel.lastIdenConfInfo != null && idenModel.lastIdenConfInfo != '') {
					let lastIdenConfInfo = JSON.parse(idenModel.lastIdenConfInfo);
					console.log(lastIdenConfInfo)
					let infoKeys = Object.keys(lastIdenConfInfo)					
					this.infoKeysFor(lastIdenConfInfo, infoKeys, 0)
				} else {
					if (!data[index].seq || data[index].seq == 0) {
						this.instance.addShape('rect', {
							fill: '',
							stroke: 'red',
							strokeWidth: 15,
							width: 500,
							height: 500,
							left: 500 * (index + 1) + index * 100,
							top: 800,
							isRegular: true
						}).then(() => {
							this.instance.discardSelection();
							this.instance.addText(data[index].idenTypeName, {
								styles: {
									fill: 'red',
									fontSize: 80
								},
								position: {
									x: 380 * (index + 1) + index * 220,
									y: 460
								}
							}).then(() => {
								this.instance.discardSelection();
								if (data[index].idenType.idenTypeName == '单圈表') {
									this.instance.addText('起点', {
										styles: {
											fill: 'red',
											fontSize: 70
										},
										position: {
											x: 300 * (index + 1) + index * 280,
											y: 950
										}
									}).then(() => {
										this.instance.discardSelection();
										this.instance.addText('终点', {
											styles: {
												fill: 'red',
												fontSize: 70
											},
											position: {
												x: 580 * (index + 1) + index * 30,
												y: 950
											}
										}).then(() => {
											this.instance.discardSelection();
											document.getElementsByClassName('tie-btn-text')[0]
												.click()
											if (index + 1 != data.length) {
												this.markClick(data, index + 1, idenModel)
											}
										})
									})
								} else {
									document.getElementsByClassName('tie-btn-text')[0].click()
									if (index + 1 != data.length) {
										this.markClick(data, index + 1, idenModel)
									}
								}
							})
						});
					}
				}
				
			},
			lineClick() {
				this.instance.addShape('rect', {
					fill: 'yellow',
					stroke: '',
					strokeWidth: 10,
					width: 450,
					height: 10,
					left: 1350,
					top: 1200,
					isRegular: true
				}).then(() => {
					this.instance.discardSelection();
					document.getElementsByClassName('tie-btn-shape')[0].click()
				})
			},
			// 保存图片，并上传
			save(val) {
				const base64String = this.instance.toDataURL() // base64 文件				
				const data = window.atob(base64String.split(',')[1])
				const ia = new Uint8Array(data.length)
				for (let i = 0; i < data.length; i++) {
					ia[i] = data.charCodeAt(i)
				}
				const blob = new Blob([ia], {
					type: 'image/png'
				}) // blob 文件
				const form = new FormData()
				form.append('image', blob)

				point.uploadImg(form).then(res => {
					let params = {
						rawImgUrl: this.$props.imgUrl,
						markImgUrl: res.msg,
						coord: this.instance._graphics._objects
					}
					// console.log(JSON.stringify(this.instance._graphics._objects))
					if (!val) {
						this.$emit('params', params)
					} else {
						this.$emit('params_', params)
					}
				})
			}
		}
	}
</script>

<style scoped>
	.drawing-container {
		height: 900px;
		position: relative;
	}

	/deep/.tui-image-editor-container.bottom .tui-image-editor-submenu>div {
		padding-bottom: 0;
		top: 55px
	}
</style>