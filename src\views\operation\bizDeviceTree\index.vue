<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
         <el-form-item label="设备树名称" prop="deviceName">
            <el-input
               v-model="queryParams.deviceName"
               placeholder="请输入设备树名称"
               clearable
               style="width: 200px"
               @keyup.enter="handleQuery"
            />
         </el-form-item>
         <el-form-item label="状态" prop="status">
            <el-select v-model="queryParams.status" placeholder="设备树状态" clearable style="width: 200px">
               <el-option
                  v-for="dict in sys_normal_disable"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
               />
            </el-select>
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
         </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
         <el-col :span="1.5">
            <el-button
               type="primary"
               plain
               icon="Plus"
               @click="handleAdd"
            >新增</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button
               type="info"
               plain
               icon="Sort"
               @click="toggleExpandAll"
            >展开/折叠</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button type="success" plain icon="Upload" @click="handleImport">导入</el-button>
         </el-col>
         <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table
         v-if="refreshTable"
         v-loading="loading"
         :data="bizDeviceTreeList"
         row-key="id"
         :default-expand-all="isExpandAll"
         :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      >
         <el-table-column prop="deviceName" label="设备树名称" width="260"></el-table-column>
         <el-table-column prop="seq" label="排序" width="200"></el-table-column>
         <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
               <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
            </template>
         </el-table-column>
         <el-table-column label="创建时间" align="center" prop="createTime" width="200">
            <template #default="scope">
               <span>{{ parseTime(scope.row.createTime) }}</span>
            </template>
         </el-table-column>
         <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template #default="scope">
               <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)">修改</el-button>
               <el-button link type="primary" icon="Plus" v-if="scope.row.nodeType == 0" @click="handleAdd(scope.row)">新增</el-button>
               <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
            </template>
         </el-table-column>
      </el-table>

      <!-- 添加或修改部门对话框 -->
      <el-dialog :title="title" v-model="open" width="600px" append-to-body>
         <el-form ref="bizDeviceTreeRef" :model="form" :rules="rules" label-width="130px">
            <el-row>
               <!-- <el-col :span="24" v-if="form.parentId != undefined">
                  <el-form-item label="上级设备树" prop="parentId">
                     <el-tree-select
                        v-model="form.parentId"
                        :data="bizDeviceTreeOptions"
                        :props="{ value: 'id', label: 'deviceName', children: 'children' }"
                        value-key="id"
                        placeholder="选择上级设备树，如果未选择上级设备树，则作为一级"
                        check-strictly
                     />
                  </el-form-item>
               </el-col> -->
               <el-col :span="24" v-if="form.parentId != undefined && form.parentId != 0">
                  <el-form-item label="上级设备树名称" prop="parentName">
                     <el-input v-model="form.parentName" placeholder="请输入设备树名称" disabled/>
                  </el-form-item>
               </el-col>
               <el-col :span="24">
                  <el-form-item label="设备树名称" prop="deviceName">
                     <el-input v-model="form.deviceName" placeholder="请输入设备树名称" />
                  </el-form-item>
               </el-col>
               <el-col :span="12">
                  <el-form-item label="显示排序" prop="seq">
                     <el-input-number v-model="form.seq" controls-position="right" :min="0" />
                  </el-form-item>
               </el-col>
               <el-col :span="12">
                  <el-form-item label="设备树状态">
                     <el-radio-group v-model="form.status">
                        <el-radio
                           v-for="dict in sys_normal_disable"
                           :key="dict.value"
                           :value="dict.value"
                        >{{ dict.label }}</el-radio>
                     </el-radio-group>
                  </el-form-item>
               </el-col>
            </el-row>
         </el-form>
         <template #footer>
            <div class="dialog-footer">
               <el-button type="primary" @click="submitForm">确 定</el-button>
               <el-button @click="cancel">取 消</el-button>
            </div>
         </template>
      </el-dialog>

          <!-- 设备树导入对话框 -->
        <el-dialog :title="upload.title" v-model="upload.open" width="400px" append-to-body>
        <el-upload ref="uploadRef" :limit="1" accept=".xlsx, .xls" :headers="upload.headers" :action="upload.url + '?updateSupport=' + upload.updateSupport" :disabled="upload.isUploading" :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <template #tip>
            <div class="el-upload__tip text-center">
                <span>仅允许导入xls、xlsx格式文件。</span>
                <el-link type="primary" :underline="false" style="font-size: 12px; vertical-align: baseline" @click="importTemplate">下载模板</el-link>
            </div>
            </template>
        </el-upload>
        <template #footer>
            <div class="dialog-footer">
            <el-button type="primary" @click="submitFileForm">确 定</el-button>
            <el-button @click="upload.open = false">取 消</el-button>
            </div>
        </template>
        </el-dialog>
   </div>
</template>

<script setup name="BizDeviceTree">
import { getToken } from "@/utils/auth"

import { listBizDeviceTree, getBizDeviceTree, addBizDeviceTree, updateBizDeviceTree, delBizDeviceTree} from "@/api/operation/bizDeviceTree"


const { proxy } = getCurrentInstance()
const { sys_normal_disable } = proxy.useDict("sys_normal_disable")

const bizDeviceTreeList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const title = ref("")
const bizDeviceTreeOptions = ref([])
const isExpandAll = ref(true)
const refreshTable = ref(true)

/*** 设备树导入参数 */
const upload = reactive({
  // 是否显示弹出层（设备树导入）
  open: false,
  // 弹出层标题（设备树导入）
  title: "",
  // 是否禁用上传
  isUploading: false,
  // 是否更新已经存在的用户数据
  updateSupport: 0,
  // 设置上传的请求头部
  headers: { Authorization: "Bearer " + getToken() },
  // 上传的地址
  url: import.meta.env.VITE_APP_BASE_API + "/biz/device/tree/import"
})

const data = reactive({
  form: {},
  queryParams: {
    deviceName: undefined,
    status: undefined
  },
  rules: {
    parentId: [{ required: true, message: "上级部门不能为空", trigger: "blur" }],
    parentName: [{ required: true, message: "上级部门不能为空", trigger: "blur" }],
    deviceName: [{ required: true, message: "部门名称不能为空", trigger: "blur" }],
    seq: [{ required: true, message: "显示排序不能为空", trigger: "blur" }]
  },
})

const { queryParams, form, rules } = toRefs(data)

/** 查询部门列表 */
function getList() {
  loading.value = true
  listBizDeviceTree(queryParams.value).then(response => {
    bizDeviceTreeList.value = proxy.handleTree(response.data, "id")
    loading.value = false
  })
}

/** 取消按钮 */
function cancel() {
  open.value = false
  reset()
}

/** 表单重置 */
function reset() {
  form.value = {
    deptId: undefined,
    parentId: undefined,
    deviceName: undefined,
    parentName: undefined,   
    seq: 0,
    status: "0"
  }
  proxy.resetForm("bizDeviceTreeRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

/** 新增按钮操作 */
function handleAdd(row) {
  reset()
  listBizDeviceTree().then(response => {
    bizDeviceTreeOptions.value = proxy.handleTree(response.data, "id")
  })
  if (row != undefined) {
    form.value.parentId = row.id
    form.value.parentName = row.deviceName
  }
  open.value = true
  title.value = "添加设备树"
}

/** 展开/折叠操作 */
function toggleExpandAll() {
  refreshTable.value = false
  isExpandAll.value = !isExpandAll.value
  nextTick(() => {
    refreshTable.value = true
  })
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  getBizDeviceTree(row.id).then(response => {
    form.value = response.data
    if(form.value.parentId != null && form.value.parentId != 0){
        form.value.parentName = response.data.parent.deviceName;
    }
    open.value = true
    title.value = "修改设备树"
  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["bizDeviceTreeRef"].validate(valid => {
    if (valid) {
      if (form.value.id != undefined) {
        updateBizDeviceTree(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        addBizDeviceTree(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$modal.confirm('是否确认删除名称为"' + row.deviceName + '"的数据项?').then(function() {
    return delBizDeviceTree(row.id)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}


/** 导入按钮操作 */
function handleImport() {
  upload.title = "设备树导入"
  upload.open = true
}

/** 下载模板操作 */
function importTemplate() {
  proxy.download("biz/device/tree/downloadTemplate", {
  }, `设备树导入模板_${new Date().getTime()}.xlsx`)
}

/**文件上传中处理 */
const handleFileUploadProgress = (event, file, fileList) => {
  upload.isUploading = true
}

/** 文件上传成功处理 */
const handleFileSuccess = (response, file, fileList) => {
  upload.open = false
  upload.isUploading = false
  proxy.$refs["uploadRef"].handleRemove(file)
  proxy.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true })
  getList()
}

/** 提交上传文件 */
function submitFileForm() {
  proxy.$refs["uploadRef"].submit()
}


getList()
</script>
