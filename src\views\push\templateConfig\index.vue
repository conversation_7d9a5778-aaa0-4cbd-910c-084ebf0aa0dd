<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="88px">
         <el-form-item label="模板名称" prop="templateName">
            <el-input
               v-model="queryParams.templateName"
               placeholder="请输入模板名称"
               clearable
               style="width: 240px"
               @keyup.enter="handleQuery"
            />
         </el-form-item>
         <el-form-item label="模板标题" prop="templateTitle">
            <el-input
               v-model="queryParams.templateTitle"
               placeholder="请输入模板标题"
               clearable
               style="width: 240px"
               @keyup.enter="handleQuery"
            />
         </el-form-item>
         <el-form-item label="推送类型" prop="pushType">
            <el-select v-model="queryParams.pushType" placeholder="请选择推送类型" clearable style="width: 240px">
               <el-option
                  v-for="dict in push_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
               />
            </el-select>
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
         </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
         <el-col :span="1.5">
            <el-button
               type="primary"
               plain
               icon="Plus"
               @click="handleAdd"
            >新增</el-button>
         </el-col>

         <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" v-if="false"/>
      <el-table-column label="模板名称" align="center" prop="templateName" />
      <el-table-column label="模板描述" align="center" prop="templateDescription"  :show-overflow-tooltip="true"/>
      <el-table-column label="模板标题" align="center" prop="templateTitle" />
      <el-table-column label="模板类型" align="center" prop="pushType">
       <template #default="scope">
          <dict-tag :options="push_type" :value="scope.row.pushType" />
        </template>
      </el-table-column>
      <el-table-column label="模板内容" align="center" prop="templateContent"  :show-overflow-tooltip="true"/>
      <el-table-column label="操作" align="center" width="180" class-name="small-padding fixed-width">
        <template #default="scope">
               <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" >修改</el-button>
               <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" >删除</el-button>
        </template>
        </el-table-column>
      </el-table>

      <pagination
         v-show="total > 0"
         :total="total"
         v-model:page="queryParams.pageNum"
         v-model:limit="queryParams.pageSize"
         @pagination="getList"
      />

      <!-- 添加或修改模版配置对话框 -->
      <el-dialog :title="title" v-model="open" width="40%" append-to-body>
         <el-form ref="templateConfigPileRef" :model="form" :rules="rules" label-width="100px">
            <el-form-item label="模板名称" prop="templateName">
               <el-input v-model="form.templateName" placeholder="请输入模板名称" />
            </el-form-item>
            <el-form-item label="模板描述" prop="templateDescription">
               <el-input v-model="form.templateDescription" placeholder="请输入模板描述" />
            </el-form-item>
            <el-form-item label="模板标题" prop="templateTitle">
               <el-input v-model="form.templateTitle" placeholder="请输入模板标题" />
            </el-form-item>
            <el-form-item label="推送类型" prop="pushType">
              <el-select v-model="form.pushType" placeholder="请选择推送类型" clearable @change="pushTypeChange">
                <el-option v-for="dict in push_type" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="" prop="">
                <el-button type="primary" icon="ArrowDownBold" @click="handleExpandParam" v-if="paramExpand">选择参数</el-button>
                <el-button type="primary" icon="ArrowUpBold" @click="handleExpandParam" v-if="!paramExpand">收起参数</el-button>
            </el-form-item>
            <el-form-item label="" prop=""  v-if="!paramExpand">
                <div class="tag-group">                   
                    <el-tag
                        v-for="tag in configParamList"
                        :size="'small'"
                        :data-param="tag.paramColumn"
                        :key="tag.paramName"
                        @click="handleTagCheck(tag.paramColumn)"
                        style="margin-left:5px;"
                        >
                        {{tag.paramName}}
                    </el-tag>&nbsp;&nbsp;&nbsp;&nbsp;
                </div>
            </el-form-item>
            <el-form-item label="模板内容" prop="templateContent">
                <el-input type="textarea" id="textarea" v-model="form.templateContent" rows="5" placeholder="请输入模板内容"></el-input>
            </el-form-item>
         </el-form>
         <template #footer>
            <div class="dialog-footer">
               <el-button type="primary" @click="submitForm">确 定</el-button>
               <el-button @click="cancel">取 消</el-button>
            </div>
         </template>
      </el-dialog>
   </div>
</template>

<script setup name="PushTemplateConfig">

import { listTemplateConfig, getTemplateConfig, addTemplateConfig, updateTemplateConfig, delTemplateConfig, getConfigParam } from "@/api/push/templateConfig"


const { proxy } = getCurrentInstance()
const { sys_yes_no, push_type } = proxy.useDict("sys_yes_no", "push_type")

const dataList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")
const dateRange = ref([])

const bizPointList = ref([])


const paramExpand = ref(true)
const configParamList = ref([])


const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    templateName: undefined,
    templateTitle: undefined,
    pushType: undefined
  },
  rules: {
    templateName: [{ required: true, message: "模板名称不能为空", trigger: "blur" }],
    templateTitle: [{ required: true, message: "模板标题不能为空", trigger: "blur" }],   
    pushType: [{ required: true, message: "推送类型不能为空", trigger: "blur" }],
    templateContent: [{ required: true, message: "模板内容不能为空", trigger: "blur" }]
  }
})

const { queryParams, form, rules } = toRefs(data)


//展开参数图标
function handleExpandParam(){
    paramExpand.value = !paramExpand.value;
}

//根据选择的推送类型查询配置参数
function pushTypeChange(){
    form.value.templateContent = null;
    getConfigParamByPushType();
}

function getConfigParamByPushType() {
    if(form.value.pushType){
        getConfigParam(form.value.pushType).then(response => {
            configParamList.value = response.data;
        });
    }else{
        configParamList.value = [];
    }
}


function handleTagCheck(e){
    // var dataParam = e.target.getAttribute('data-param');
    insertVariable(e)
}

async function insertVariable(value) {
      value = ("${").concat(value).concat("}")
      const myField = document.querySelector('#textarea');
      // const myField = this.$refs.singleText;
      // console.log('myField--',myField);
      if(myField.selectionStart || myField.selectionStart === 0) {
        let startPos = myField.selectionStart;
        let endPos = myField.selectionEnd;
       form.value.templateContent = myField.value.substring(0, startPos) + value
                    + myField.value.substring(endPos, myField.value.length);
        // await this.$nextTick() // 这句是重点, 圈起来
        myField.focus();
        myField.setSelectionRange(endPos + value.length, endPos + value.length);
      } else {
        form.value.templateContent  = value;
      }
    }

/** 查询模版列表 */
function getList() {
  loading.value = true
  listTemplateConfig(proxy.addDateRange(queryParams.value, dateRange.value)).then(response => {
    dataList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

/** 取消按钮 */
function cancel() {
  open.value = false
  reset()
}

/** 表单重置 */
function reset() {
  form.value = {
    id: undefined,
    templateName: undefined,
    templateDescription: undefined,
    templateTitle: undefined,
    pushType: undefined,
    templateContent: undefined
  }
  paramExpand.value = true
  configParamList.value = [];
  proxy.resetForm("templateConfigPileRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = []
  proxy.resetForm("queryRef")
  handleQuery()
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加模版"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const id = row.id || ids.value
  getTemplateConfig(id).then(response => {
    form.value = response.data
    getConfigParamByPushType();
    open.value = true
    title.value = "修改模版"
  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["templateConfigPileRef"].validate(valid => {
    if (valid) {
      if (form.value.id != undefined) {
        updateTemplateConfig(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        addTemplateConfig(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const ids = row.id || ids.value
  proxy.$modal.confirm('是否确认删除模版配置数据项？').then(function () {
    return delTemplateConfig(ids)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

getList()
</script>
