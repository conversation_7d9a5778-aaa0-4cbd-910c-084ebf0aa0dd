<template>
	<div :class="classObj" class="app-wrapper" :style="{ '--current-color': theme }">
		<div :class="{ hasTagsView: hasTagsView, sidebarHide: sidebar.hide }" class="main-container">
			<div :class="{ 'fixed-header': true }">
				<el-image :src="imageUrl" style="position: absolute; left: 50px; top: 10px; height: 60px;"></el-image>

				<div class="navigation-demo">
					<sidebar v-if="!sidebar.hide" class="sidebar-container" />
				</div>
				
				<div v-show="basePathData">
					<el-menu :default-active="activeIndex" class="el-menu-demo" mode="horizontal" @select="handleSelect"
						:unique-opened="true">
						<sidebar-item v-for="(route, index) in selectionMenuData" :key="route.path + index"
							:item="route" :base-path="resolvePath(route.path)" />
					</el-menu>
				</div>

				<navbar @setLayout="setLayout" />
			</div>
			
			<app-main />
		</div>
	</div>
</template>

<script setup>
	import {
		isExternal
	} from '@/utils/validate'
	import {
		getNormalPath
	} from '@/utils/hyc'
	import SidebarItem from './components/Sidebar/SidebarItem'
	import {
		useWindowSize
	} from '@vueuse/core'
	import Sidebar from './components/Sidebar/index.vue'
	import {
		AppMain,
		Navbar,
		Settings,
		TagsView
	} from './components'
	import useAppStore from '@/store/modules/app'
	import useSettingsStore from '@/store/modules/settings'
	import usePermissionStore from '@/store/modules/permission'
	import useMenuStore from '@/store/modules/menu'
	import exampleImage from '@/assets/logo/logHead.png';

	const imageUrl = exampleImage;

	const settingsStore = useSettingsStore()
	const theme = computed(() => settingsStore.theme)
	const sideTheme = computed(() => settingsStore.sideTheme)
	const sidebar = computed(() => useAppStore().sidebar)
	const device = computed(() => useAppStore().device)
	const needTagsView = computed(() => settingsStore.tagsView)
	const fixedHeader = computed(() => settingsStore.fixedHeader)
	const permissionStore = usePermissionStore()
	const sidebarRouters = computed(() => permissionStore.sidebarRouters)

	const classObj = computed(() => ({
		hideSidebar: !sidebar.value.opened,
		openSidebar: sidebar.value.opened,
		withoutAnimation: sidebar.value.withoutAnimation,
		mobile: device.value === 'mobile'
	}))

	const {
		width,
		height
	} = useWindowSize()
	const WIDTH = 992 // refer to Bootstrap's responsive design

	watch(() => device.value, () => {
		if (device.value === 'mobile' && sidebar.value.opened) {
			useAppStore().closeSideBar({
				withoutAnimation: false
			})
		}
	})
	
	const { proxy } = getCurrentInstance()

	const menuStore = useMenuStore()
	const selectionMenuData = ref(null)
	const basePathData = ref(null)
	const selectionMenu = computed(() => menuStore.selectionMenu)
	const basePath = computed(() => menuStore.basePath)

	watch(selectionMenu, (New, Old) => {
		let path = resolvePath(New[0].path)
		if(proxy.$route.path.split('/')[1] == path.split('/')[1] && proxy.$route.path != path) {
			path = proxy.$route.path
		}
		activeIndex.value = path
		selectionMenuData.value = New
	})
	
	let hasTagsView = ref(false)
	watch(basePath, (New, Old) => {
		if(!New) {
			hasTagsView = false
		} else {
			hasTagsView = true
		}
		
		if(New != Old) {
			stopAllPlay()
			destroyPlugin()
		}
		basePathData.value = New
	})
	
	// 销毁海康摄像机插件
	function destroyPlugin() {
		WebVideoCtrl.I_DestroyPlugin().then(() => {
			console.log('销毁成功！')
		}).catch(() => {
			console.log('销毁失败！')
		})
	}
	
	function stopAllPlay() {
		WebVideoCtrl.I_StopAllPlay().then(() => {
			console.log('关闭全部视频播放成功！')
		}).catch(() => {
			console.log('关闭全部视频播放失败！')
		})
	}

	function resolvePath(routePath, routeQuery) {
		if (isExternal(routePath)) {
			return routePath
		}
		if (isExternal(basePathData.value)) {
			return basePathData.value
		}
		if (routeQuery) {
			let query = JSON.parse(routeQuery)
			return {
				path: getNormalPath(basePathData.value + '/' + routePath),
				query: query
			}
		}
		return getNormalPath(basePathData.value + '/' + routePath)
	}

	watchEffect(() => {
		if (width.value - 1 < WIDTH) {
			useAppStore().toggleDevice('mobile')
			useAppStore().closeSideBar({
				withoutAnimation: true
			})
		} else {
			useAppStore().toggleDevice('desktop')
		}
	})

	function handleClickOutside() {
		useAppStore().closeSideBar({
			withoutAnimation: false
		})
	}

	const settingRef = ref(null)

	function setLayout() {
		settingRef.value.openSetting()
	}

	import {
		ref,
		watch
	} from 'vue'

	const activeIndex = ref('1')
	const activeIndex2 = ref('1')
	const handleSelect = (key, keyPath) => {
		// console.log(key, keyPath)
	}
</script>

<style lang="scss" scoped>
	@import "@/assets/styles/mixin.scss";
	@import "@/assets/styles/variables.module.scss";

	.app-wrapper {
		@include clearfix;
		position: relative;
		height: 100%;
		width: 100%;
		// overflow-y: hidden;

		&.mobile.openSidebar {
			position: fixed;
			top: 0;
		}
	}

	.drawer-bg {
		background: #000;
		opacity: 0.3;
		width: 100%;
		top: 0;
		height: 100%;
		position: absolute;
		z-index: 999;
	}

	.fixed-header {
		position: fixed;
		top: 0;
		right: 0;
		z-index: 9;
		width: calc(100%);
		transition: width 0.28s;
	}

	.hideSidebar .fixed-header {
		width: calc(100% - 54px);
	}

	.sidebarHide .fixed-header {
		width: 100%;
	}

	.mobile .fixed-header {
		width: 100%;
	}

	.el-menu-demo {
		display: flex;
		justify-content: flex-end;
		height: 40px;
	}

	.navigation-demo {
		height: 80px;
		background-color: #282c38;
		padding: 20px 0 0 270px;
	}
</style>