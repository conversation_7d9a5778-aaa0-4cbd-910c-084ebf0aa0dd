<template>
	<div class="app-container">
		<el-row class="PTZ">
			<el-col :span="8">
				<el-form inline>
					<el-form-item label="机器人">
						<el-select filterable placeholder="请选择机器人" v-model="robotSelection"
							@change="robotSelectionChange">
							<el-option v-for="item in robotList" :label="item.robotName" :key="item.id"
								:value="item.id"></el-option>
						</el-select>
					</el-form-item>
					<el-form-item>
						<el-button @click="clickLogin(1);">登录</el-button>
						<el-button @click="clickLogout();">退出</el-button>

					</el-form-item>
					<el-form-item label="已登录">
						<el-select v-model="ip" @change="getChannelInfo();">
							<el-option v-for="item in ipList" :value="item" :label="item"></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="通道">
						<el-select v-model="channels">
							<el-option v-for="item in channelsList" :label="item.name" :value="item.id"></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="预览码流">
						<el-select v-model="streamtype" id="streamtype" class="sel">
							<el-option :value="1" label="主码流"></el-option>
							<el-option :value="2" label="子码流"></el-option>
							<el-option :value="3" label="第三码流"></el-option>
							<el-option :value="4" label="转码码流"></el-option>
						</el-select>
					</el-form-item><br />
					<el-form-item>
						<el-button @click="clickStartRealPlay();" type="success">开始预览</el-button>
						<el-button @click="clickStopRealPlay();">停止预览</el-button>
						<el-button @click="clickFullScreen();">全屏</el-button>
					</el-form-item><br />
					<el-form-item>
						<el-button @click="clickCapturePicData();">抓图</el-button>
						
						<el-button @click="clickStartRecord('realplay');">开始录像</el-button>
						<el-button @click="clickStopRecord('realplay');">停止录像</el-button>
						&nbsp;<span v-show="videoNum != 0">{{ videoNum }}</span>
						<el-tooltip class="box-item" effect="dark" content="录像播放器下载" placement="top">
							<a @click="playPluginDownload">
								<el-icon><Download /></el-icon>
							</a>
						</el-tooltip>
					</el-form-item><br />
					<el-form-item label="声量">
						<el-slider style="width: 250px;" v-model="volume" @change="clickSetVolume()" />
					</el-form-item>
					<el-form-item>
						<el-button @click="clickOpenSound();">打开声音</el-button>
						<el-button @click="clickCloseSound();">关闭声音</el-button>
					</el-form-item>
					<el-form-item label="通道">
						<el-select v-model="audiochannels">
							<el-option v-for="item in audiochannelsList" :label="item.id" :value="item.id"></el-option>
						</el-select>&nbsp;
						<el-button @click="clickGetAudioInfo();">获取通道</el-button>
						<el-button @click="clickStartVoiceTalk();">开始对讲</el-button>
						<el-button @click="clickStopVoiceTalk();">停止对讲</el-button>
					</el-form-item><br />
					<el-form-item label="云台">
						<div class="PTZ_direction">
							<el-button @mousedown="mouseDownPTZControl(5);"
								@mouseup="mouseUpPTZControl();">左上</el-button>
							<el-button @mousedown="mouseDownPTZControl(1);"
								@mouseup="mouseUpPTZControl();">上</el-button>
							<el-button @mousedown="mouseDownPTZControl(7);"
								@mouseup="mouseUpPTZControl();">右上</el-button><br />
							<el-button @mousedown="mouseDownPTZControl(3);"
								@mouseup="mouseUpPTZControl();">左</el-button>
							<el-button @mousedown="mouseDownPTZControl(9);"
								@mouseup="mouseUpPTZControl();">自动</el-button>
							<el-button @mousedown="mouseDownPTZControl(4);"
								@mouseup="mouseUpPTZControl();">右</el-button><br />
							<el-button @mousedown="mouseDownPTZControl(6);"
								@mouseup="mouseUpPTZControl();">左下</el-button>
							<el-button @mousedown="mouseDownPTZControl(2);"
								@mouseup="mouseUpPTZControl();">下</el-button>
							<el-button @mousedown="mouseDownPTZControl(8);"
								@mouseup="mouseUpPTZControl();">右下</el-button>
						</div>
					</el-form-item>
					<el-form-item>
						<el-button @mousedown="PTZZoomIn()" @mouseup="PTZZoomStop()">变倍+</el-button>
						<el-button @mousedown="PTZZoomout()" @mouseup="PTZZoomStop()">变倍-</el-button>
						<el-button @mousedown="PTZFocusIn()" @mouseup="PTZFoucusStop()">变焦+</el-button>
						<el-button @mousedown="PTZFoucusOut()" @mouseup="PTZFoucusStop()">变焦-</el-button>
						<el-button @mousedown="PTZIrisIn()" @mouseup="PTZIrisStop()">光圈+</el-button>
						<el-button @mousedown="PTZIrisOut()" @mouseup="PTZIrisStop()">光圈-</el-button>
					</el-form-item>

					<el-form-item label="回放码流">
						<el-select v-model="recordStreamtype" class="sel">
							<el-option :value="1" label="主码流"></el-option>
							<el-option :value="2" label="子码流"></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="回放时间">
						<el-date-picker v-model="starttime" type="datetime" placeholder="请选择开始时间"
							value-format="YYYY-MM-DD HH:mm:ss" /> &nbsp;
						<el-date-picker v-model="endtime" type="datetime" placeholder="请选择结束时间"
							value-format="YYYY-MM-DD HH:mm:ss" />
					</el-form-item>
					<el-form-item>
						<el-button @click="clickStartPlayback();">开始回放</el-button>
						<el-button @click="clickStopPlayback();">停止回放</el-button>
						<el-button @click="clickPause();">暂停</el-button>
						<el-button @click="clickResume();">恢复</el-button>
						<el-button @click="clickPlaySlow();">慢放</el-button>
						<el-button @click="clickPlayFast();">快放</el-button>
					</el-form-item>

				</el-form>

			</el-col>
			<el-col :span="16">
				<div id="divPlugin" class="plugin"></div>
			</el-col>
		</el-row>

	</div>

</template>

<script>
	import {
		useRoute
	} from 'vue-router';
	import robot from '@/api/robot/robot';
	import {
		parseTime
	} from '@/utils/hyc'

	let g_iWndIndex = 0; //可以不用设置这个变量，有窗口参数的接口中，不用传值，开发包会默认使用当前选择窗口
	let g_oLocalConfig = null; //本地配置	
	let ERROR_CODE_UNKNOWN = 1000; //未知错误
	let ERROR_CODE_NETWORKERROR = 1001; //网络错误
	let ERROR_CODE_PARAMERROR = 1002; //缺少插件元素		
	let ERROR_CODE_LOGIN_NOLOGIN = 2000; // 未登录
	let ERROR_CODE_LOGIN_REPEATLOGIN = 2001; //设备已登录，重复登录
	let ERROR_CODE_LOGIN_NOSUPPORT = 2002; //当前设备不支持Digest登录
	let ERROR_CODE_PLAY_PLUGININITFAIL = 3000; //插件初始化失败
	let ERROR_CODE_PLAY_NOREPEATPLAY = 3001; //当前窗口已经在预览
	let ERROR_CODE_PLAY_PLAYBACKABNORMAL = 3002; //回放异常
	let ERROR_CODE_PLAY_PLAYBACKSTOP = 3003; //回放停止
	let ERROR_CODE_PLAY_NOFREESPACE = 3004; //录像过程中，硬盘容量不足
	let ERROR_CODE_TALK_FAIL = 5000; //语音对讲失败
	let version = "V3.3.0build20230322";
	let g_szRecordType = "";
	let g_bPTZAuto = false;
	let g_iSearchTimes = 0;
	let g_iDownloadID = -1;
	let g_tDownloadProcess = 0;
	let g_tUpgrade = 0;
	let g_bEnableDraw = false;

	function showCBInfo(szInfo) {
		console.log(parseTime(new Date()) + ' ' + szInfo)
	}

	function showOPInfo(szInfo, status, xmlDoc) {
		console.log(parseTime(new Date()) + ' ' + szInfo)
	}

	export default {
		data() {
			return {
				starttime: null,
				endtime: null,
				recordStreamtype: 1,
				audiochannels: null,
				audiochannelsList: [],
				robotSelection: null,
				robotList: [],
				ip: null,
				ipList: [],
				channelsList: [],
				channels: null,
				streamtype: 1,
				volume: 50,
				loginip: null,
				port: null,
				username: null,
				password: null,
				videoNum: 0,
				timer: null,
				selectionRobotHardwareList: null
			}
		},
		mounted() {
			this.loadRobot()
			this.init()
		},
		methods: {
			playPluginDownload() {
				const a = document.createElement('a') // 创建一个<a></a>标签
				a.href = '/player.zip' // 给a标签的href属性值加上地址
				a.download = 'player.zip' // 设置下载文件文件名，这里加上.xlsx指定文件类型，pdf文件就指定.fpd即可
				a.style.display = 'none' // 障眼法藏起来a标签
				document.body.appendChild(a) // 将a标签追加到文档对象中
				a.click() // 模拟点击了a标签，会触发a标签的href的读取，浏览器就会自动下载了
				a.remove() // 一次性的，用完就删除a标签
			},
			robotSelectionChange(row) {
				let robot = this.robotList.filter(f => f.id == row)[0]
				let cameraInfo = null
				if (robot.robotHardwareList.length > 0) {
					this.selectionRobotHardwareList = robot.robotHardwareList
					robot.robotHardwareList.map(item => {
						// 6录像机 5可见光
						if (item.hardwareType == '6' || (!cameraInfo && item.hardwareType == '5')) {
							cameraInfo = item
						}
					})
				}
				console.log(cameraInfo)
				if (!cameraInfo) {
					return this.$modal.msgWarning('机器人没有配置可见光摄像机');
				}

				this.loginip = cameraInfo.ip
				this.port = cameraInfo.port
				this.username = cameraInfo.account
				this.password = cameraInfo.pwd

				this.clickLogout(cameraInfo.ip + '_' + cameraInfo.port)
			},
			loadRobot() {
				robot.getAll().then(res => {
					this.robotList = res.data
				})
			},
			init() {
				let that = this
				// 初始化插件参数及插入插件
				WebVideoCtrl.I_InitPlugin({
					bWndFull: true, //是否支持单窗口双击全屏，默认支持 true:支持 false:不支持
					iWndowType: 1,
					// aIframe: ["test"],
					cbSelWnd: function(xmlDoc) {
						g_iWndIndex = parseInt($(xmlDoc).find("SelectWnd").eq(0).text(), 10);
						var szInfo = "当前选择的窗口编号：" + g_iWndIndex;
						that.showCBInfo(szInfo);
					},
					cbDoubleClickWnd: function(iWndIndex, bFullScreen) {
						var szInfo = "当前放大的窗口编号：" + iWndIndex;
						if (!bFullScreen) {
							szInfo = "当前还原的窗口编号：" + iWndIndex;
						}
						that.showCBInfo(szInfo);
					},
					cbEvent: function(iEventType, iParam1, iParam2) {
						if (2 == iEventType) { // 回放正常结束
							that.showCBInfo("窗口" + iParam1 + "回放结束！");
						} else if (-1 == iEventType) {
							that.showCBInfo("设备" + iParam1 + "网络错误！");
						} else if (3001 == iEventType) {
							that.clickStopRecord(g_szRecordType, iParam1);
						}
					},
					cbInitPluginComplete: function() {
						WebVideoCtrl.I_InsertOBJECTPlugin("divPlugin").then(() => {
							// 检查插件是否最新
							WebVideoCtrl.I_CheckPluginVersion().then((bFlag) => {
								if (bFlag) {
									alert("检测到新的插件版本，请安装确认后下载的HCWebSDKPluginsUserSetup.exe");
								} else {
									that.changeWndNum(3)
									// that.clickSetLocalCfg()
								}

							});
						}, () => {
							alert("插件初始化失败，请确认是否已安装插件；如果未安装，请安装确认后下载的HCWebSDKPluginsUserSetup.exe");
							that.goOnLink()
						});
					}

				});



				// 窗口事件绑定
				$(window).bind({
					resize: function() {
						//WebVideoCtrl.I_Resize($("body").width(), $("body").height());
					}
				});

			},
			goOnLink() {
				const a = document.createElement('a') // 创建一个<a></a>标签
				a.href = '/HCWebSDKPluginsUserSetup.exe' // 给a标签的href属性值加上地址
				a.download = 'HCWebSDKPluginsUserSetup.exe' // 设置下载文件文件名，这里加上.xlsx指定文件类型，pdf文件就指定.fpd即可
				a.style.display = 'none' // 障眼法藏起来a标签
				document.body.appendChild(a) // 将a标签追加到文档对象中
				a.click() // 模拟点击了a标签，会触发a标签的href的读取，浏览器就会自动下载了
				a.remove() // 一次性的，用完就删除a标签

			},
			// 显示操作信息			
			showOPInfo(szInfo, status, xmlDoc) {
				console.log(parseTime(new Date()) + ' ' + szInfo)
			},
			// 显示回调信息			
			showCBInfo(szInfo) {
				console.log(parseTime(new Date()) + ' ' + szInfo)
			},
			// 获取窗口尺寸			
			getWindowSize() {
				var nWidth = $(this).width() + $(this).scrollLeft(),
					nHeight = $(this).height() + $(this).scrollTop();

				return {
					width: nWidth,
					height: nHeight
				};
			},
			// 设置本地参数			
			clickSetLocalCfg() {
				// WebVideoCtrl.I_GetLocalCfg().then(res=>{
				// 	console.log(res)
				// })

				g_oLocalConfig = {
					"captureFileFormat": "0", //抓图格式，0：JPEG，1：BMP
					"capturePath": "C:\\Users\\<USER>\\HCWebSDKPlugin\\CaptureFiles", //预览抓图保存路径
					"downloadPath": "C:\\Users\\<USER>\\HCWebSDKPlugin\\DownloadFiles", //回放下载保存路径
					"ivsMode": "0", //规则信息	
					"playbackFilePath": "C:\\Users\\<USER>\\HCWebSDKPlugin\\PlaybackFiles", //回放剪辑保存路径
					"playbackPicPath": "C:\\Users\\<USER>\\HCWebSDKPlugin\\PlaybackPics", //回放抓图保存路径
					"protocolType": "1", //协议类型，1：TCP，2：UDP
					"recordPath": "C:\\Users\\<USER>\\Desktop", //预览录像保存路径
					"secretKey": "", //码流秘钥
					"buffNumberType": "", //播放性能，0：最短延时，1：实时性好，2：均衡，3：流畅性好
					"playWndType": "0", //图像尺寸，0：充满，1：4:3，2：16:9 
					"packgeSize": "0", //录像文件打包大小，0：256M，1：512M，2：1G
					"osdPosInfo": "0" //叠加POS信息，0：禁用，1：启用
				}

				WebVideoCtrl.I_SetLocalCfg(g_oLocalConfig).then(() => {
					showOPInfo("本地配置设置成功！");
				}, (oError) => {
					var szInfo = "本地配置设置失败！";
					showOPInfo(szInfo, oError.errorCode, oError.errorMsg);
				});
			},
			// 窗口分割数
			changeWndNum(iType) {
				if ("1*2" === iType || "2*1" === iType) {
					WebVideoCtrl.I_ArrangeWindow(iType).then(() => {
						showOPInfo("窗口分割成功！");
					}, (oError) => {
						var szInfo = "窗口分割失败！";
						showOPInfo(szInfo, oError.errorCode, oError.errorMsg);
					});
				} else {
					iType = parseInt(iType, 10);
					WebVideoCtrl.I_ChangeWndNum(iType).then(() => {
						showOPInfo("窗口分割成功！");
					}, (oError) => {
						var szInfo = "窗口分割失败！";
						showOPInfo(szInfo, oError.errorCode, oError.errorMsg);
					});
				}
			},
			// 登录			
			clickLogin(szProtoType) {
				let that = this
				let szIP = that.loginip
				let szPort = that.port
				let szUsername = that.username
				let szPassword = that.password

				if (!szIP || !szPort || !szUsername || !szPassword) {
					return this.$modal.msgWarning('摄像机配置信息不完整');
				}

				var szDeviceIdentify = szIP + "_" + szPort;

				WebVideoCtrl.I_Login(szIP, szProtoType, szPort, szUsername, szPassword, {
					success: function(xmlDoc) {
						that.showOPInfo(szDeviceIdentify + " 登录成功！");
						that.ipList.push(szDeviceIdentify)
						that.ip = szDeviceIdentify

						that.getChannelInfo();
					},
					error: function(oError) {
						if (ERROR_CODE_LOGIN_REPEATLOGIN === oError.errorCode) {
							that.showOPInfo(szDeviceIdentify + " 已登录过！");

						} else {
							if (oError.errorCode === 401) {
								that.showOPInfo(szDeviceIdentify + " 登录失败，已自动切换认证方式！");
							} else {
								that.showOPInfo(szDeviceIdentify + " 登录失败！", oError.errorCode, oError
								.errorMsg);
							}
						}
					}
				});
			},
			// 退出			
			clickLogout(val) {
				let that = this
				var szDeviceIdentify = val || this.ip

				if (null == szDeviceIdentify) {
					return;
				}

				WebVideoCtrl.I_Logout(szDeviceIdentify).then(() => {
					that.ip = null
					showOPInfo(szDeviceIdentify + " " + "退出成功！");
				}, () => {
					showOPInfo(szDeviceIdentify + " " + "退出失败！");
				});
			},
			// 获取设备信息			
			clickGetDeviceInfo() {
				var szDeviceIdentify = this.ip;

				if (null == szDeviceIdentify) {
					return;
				}

				WebVideoCtrl.I_GetDeviceInfo(szDeviceIdentify, {
					success: function(xmlDoc) {
						var arrStr = [];
						arrStr.push("设备名称：" + $(xmlDoc).find("deviceName").eq(0).text() + "\r\n");
						arrStr.push("设备ID：" + $(xmlDoc).find("deviceID").eq(0).text() + "\r\n");
						arrStr.push("型号：" + $(xmlDoc).find("model").eq(0).text() + "\r\n");
						arrStr.push("设备序列号：" + $(xmlDoc).find("serialNumber").eq(0).text() + "\r\n");
						arrStr.push("MAC地址：" + $(xmlDoc).find("macAddress").eq(0).text() + "\r\n");
						arrStr.push("主控版本：" + $(xmlDoc).find("firmwareVersion").eq(0).text() + " " + $(xmlDoc)
							.find("firmwareReleasedDate").eq(0).text() + "\r\n");
						arrStr.push("编码版本：" + $(xmlDoc).find("encoderVersion").eq(0).text() + " " + $(xmlDoc)
							.find(
								"encoderReleasedDate").eq(0).text() + "\r\n");

						showOPInfo(szDeviceIdentify + " 获取设备信息成功！");
						alert(arrStr.join(""));
					},
					error: function(oError) {
						showOPInfo(szDeviceIdentify + " 获取设备信息失败！", oError.errorCode, oError.errorMsg);
					}
				});
			},
			// 获取通道			
			getChannelInfo() {
				let that = this
				let szDeviceIdentify = this.ip
				let oSel = this.channels
				this.channelsList = []

				if (null == szDeviceIdentify) {
					return;
				}

				// 模拟通道
				WebVideoCtrl.I_GetAnalogChannelInfo(szDeviceIdentify, {
					success: function(xmlDoc) {
						var oChannels = $(xmlDoc).find("VideoInputChannel");

						$.each(oChannels, function(i) {
							var id = $(this).find("id").eq(0).text(),
								name = $(this).find("name").eq(0).text();
							if ("" == name) {
								name = "Camera " + (i < 9 ? "0" + (i + 1) : (i + 1));
							}
							that.channelsList.push({
								id: id,
								name: name
							})
						});
						that.channels = '1'
						that.showOPInfo(szDeviceIdentify + " 获取模拟通道成功！");
					},
					error: function(oError) {
						that.showOPInfo(szDeviceIdentify + " 获取模拟通道失败！", oError.errorCode, oError.errorMsg);
					}
				});
				// 数字通道
				WebVideoCtrl.I_GetDigitalChannelInfo(szDeviceIdentify, {
					success: function(xmlDoc) {
						var oChannels = $(xmlDoc).find("InputProxyChannelStatus");

						$.each(oChannels, function(i) {
							var id = $(this).find("id").eq(0).text(),
								name = $(this).find("name").eq(0).text(),
								ipAddress = $(this).find("ipAddress").eq(0).text(),
								online = $(this).find("online").eq(0).text();

							let hardwares = that.selectionRobotHardwareList.filter(f => f.ip ==
								ipAddress)

							if ("false" == online || hardwares.length < 1) { // 过滤禁用的数字通道
								return true;
							}
							if ("" == name) {
								name = "IPCamera " + (i < 9 ? "0" + (i + 1) : (i + 1));
							}

							that.channelsList.push({
								id: id,
								name: name
							})

						});
						that.channels = that.channelsList[0].id
						showOPInfo(szDeviceIdentify + " 获取数字通道成功！");
					},
					error: function(oError) {
						showOPInfo(szDeviceIdentify + " 获取数字通道失败！", oError.errorCode, oError.errorMsg);
					}
				});
			},
			clickStartRealPlay(iStreamType) {
				let that = this
				let oWndInfo = WebVideoCtrl.I_GetWindowStatus(g_iWndIndex)
				let szDeviceIdentify = this.ip
				let iChannelID = this.channels
				let szInfo = "";

				if (null == szDeviceIdentify) {
					return;
				}
				var startRealPlay = function() {
					WebVideoCtrl.I_StartRealPlay(szDeviceIdentify, {
						iChannelID: iChannelID,
						success: function() {
							szInfo = "开始预览成功！";
							that.showOPInfo(szDeviceIdentify + " " + szInfo);
						},
						error: function(oError) {
							that.showOPInfo(szDeviceIdentify + " 开始预览失败！", oError.errorCode, oError
								.errorMsg);
						}
					});
				};

				if (oWndInfo != null) { // 已经在播放了，先停止
					WebVideoCtrl.I_Stop({
						success: function() {
							startRealPlay();
						}
					});
				} else {
					startRealPlay();
				}
			},
			destroyPlugin() {
				let that = this
				WebVideoCtrl.I_DestroyPlugin().then(() => {
					// 获取按钮元素
					document.querySelectorAll('input[type="button"]').forEach(button => {
						button.disabled = true;
					});
					document.querySelectorAll('a').forEach(link => {
						link.removeAttribute('onclick'); // 移除内联事件
						link.removeAttribute('href');
						link.setAttribute('aria-disabled', 'true'); // 辅助技术可识别
						link.style.cursor = 'default'; // 移除手型光标
						// 创建一个无事件的新节点替换
						const newLink = link.cloneNode(false);
						newLink.textContent = link.textContent;
						link.parentNode.replaceChild(newLink, link);
					});
					// 销毁后不能再预览了
					that.showOPInfo('销毁成功！')
				}).catch(() => {
					that.showOPInfo('销毁失败！')
				})

			},
			showPlugin() {
				WebVideoCtrl.I_ShowPlugin().then(() => {
					showOPInfo('展示成功！')
				}).catch(() => {
					showOPInfo('展示失败！')
				})

			},
			hidPlugin() {
				let that = this
				WebVideoCtrl.I_HidPlugin().then(() => {
					that.showOPInfo('隐藏成功！')
				}).catch(() => {
					that.showOPInfo('隐藏失败！')
				})

			},
			setTextOverlay() {
				var szDeviceIdentify = $("#ip").val();
				var szInfo = "";
				var that = this;
				var iChannelID = parseInt($("#channels").val(), 10);
				var szUrl = "ISAPI/System/Video/inputs/channels/" + iChannelID + "/overlays";
				WebVideoCtrl.I_GetTextOverlay(szUrl, szDeviceIdentify, {
					success: function(data) {
						$(data).find("TextOverlay").eq(0).find("displayText").eq(0).text("我tet");
						$(data).find("TextOverlay").eq(0).find("positionX").eq(0).text("20");
						$(data).find("TextOverlay").eq(0).find("positionY").eq(0).text("30");
						var xmldoc = toXMLStr(data);
						var newOptions = {
							type: "PUT",
							data: xmldoc,
							success: function() {
								szInfo = "绘制osd信息成功";
								showOPInfo(szDeviceIdentify + " " + szInfo);
							},
							error: function(oError) {
								showOPInfo(szDeviceIdentify + " 设置osd信息失败！", oError.errorCode, oError
									.errorMsg);
							}
						};

						WebVideoCtrl.I_SendHTTPRequest(szDeviceIdentify, szUrl, newOptions);
					},
					error: function(oError) {
						showOPInfo(szDeviceIdentify + " 设置osd信息失败！", oError.errorCode, oError.errorMsg);
					}
				});
			},
			// 关闭全部视频播放			
			stopAllPlay() {
				WebVideoCtrl.I_StopAllPlay().then(() => {
					showOPInfo('关闭全部视频播放成功！')
				}).catch(() => {
					showOPInfo('失败！')
				})
			},
			// 停止预览			
			clickStopRealPlay() {
				let that = this
				let oWndInfo = WebVideoCtrl.I_GetWindowStatus(g_iWndIndex)
				let szInfo = "";
				console.log(oWndInfo)
				if (oWndInfo != null) {
					WebVideoCtrl.I_Stop({
						success: function() {
							szInfo = "停止预览成功！";
							that.showOPInfo(oWndInfo.szDeviceIdentify + " " + szInfo);
						},
						error: function(oError) {
							that.showOPInfo(szDeviceIdentify + " 停止预览失败！", oError.errorCode, oError.errorMsg);
						}
					});
				}
			},
			// 打开声音			
			clickOpenSound() {
				var oWndInfo = WebVideoCtrl.I_GetWindowStatus(g_iWndIndex),
					szInfo = "";

				if (oWndInfo != null) {
					// var allWndInfo = WebVideoCtrl.I_GetWindowStatus();
					// // 循环遍历所有窗口，如果有窗口打开了声音，先关闭
					// for (var i = 0, iLen = allWndInfo.length; i < iLen; i++) {
					//     oWndInfo = allWndInfo[i];
					//     if (oWndInfo.bSound) {
					//         WebVideoCtrl.I_CloseSound(oWndInfo.iIndex);
					//         break;
					//     }
					// }

					WebVideoCtrl.I_OpenSound().then(() => {
						showOPInfo(oWndInfo.szDeviceIdentify + " " + "打开声音成功！");
					}, (oError) => {
						var szInfo = " 打开声音失败！";
						showOPInfo(oWndInfo.szDeviceIdentify + szInfo, oError.errorCode, oError.errorMsg);
					});
				}
			},
			// 关闭声音			
			clickCloseSound() {
				var oWndInfo = WebVideoCtrl.I_GetWindowStatus(g_iWndIndex),
					szInfo = "";

				if (oWndInfo != null) {
					WebVideoCtrl.I_CloseSound().then(() => {
						showOPInfo(oWndInfo.szDeviceIdentify + " " + "关闭声音成功！");
					}, (oError) => {
						var szInfo = " 关闭声音失败！";
						showOPInfo(oWndInfo.szDeviceIdentify + szInfo, oError.errorCode, oError.errorMsg);
					});
				}
			},
			// 设置音量			
			clickSetVolume() {
				var oWndInfo = WebVideoCtrl.I_GetWindowStatus(g_iWndIndex)
				let iVolume = this.volume
				let szInfo = "";

				if (oWndInfo != null) {
					WebVideoCtrl.I_SetVolume(iVolume).then(() => {
						showOPInfo(oWndInfo.szDeviceIdentify + " " + "设置音量成功");
					}, (oError) => {
						var szInfo = " 设置音量失败！";
						showOPInfo(oWndInfo.szDeviceIdentify + szInfo, oError.errorCode, oError.errorMsg);
					});
				}
			},
			// 抓图			
			clickCapturePic(szType) {
				var oWndInfo = WebVideoCtrl.I_GetWindowStatus(g_iWndIndex),
					szInfo = "";

				if (oWndInfo != null) {
					var oLocalConfig = WebVideoCtrl.I_GetLocalCfg();
					var szCaptureFileFormat = "0";
					if (oLocalConfig) {
						szCaptureFileFormat = oLocalConfig.captureFileFormat;
					}

					var szChannelID = $("#channels").val();
					var szPicName = oWndInfo.szDeviceIdentify + "_" + szChannelID + "_" + new Date().getTime();
					//如果是回放抓图，需要增加如下前缀："playback_"
					if ("playback" === szType) {
						szPicName = "playback_" + oWndInfo.szDeviceIdentify + "_" + szChannelID + "_" + new Date()
							.getTime();
					}

					szPicName += ("0" === szCaptureFileFormat) ? ".jpg" : ".bmp";
					WebVideoCtrl.I_CapturePic(szPicName, {}).then(function() {
						szInfo = "抓图成功！";
						showOPInfo(oWndInfo.szDeviceIdentify + " " + szInfo);
					}, function(oError) {
						szInfo = " 抓图失败！";
						showOPInfo(oWndInfo.szDeviceIdentify + szInfo, oError.errorCode, oError.errorMsg);
					});
				}
			},
			// 抓图				
			clickCapturePicData() {
				var oWndInfo = WebVideoCtrl.I_GetWindowStatus(g_iWndIndex),
					szInfo = "";
				if (oWndInfo != null) {
					WebVideoCtrl.I_CapturePicData().then(function(res) {
						// console.log(res);
						const data = window.atob(res)
						const ia = new Uint8Array(data.length)
						for (let i = 0; i < data.length; i++) {
							ia[i] = data.charCodeAt(i)
						}
						const blob = new Blob([ia], {
							type: 'image/png'
						})
						const downloadUrl = URL.createObjectURL(blob);
						const link = document.createElement('a');
						link.href = downloadUrl;
						link.download = 'image_' + Date.now() + '.png';
						link.click();
						URL.revokeObjectURL(downloadUrl);

						szInfo = "抓图上传成功！";
						showOPInfo(oWndInfo.szDeviceIdentify + " " + szInfo);
					}, function() {
						szInfo = "抓图失败！";
						showOPInfo(oWndInfo.szDeviceIdentify + " " + szInfo);
					});
				}
			},
			// 开始录像			
			clickStartRecord(szType) {
				let that = this
				that.videoNum = 1;
				var oWndInfo = WebVideoCtrl.I_GetWindowStatus(g_iWndIndex),
					szInfo = "";

				g_szRecordType = szType;

				if (oWndInfo != null) {
					var szChannelID = $("#channels").val(),
						szFileName = oWndInfo.szDeviceIdentify + "_" + szChannelID + "_" + new Date().getTime();

					WebVideoCtrl.I_StartRecord(szFileName, {
						success: function() {
							if ('realplay' === szType) {
								that.videoNumPlus()
								szInfo = "开始录像成功！";
							} else if ('playback' === szType) {
								szInfo = "开始剪辑成功！";
							}
							showOPInfo(oWndInfo.szDeviceIdentify + " " + szInfo);
						},
						error: function(oError) {
							if ('realplay' === szType) {
								szInfo = " 开始录像失败！";
							} else if ('playback' === szType) {
								szInfo = " 开始剪辑失败！";
							}
							showOPInfo(oWndInfo.szDeviceIdentify + szInfo, oError.errorCode, oError.errorMsg);
						}
					});
				}
			},
			videoNumPlus() {
				this.timer = setTimeout(() => {
					if (this.videoNum != 0) {
						this.videoNum++;
						this.videoNumPlus()
					}
				}, 1000)
			},
			// 停止录像
			clickStopRecord(szType, iWndIndex) {
				let that = this
				if ("undefined" === typeof iWndIndex) {
					iWndIndex = g_iWndIndex;
				}
				var oWndInfo = WebVideoCtrl.I_GetWindowStatus(iWndIndex),
					szInfo = "";

				if (oWndInfo != null) {
					WebVideoCtrl.I_StopRecord({
						success: function() {
							if ('realplay' === szType) {
								that.videoNum = 0;
								szInfo = "停止录像成功！";
							} else if ('playback' === szType) {
								szInfo = "停止剪辑成功！";
							}
							that.showOPInfo(oWndInfo.szDeviceIdentify + " " + szInfo);
						},
						error: function(oError) {
							if ('realplay' === szType) {
								szInfo = "停止录像失败！";
							} else if ('playback' === szType) {
								szInfo = "停止剪辑失败！";
							}
							that.showOPInfo(szDeviceIdentify + szInfo, oError.errorCode, oError.errorMsg);
						}
					});
				}
			},
			// 获取对讲通道			
			clickGetAudioInfo() {
				let that = this
				var szDeviceIdentify = this.ip

				if (null == szDeviceIdentify) {
					return;
				}
				that.audiochannelsList = []
				WebVideoCtrl.I_GetAudioInfo(szDeviceIdentify, {
					success: function(xmlDoc) {
						var oAudioChannels = $(xmlDoc).find("TwoWayAudioChannel")
						$.each(oAudioChannels, function() {
							var id = $(this).find("id").eq(0).text();
							that.audiochannelsList.push({
								id: id
							})
						});
						that.audiochannels = that.audiochannelsList[0].id
						showOPInfo(szDeviceIdentify + " 获取对讲通道成功！");
					},
					error: function(oError) {
						showOPInfo(szDeviceIdentify + " 获取对讲通道失败！", oError.errorCode, oError.errorMsg);
					}
				});
			},

			// 开始对讲			
			clickStartVoiceTalk() {
				var szDeviceIdentify = this.ip
				let iAudioChannel = this.audiochannels
				let szInfo = "";

				if (null == szDeviceIdentify) {
					return;
				}

				if (isNaN(iAudioChannel)) {
					this.$modal.msgWarning("请选择对讲通道！");
					return;
				}
				WebVideoCtrl.I_StartVoiceTalk(szDeviceIdentify, iAudioChannel).then(() => {
					szInfo = "开始对讲成功！";
					showOPInfo(szDeviceIdentify + " " + szInfo);
				}, (oError) => {
					var szInfo = " 开始对讲失败！";
					showOPInfo(szDeviceIdentify + szInfo, oError.errorCode, oError.errorMsg);
				});
			},

			// 停止对讲			
			clickStopVoiceTalk() {
				var szDeviceIdentify = this.ip
				WebVideoCtrl.I_StopVoiceTalk().then(() => {
					szInfo = "停止对讲成功！";
					showOPInfo(szDeviceIdentify + " " + szInfo);
				}, (oError) => {
					var szInfo = " 停止对讲失败！";
					showOPInfo(szDeviceIdentify + szInfo, oError.errorCode, oError.errorMsg);
				});
			},

			// 全屏			
			clickFullScreen() {
				WebVideoCtrl.I_FullScreen(true).then(() => {
					showOPInfo("全屏成功");
				}, (oError) => {
					showOPInfo("全屏失败！", oError.errorCode, oError.errorMsg);
				});
			},
			// PTZ控制 9为自动，1,2,3,4,5,6,7,8为方向PTZ			
			mouseDownPTZControl(iPTZIndex) {
				var oWndInfo = WebVideoCtrl.I_GetWindowStatus(g_iWndIndex)
				let iPTZSpeed = 4

				if (oWndInfo != null) {
					if (9 == iPTZIndex && g_bPTZAuto) {
						iPTZSpeed = 0; // 自动开启后，速度置为0可以关闭自动
					} else {
						g_bPTZAuto = false; // 点击其他方向，自动肯定会被关闭
					}

					WebVideoCtrl.I_PTZControl(iPTZIndex, false, {
						iPTZSpeed: iPTZSpeed,
						success: function(xmlDoc) {
							if (9 == iPTZIndex && g_bPTZAuto) {
								showOPInfo(oWndInfo.szDeviceIdentify + " 停止云台成功！");
							} else {
								showOPInfo(oWndInfo.szDeviceIdentify + " 开启云台成功！");
							}
							if (9 == iPTZIndex) {
								g_bPTZAuto = !g_bPTZAuto;
							}
						},
						error: function(oError) {
							showOPInfo(oWndInfo.szDeviceIdentify + " 开启云台失败！", oError.errorCode, oError
								.errorMsg);
						}
					});
				}
			},
			// 方向PTZ停止			
			mouseUpPTZControl() {
				var oWndInfo = WebVideoCtrl.I_GetWindowStatus(g_iWndIndex);

				if (oWndInfo != null) {
					WebVideoCtrl.I_PTZControl(1, true, {
						success: function(xmlDoc) {
							showOPInfo(oWndInfo.szDeviceIdentify + " 停止云台成功！");
						},
						error: function(oError) {
							showOPInfo(oWndInfo.szDeviceIdentify + " 停止云台失败！", oError.errorCode, oError
								.errorMsg);
						}
					});
				}
			},
			// 设置预置点			
			clickSetPreset() {
				var oWndInfo = WebVideoCtrl.I_GetWindowStatus(g_iWndIndex),
					iPresetID = parseInt($("#preset").val(), 10);

				if (oWndInfo != null) {
					WebVideoCtrl.I_SetPreset(iPresetID, {
						success: function(xmlDoc) {
							showOPInfo(oWndInfo.szDeviceIdentify + " 设置预置点成功！");
						},
						error: function(oError) {
							showOPInfo(oWndInfo.szDeviceIdentify + " 设置预置点失败！", oError.errorCode, oError
								.errorMsg);
						}
					});
				}
			},
			// 调用预置点			
			clickGoPreset() {
				var oWndInfo = WebVideoCtrl.I_GetWindowStatus(g_iWndIndex),
					iPresetID = parseInt($("#preset").val(), 10);

				if (oWndInfo != null) {
					WebVideoCtrl.I_GoPreset(iPresetID, {
						success: function(xmlDoc) {
							showOPInfo(oWndInfo.szDeviceIdentify + " 调用预置点成功！");
						},
						error: function(oError) {
							showOPInfo(oWndInfo.szDeviceIdentify + " 调用预置点失败！", oError.errorCode, oError
								.errorMsg);
						}
					});
				}
			},
			// 开始回放			
			clickStartPlayback() {
				var oWndInfo = WebVideoCtrl.I_GetWindowStatus(g_iWndIndex)
				let szDeviceIdentify = this.ip
				let iStreamType = this.recordStreamtype
				let iChannelID = this.channels
				let szStartTime = this.starttime
				let szEndTime = this.endtime
				let szInfo = ""

				if (null == szDeviceIdentify) {
					return;
				}
				console.log(szEndTime)
				if (!szStartTime || !szEndTime) {
					this.$modal.msgWarning("开始或结束时间不能为空！");
					return;
				}
				if (Date.parse(szEndTime.replace(/-/g, "/")) - Date.parse(szStartTime.replace(/-/g, "/")) < 0) {
					this.$modal.msgWarning("开始时间大于结束时间");
					return;
				}

				var startPlayback = function() {
					WebVideoCtrl.I_StartPlayback(szDeviceIdentify, {
						iStreamType: iStreamType,
						iChannelID: iChannelID,
						szStartTime: szStartTime,
						szEndTime: szEndTime,
						success: function() {
							szInfo = "开始回放成功！";
							showOPInfo(szDeviceIdentify + " " + szInfo);
						},
						error: function(oError) {
							szInfo = "开始回放失败！";
							showOPInfo(szDeviceIdentify + szInfo, oError.errorCode, oError.errorMsg);
						}
					});
				};

				if (oWndInfo != null) { // 已经在播放了，先停止
					WebVideoCtrl.I_Stop({
						success: function() {
							startPlayback();
						}
					});
				} else {
					startPlayback();
				}
			},
			// 停止回放			
			clickStopPlayback() {
				var oWndInfo = WebVideoCtrl.I_GetWindowStatus(g_iWndIndex),
					szInfo = "";

				if (oWndInfo != null) {
					WebVideoCtrl.I_Stop({
						success: function() {
							szInfo = "停止回放成功！";
							showOPInfo(oWndInfo.szDeviceIdentify + " " + szInfo);
						},
						error: function(oError) {
							szInfo = "停止回放失败！";
							showOPInfo(szDeviceIdentify + szInfo, oError.errorCode, oError.errorMsg);
						}
					});
				}
			},
			// 暂停			
			clickPause() {
				var oWndInfo = WebVideoCtrl.I_GetWindowStatus(g_iWndIndex),
					szInfo = "";

				if (oWndInfo != null) {
					WebVideoCtrl.I_Pause({
						success: function() {
							szInfo = "暂停成功！";
							showOPInfo(oWndInfo.szDeviceIdentify + " " + szInfo);
						},
						error: function(oError) {
							szInfo = "暂停失败！";
							showOPInfo(oWndInfo.szDeviceIdentify + szInfo, oError.errorCode, oError.errorMsg);
						}
					});
				}
			},
			// 恢复			
			clickResume() {
				var oWndInfo = WebVideoCtrl.I_GetWindowStatus(g_iWndIndex),
					szInfo = "";

				if (oWndInfo != null) {
					WebVideoCtrl.I_Resume({
						success: function() {
							szInfo = "恢复成功！";
							showOPInfo(oWndInfo.szDeviceIdentify + " " + szInfo);
						},
						error: function(oError) {
							szInfo = "恢复失败！";
							showOPInfo(oWndInfo.szDeviceIdentify + szInfo, oError.errorCode, oError.errorMsg);
						}
					});
				}
			},
			// 慢放			
			clickPlaySlow() {
				var oWndInfo = WebVideoCtrl.I_GetWindowStatus(g_iWndIndex),
					szInfo = "";

				if (oWndInfo != null) {
					WebVideoCtrl.I_PlaySlow({
						success: function() {
							szInfo = "慢放成功！";
							showOPInfo(oWndInfo.szDeviceIdentify + " " + szInfo);
						},
						error: function(oError) {
							szInfo = "慢放失败！";
							showOPInfo(oWndInfo.szDeviceIdentify + szInfo, oError.errorCode, oError.errorMsg);
						}
					});
				}
			},
			// 快放
			clickPlayFast() {
				var oWndInfo = WebVideoCtrl.I_GetWindowStatus(g_iWndIndex),
					szInfo = "";

				if (oWndInfo != null) {
					WebVideoCtrl.I_PlayFast({
						success: function() {
							szInfo = "快放成功！";
							showOPInfo(oWndInfo.szDeviceIdentify + " " + szInfo);
						},
						error: function(oError) {
							szInfo = "快放失败！";
							showOPInfo(oWndInfo.szDeviceIdentify + szInfo, oError.errorCode, oError.errorMsg);
						}
					});
				}
			},
			PTZZoomIn() {
				var oWndInfo = WebVideoCtrl.I_GetWindowStatus(g_iWndIndex);

				if (oWndInfo != null) {
					WebVideoCtrl.I_PTZControl(10, false, {
						iWndIndex: g_iWndIndex,
						success: function(xmlDoc) {
							showOPInfo(oWndInfo.szDeviceIdentify + " 调焦+成功！");
						},
						error: function(oError) {
							showOPInfo(oWndInfo.szDeviceIdentify + "  调焦+失败！", oError.errorCode, oError
								.errorMsg);
						}
					});
				}
			},
			PTZZoomout() {
				var oWndInfo = WebVideoCtrl.I_GetWindowStatus(g_iWndIndex);

				if (oWndInfo != null) {
					WebVideoCtrl.I_PTZControl(11, false, {
						iWndIndex: g_iWndIndex,
						success: function(xmlDoc) {
							showOPInfo(oWndInfo.szDeviceIdentify + " 调焦-成功！");
						},
						error: function(oError) {
							showOPInfo(oWndInfo.szDeviceIdentify + "  调焦-失败！", oError.errorCode, oError
								.errorMsg);
						}
					});
				}
			},
			PTZZoomStop() {
				var oWndInfo = WebVideoCtrl.I_GetWindowStatus(g_iWndIndex);

				if (oWndInfo != null) {
					WebVideoCtrl.I_PTZControl(11, true, {
						iWndIndex: g_iWndIndex,
						success: function(xmlDoc) {
							showOPInfo(oWndInfo.szDeviceIdentify + " 调焦停止成功！");
						},
						error: function(oError) {
							showOPInfo(oWndInfo.szDeviceIdentify + "  调焦停止失败！", oError.errorCode, oError
								.errorMsg);
						}
					});
				}
			},
			PTZFocusIn() {
				var oWndInfo = WebVideoCtrl.I_GetWindowStatus(g_iWndIndex);

				if (oWndInfo != null) {
					WebVideoCtrl.I_PTZControl(12, false, {
						iWndIndex: g_iWndIndex,
						success: function(xmlDoc) {
							showOPInfo(oWndInfo.szDeviceIdentify + " 聚焦+成功！");
						},
						error: function(oError) {
							showOPInfo(oWndInfo.szDeviceIdentify + "  聚焦+失败！", oError.errorCode, oError
								.errorMsg);
						}
					});
				}
			},
			PTZFoucusOut() {
				var oWndInfo = WebVideoCtrl.I_GetWindowStatus(g_iWndIndex);

				if (oWndInfo != null) {
					WebVideoCtrl.I_PTZControl(13, false, {
						iWndIndex: g_iWndIndex,
						success: function(xmlDoc) {
							showOPInfo(oWndInfo.szDeviceIdentify + " 聚焦-成功！");
						},
						error: function(oError) {
							showOPInfo(oWndInfo.szDeviceIdentify + "  聚焦-失败！", oError.errorCode, oError
								.errorMsg);
						}
					});
				}
			},
			PTZFoucusStop() {
				var oWndInfo = WebVideoCtrl.I_GetWindowStatus(g_iWndIndex);

				if (oWndInfo != null) {
					WebVideoCtrl.I_PTZControl(12, true, {
						iWndIndex: g_iWndIndex,
						success: function(xmlDoc) {
							showOPInfo(oWndInfo.szDeviceIdentify + " 聚焦停止成功！");
						},
						error: function(oError) {
							showOPInfo(oWndInfo.szDeviceIdentify + "  聚焦停止失败！", oError.errorCode, oError
								.errorMsg);
						}
					});
				}
			},
			PTZIrisIn() {
				var oWndInfo = WebVideoCtrl.I_GetWindowStatus(g_iWndIndex);

				if (oWndInfo != null) {
					WebVideoCtrl.I_PTZControl(14, false, {
						iWndIndex: g_iWndIndex,
						success: function(xmlDoc) {
							showOPInfo(oWndInfo.szDeviceIdentify + " 光圈+成功！");
						},
						error: function(oError) {
							showOPInfo(oWndInfo.szDeviceIdentify + "  光圈+失败！", oError.errorCode, oError
								.errorMsg);
						}
					});
				}
			},
			PTZIrisOut() {
				var oWndInfo = WebVideoCtrl.I_GetWindowStatus(g_iWndIndex);

				if (oWndInfo != null) {
					WebVideoCtrl.I_PTZControl(15, false, {
						iWndIndex: g_iWndIndex,
						success: function(xmlDoc) {
							showOPInfo(oWndInfo.szDeviceIdentify + " 光圈-成功！");
						},
						error: function(oError) {
							showOPInfo(oWndInfo.szDeviceIdentify + "  光圈-失败！", oError.errorCode, oError
								.errorMsg);
						}
					});
				}
			},
			PTZIrisStop() {
				var oWndInfo = WebVideoCtrl.I_GetWindowStatus(g_iWndIndex);

				if (oWndInfo != null) {
					WebVideoCtrl.I_PTZControl(14, true, {
						iWndIndex: g_iWndIndex,
						success: function(xmlDoc) {
							showOPInfo(oWndInfo.szDeviceIdentify + " 光圈停止成功！");
						},
						error: function(oError) {
							showOPInfo(oWndInfo.szDeviceIdentify + "  光圈停止失败！", oError.errorCode, oError
								.errorMsg);
						}
					});
				}
			}
		}
	}
</script>

<style scoped>
	.PTZ .el-input {
		width: 160px;
	}

	.PTZ .el-select {
		width: 160px;
	}

	.plugin {
		width: 100%;
		height: 700px;
	}

	.PTZ_direction .el-button {
		width: 60px;
		margin: 0;
	}
</style>