import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/hyc";


// 查询模版列表
export function listTemplateConfig(query) {
    return request({
      url: '/push/template/config/list',
      method: 'get',
      params: query
    })
}

// 查询模版详细
export function getTemplateConfig(id) {
    return request({
      url: '/push/template/config/getById/' + parseStrEmpty(id),
      method: 'get'
    })
  }



// 新增模版
export function addTemplateConfig(data) {
    return request({
      url: '/push/template/config/add',
      method: 'post',
      data: data
    })
  }
  
  // 修改模版
  export function updateTemplateConfig(data) {
    return request({
      url: '/push/template/config/update',
      method: 'put',
      data: data
    })
  }

// 删除模版
export function delTemplateConfig(id) {
    return request({
      url: '/push/template/config/delete/' + id,
      method: 'delete'
    })
  }

  // 查询模版参数
export function getConfigParam(pushType) {
  return request({
    url: '/push/template/config/getConfigParam/' + parseStrEmpty(pushType),
    method: 'get'
  })
}

  // 查询模版
  export function getByPushType(pushType) {
    return request({
      url: '/push/template/config/getByPushType/' + parseStrEmpty(pushType),
      method: 'get'
    })
  }
