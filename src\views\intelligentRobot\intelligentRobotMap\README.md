# 智能机器人地图组件

## 概述

本组件实现了智能机器人地图显示功能，包括地图显示、WebSocket实时通信、机器人位置跟踪、障碍物显示、AI报警等功能。

## 功能特性

### 核心功能
1. **地图显示** - 显示SVG格式的地图
2. **WebSocket连接** - 与后端建立实时通信
3. **机器人跟踪** - 实时显示机器人位置和状态
4. **障碍物显示** - 显示地图上的障碍点
5. **AI报警** - 显示AI检测到的异常情况
6. **点位管理** - 可选择性显示不同类型的点位

### 交互功能
1. **地图缩放** - 鼠标滚轮缩放地图
2. **地图拖拽** - 鼠标左键拖拽移动地图
3. **全屏模式** - F11进入全屏，ESC退出全屏
4. **悬浮提示** - 鼠标悬停显示详细信息

## 组件结构

```
src/views/intelligentRobot/intelligentRobotMap/
├── index.vue                                    # 主页面
├── components/
│   └── IntelligentRobotMapComponent.vue        # 地图组件
└── README.md                                   # 本文档
```

## 使用方法

### 1. 在页面中引入组件

```vue
<template>
  <div>
    <intelligent-robot-map-component 
      ref="mapComponentRef"
      @map-loaded="handleMapLoaded"
      @websocket-connected="handleWebSocketConnected"
      @websocket-disconnected="handleWebSocketDisconnected"
    />
  </div>
</template>

<script setup>
import IntelligentRobotMapComponent from './components/IntelligentRobotMapComponent.vue'

const mapComponentRef = ref(null)

const handleMapLoaded = (mapData) => {
  console.log('地图加载完成:', mapData)
}

const handleWebSocketConnected = () => {
  console.log('WebSocket连接成功')
}

const handleWebSocketDisconnected = () => {
  console.log('WebSocket连接断开')
}
</script>
```

### 2. 组件事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| map-loaded | 地图加载完成 | mapData: 地图数据对象 |
| websocket-connected | WebSocket连接成功 | - |
| websocket-disconnected | WebSocket连接断开 | - |

### 3. 组件方法

通过ref可以调用组件的方法：

```javascript
// 重新加载地图数据
mapComponentRef.value.loadMapData()

// 切换全屏模式
mapComponentRef.value.toggleFullscreen()

// 更新地图元素
mapComponentRef.value.updateMapElements()
```

## WebSocket通信

### 连接配置
- **URL**: `ws://${location.host}/wsDispatch`
- **认证**: Bearer Token (从localStorage获取)

### 消息类型

#### 1. 地图信息 (MAP)
```json
{
  "type": "MAP",
  "data": {
    "o": [{"x": 1.0, "y": 2.0, "type": 0}],  // 障碍点
    "ps": [["1", 1, 23.4, 56.3, 0.0, 0, "LandMark", "标记点A"]],  // 点位
    "ai": [{"name": "漏液报警", "x": 1.0, "y": 2.0, "type": "leak"}]  // AI报警
  }
}
```

#### 2. 机器人状态 (ROBOT)
```json
{
  "type": "ROBOT",
  "data": [{
    "id": 1,
    "name": "机器人1",
    "pos": {"x": 1.0, "y": 2.0},
    "theta": 1.57,
    "clr": "#ff0000",
    "s": "运行中",
    "bty": "85%",
    "vel": 1.2
  }]
}
```

#### 3. 障碍变更 (OBSTACLE)
```json
{
  "type": "OBSTACLE",
  "data": ["xmap_id_1", "xmap_id_2"]  // 当前存在的障碍ID列表
}
```

#### 4. AI报警变更 (AI_ALARM)
```json
{
  "type": "AI_ALARM",
  "data": [{"name": "漏液报警", "x": 1.0, "y": 2.0, "type": "leak"}]
}
```

## 点位类型

支持以下点位类型的显示控制：

| 类型 | 标识 | 图标 | 说明 |
|------|------|------|------|
| 标记点 | LandMark | 📍 | 普通标记点 |
| 充电点 | ChargePoint | 🔋 | 机器人充电位置 |
| 识别点 | IdenPoint | 👁️ | AI识别检测点 |
| 红外测温 | InfraredDetection | 🌡️ | 温度检测点 |
| 十字路口 | Crossroads | ➕ | 路径交叉点 |
| 出口 | Exit | 🚪 | 出入口位置 |
| 停车位 | Parking | 🅿️ | 停车区域 |

## AI报警类型

| 类型 | 标识 | 颜色 | 说明 |
|------|------|------|------|
| 漏液报警 | leak | #ff6b6b | 检测到液体泄漏 |
| 异物检测 | matter | #4ecdc4 | 检测到异常物体 |
| 温度报警 | temp | #45b7d1 | 温度异常 |
| 烟雾报警 | gas | #96ceb4 | 检测到烟雾或气体 |

## 坐标转换

组件使用以下坐标转换逻辑：

1. **输入坐标**: 机器人实际位置坐标（米为单位）
2. **转换过程**: 
   - 应用地图偏移量
   - 应用地图缩放比例
   - 转换为pt单位
   - 转换为像素坐标
3. **输出坐标**: ECharts可用的屏幕坐标

转换公式参考 `src/utils/mapCalc.js` 中的实现。

## 样式定制

组件使用SCSS编写样式，支持以下定制：

### 主要CSS类
- `.intelligent-robot-map` - 主容器
- `.map-display-area` - 地图显示区域
- `.point-toolbar` - 点位选择工具栏
- `.connection-status` - 连接状态指示器

### 响应式设计
组件支持移动端适配，在768px以下会调整布局和字体大小。

## 依赖项

- Vue 3.x
- Element Plus
- ECharts 5.x
- SCSS

## 注意事项

1. **WebSocket连接**: 确保后端WebSocket服务正常运行
2. **地图数据**: 需要后端提供SVG格式的地图数据
3. **坐标系统**: 确保前后端坐标系统一致
4. **性能优化**: 大量数据时建议启用虚拟化或分页
5. **浏览器兼容**: 需要支持WebSocket的现代浏览器

## 故障排除

### 常见问题

1. **地图不显示**
   - 检查API接口是否正常
   - 确认SVG数据格式正确

2. **WebSocket连接失败**
   - 检查网络连接
   - 确认Token有效性
   - 检查后端WebSocket服务

3. **坐标显示错误**
   - 检查坐标转换参数
   - 确认地图比例尺设置

4. **性能问题**
   - 减少同时显示的元素数量
   - 优化更新频率
   - 检查内存泄漏

## 更新日志

### v1.0.0 (2025-01-05)
- 初始版本发布
- 实现基础地图显示功能
- 支持WebSocket实时通信
- 添加机器人位置跟踪
- 实现障碍物和AI报警显示
- 支持点位类型选择
- 添加地图交互功能（缩放、拖拽、全屏）
