<template>
  <div class="app-container">

    <el-form class="query-form" :model="queryParams" :inline="true" label-width="85px">
      <el-form-item label="robotId">
        <el-input v-model="queryParams.robotId" placeholder="请输入设备ID"></el-input>
      </el-form-item>
      <el-form-item label="接口">
        <el-select v-model="queryParams.service" placeholder="请选择接口">
          <el-option v-for="item in serviceList" :value="item.key" :label="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="日志内容">
        <el-input v-model="queryParams.content" placeholder="请输入参数"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button icon="Search" type="primary" @click="handleSend">发送</el-button>
      </el-form-item>
    </el-form>

    <el-button type="primary" @click="dataList=[]">清空</el-button>
    <el-table :data="dataList">
      <el-table-column label="记录时间" align="center" prop="createTime"/>
      <el-table-column label="机器人ID" align="center" prop="robotId"/>
      <el-table-column label="类型" align="center" prop="logType"/>
      <el-table-column label="参数" align="center" prop="content"/>
      <el-table-column label="消息" align="center" prop="message"/>
    </el-table>

  </div>
</template>

<script setup>
import {getServiceList, sendMessage} from '@/api/industrial/industrialTest';
import dayjs from 'dayjs';

const dataList = ref([])
const queryParams = ref({robotId: '', service: '', content: ''})
const serviceList = ref([])


function handleSend() {
  dataList.value.push({
    createTime: dayjs(new Date()).format('YYYY-MM-DD HH:MM:ss'),
    robotId: queryParams.value.robotId,
    logType: "发送",
    content: queryParams.value.content,
    message: ""
  })
  sendMessage(queryParams.value).then(res => {
    if (res.data != undefined) {
      dataList.value.push({
        createTime: dayjs(new Date()).format('YYYY-MM-DD HH:MM:ss'),
        robotId: queryParams.value.robotId,
        logType: "响应",
        content: res.data.params,
        message: res.data.message
      })
    }
  })
}

getServiceList().then(res => serviceList.value = res.data);
</script>

<style>

</style>