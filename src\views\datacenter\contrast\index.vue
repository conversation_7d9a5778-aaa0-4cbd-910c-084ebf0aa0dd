<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true">
      <el-form-item label="规则名称" prop="idenRuleName">
        <el-input
            v-model="queryParams.idenRuleName"
            placeholder="请输入规则名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-form :model="queryParams" ref="queryRef" :inline="true">
      <el-form-item>
        <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="table.tableList" style="width: 100%;">
      <el-table-column label="序号" width="50" type="index" align="center">
        <template #default="scope">
          <span>{{ (table.pageNum - 1) * table.pageSize + scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="规则名称" align="center" prop="idenRuleName" :show-overflow-tooltip="true"/>
      <el-table-column label="规则类别" align="center" prop="idenRuleType" :show-overflow-tooltip="true">
        <template #default="scope">
          <div v-for=" item in filteredIdenTypes(scope.row.idenRuleType)">
            {{ item.label }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="点位模型" align="center" prop="idenModelId" :show-overflow-tooltip="true">
        <template #default="scope">
          <div v-for=" item in filteredIdenModelId(scope.row.idenModelId)">
            {{ item.label }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="规则数量" align="center" prop="ruleNum" :show-overflow-tooltip="true"/>

      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Notebook" @click="handleRulesList(scope.row)"
                     v-hasPermi="['monitor:online:forceLogout']">规则列表
          </el-button>
          <el-button link type="primary" icon="Edit" @click="handleEdit(scope.row)"
                     v-hasPermi="['monitor:online:forceLogout']">编辑
          </el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
                     v-hasPermi="['iden:rule:remove']">删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="table.total > 0" :total="table.total" v-model:page="table.pageNum" v-model:limit="table.pageSize" @pagination="getList"/>

    <!-- 添加规则 -->
    <el-dialog :title="rulesForm.title" v-model="rulesForm.dialogOpen" width="820px" append-to-body>
      <el-form ref="rulesFormRef" :model="rulesForm" :rules="rules" label-width="120px">
        <el-form-item label="规则名称" prop="idenRuleName">
          <el-input v-model="rulesForm.idenRuleName" placeholder="请输入任务名称"/>
        </el-form-item>
        <el-form-item label="规则类别" prop="idenRuleType">
          <el-select v-model="rulesForm.idenRuleType" placeholder="请选择">
            <el-option v-for="dict in iden_rule_type" :key="dict.value" :label="dict.label"
                       :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="点位模型" prop="idenModelId">
          <el-select v-model="rulesForm.idenModelId" placeholder="请选择">
            <el-option v-for="dict in idenModelList" :key="dict.value" :label="dict.label"
                       :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm()">确 定</el-button>
          <el-button @click="rulesForm.dialogOpen=false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <IdenRuleExpr ref="idenRuleExpr" ></IdenRuleExpr>
  </div>
</template>

<script setup name="IdenRule">

import IdenRuleExpr from  "@/views/operation/idenRuleExpr"

const {proxy} = getCurrentInstance()
const {iden_rule_type} = proxy.useDict("iden_rule_type")
const table = reactive({tableList: [], total: 0, pageNum: 1, pageSize: 10})
const queryParams = reactive({idenRuleName: ''})
const loading = ref(true)
const data = reactive({
  rulesForm: {dialogOpen: false, title: '', id: '', idenRuleName: '', idenRuleType: '', idenModelId: ''},
  rules: {
    idenRuleName: [{required: true, message: "规则名称不能为空", trigger: "blur"}],
    idenRuleType: [{required: true, message: "规则类别不能为空", trigger: "change"}],
    idenModelId: [{required: true, message: "点位模型不能为空", trigger: "change"}]
  }
})
const {rulesForm, rules} = toRefs(data)
const idenModelList = [];

</script>
