<template>
	<div class="app-container" v-loading="loading">
		<el-descriptions title="" border :column="3">
			<el-descriptions-item align="center" label="任务名称" :span="2">{{data.taskName}}</el-descriptions-item>
			<el-descriptions-item align="center" label="任务状态" :span="2">
				<dict-tag :options="task_instance_status" :value="data.status" />
			</el-descriptions-item>
			<el-descriptions-item align="center" label="所属部门" :span="3">{{data.deptName}}</el-descriptions-item>
			<el-descriptions-item align="center" label="开始时间"
				:span="2">{{parseTime(data.startTime)}}</el-descriptions-item>
			<el-descriptions-item align="center" label="结束时间"
				:span="2">{{parseTime(data.finishTime)}}</el-descriptions-item>
			<el-descriptions-item align="center" label="环境信息">{{data.envReport}}</el-descriptions-item>
		</el-descriptions>

		<el-descriptions title="巡检结果" border :column="6">
			<el-descriptions-item align="center"
				label="已巡检">{{data.pointHealthNum + data.pointAlarmNum + data.abnormalNum}}</el-descriptions-item>
			<el-descriptions-item align="center" label="正常点位">{{data.pointHealthNum}}</el-descriptions-item>
			<el-descriptions-item align="center" label="告警点位">{{data.pointAlarmNum}}</el-descriptions-item>
			<el-descriptions-item align="center" label="不可达">{{data.unreachableNum}}</el-descriptions-item>
			<el-descriptions-item align="center" label="未识别">{{data.abnormalNum}}</el-descriptions-item>
			<el-descriptions-item align="center" label="智能报警">{{data.aiAlarmNum}}</el-descriptions-item>
		</el-descriptions>
		<el-descriptions title="智能报警"></el-descriptions>

		<el-table :data="data.alarmAiList" border>
			<el-table-column type="index" label="序号" align="center" width="60" />
			<el-table-column label="报警名称" align="center" prop="alarmName" />
			<el-table-column label="识别类型" align="center" prop="idenType" />
			<el-table-column label="识别结果" align="center" prop="idenResult" />
			<el-table-column label="报警等级" align="center" prop="alarmLevel" />
			<el-table-column label="报警时间" align="center">
				<template #default="scope">
					{{ parseTime(scope.row.triggerTime) }}
				</template>
			</el-table-column>
			<el-table-column label="备注" align="center" prop="remark" />
			<el-table-column label="现场照片" align="center">
				<template #default="scope">
					<el-image preview-teleported style="width: 100px; height: 100px" :src="apiUrl + scope.row.imgUrl" :zoom-rate="1.2"
						:preview-src-list="[apiUrl + scope.row.imgUrl]" :max-scale="7" :min-scale="0.2" fit="cover"
						show-progress :initial-index="0" />
				</template>
			</el-table-column>
		</el-table>
		
		<!-- 0未抵达 | 1执行中 | 3巡检正常 | 7识别异常 | 8不可达 | 9告警 -->
		<el-descriptions title="告警点位"></el-descriptions>

		<el-table :data="data.status9" border>
			<el-table-column type="index" label="序号" align="center" width="60" />
			<el-table-column label="点位模型" align="center" prop="idenModelName" />
			<el-table-column label="点位名称" align="center" prop="pointName" />
			<el-table-column label="识别结果" align="center">
				<template #default="scope">
					<div v-for="item in scope.row.taskInstanceNodeResultList ">
						<span>{{item.idenModelParamName}} = {{ item.floatValue || item.enumValue }}</span>
					</div>
				</template>
			</el-table-column>
			<el-table-column label="报警等级" align="center">
				<template #default="scope">
					<span>{{scope.row.ruleResult + scope.row.ruleExpressionText}}</span>
				</template>
			</el-table-column>
			<el-table-column label="采集时间" align="center">
				<template #default="scope">
					{{ parseTime(scope.row.idenTime) }}
				</template>
			</el-table-column>
			<el-table-column label="现场照片" align="center">
				<template #default="scope">
					<el-image preview-teleported style="width: 100px; height: 100px" :src="apiUrl + scope.row.imgUrl" :zoom-rate="1.2"
						:preview-src-list="[apiUrl + scope.row.imgUrl]" :max-scale="7" :min-scale="0.2" fit="cover"
						show-progress :initial-index="0" />
				</template>
			</el-table-column>
		</el-table>

		<el-descriptions title="异常识别"></el-descriptions>

		<el-table :data="data.status7" border>
			<el-table-column type="index" label="序号" align="center" width="60" />
			<el-table-column label="点位模型" align="center" prop="idenModelName" />
			<el-table-column label="点位名称" align="center" prop="pointName" />
			<el-table-column label="状态" align="center">
				<template #default="scope">
					<span>{{scope.row.ruleResult + scope.row.ruleExpressionText}}</span>
				</template>
			</el-table-column>
			<el-table-column label="采集时间" align="center">
				<template #default="scope">
					{{ parseTime(scope.row.idenTime) }}
				</template>
			</el-table-column>
			<el-table-column label="识别结果" align="center">
				<template #default="scope">
					<div v-for="item in scope.row.taskInstanceNodeResultList ">
						<span>{{item.idenModelParamName}} = {{ item.floatValue || enumValue }}</span>
					</div>
				</template>
			</el-table-column>
			<el-table-column label="现场照片" align="center">
				<template #default="scope">
					<el-image preview-teleported style="width: 100px; height: 100px" :src="apiUrl + scope.row.imgUrl" :zoom-rate="1.2"
						:preview-src-list="[apiUrl + scope.row.imgUrl]" :max-scale="7" :min-scale="0.2" fit="cover"
						show-progress :initial-index="0" />
				</template>
			</el-table-column>
		</el-table>

		<el-descriptions title="正常点位"></el-descriptions>

		<el-table :data="data.status3" border>
			<el-table-column type="index" label="序号" align="center" width="60" />
			<el-table-column label="点位模型" align="center" prop="idenModelName" />
			<el-table-column label="点位名称" align="center" prop="pointName" />
			<el-table-column label="识别结果" align="center">
				<template #default="scope">
					<div v-for="item in scope.row.taskInstanceNodeResultList ">
						<span>{{item.idenModelParamName}} = {{ item.floatValue || enumValue }}</span>
					</div>
				</template>
			</el-table-column>
			<el-table-column label="状态" align="center">
				<template #default="scope">
					<span>{{scope.row.ruleResult + scope.row.ruleExpressionText}}</span>
				</template>
			</el-table-column>
			<el-table-column label="采集时间" align="center">
				<template #default="scope">
					{{ parseTime(scope.row.idenTime) }}
				</template>
			</el-table-column>

			<el-table-column label="现场照片" align="center">
				<template #default="scope">
					<el-image preview-teleported style="width: 100px; height: 100px" :src="apiUrl + scope.row.imgUrl" :zoom-rate="1.2"
						:preview-src-list="[apiUrl + scope.row.imgUrl]" :max-scale="7" :min-scale="0.2" fit="cover"
						show-progress :initial-index="0" />
				</template>
			</el-table-column>
		</el-table>
	</div>
</template>

<script>
	import taskInstance from '@/api/task/taskInstance';

	export default {
		data() {
			return {
				loading: false,
				taskId: this.$route.query.id,
				data: {},
				apiUrl: import.meta.env.VITE_APP_BASE_API,
				task_instance_status: getCurrentInstance().proxy.useDict("task_instance_status").task_instance_status,
			}
		},
		created() {
			this.getList()
		},
		methods: {
			getList() {
				this.loading = true
				taskInstance.getViewTheReport(this.taskId).then(res => {
					let status3 = res.data.taskInstanceNodeList.filter(f=>f.status == 3)
					let status7 = res.data.taskInstanceNodeList.filter(f=>f.status == 7)
					let status9 = res.data.taskInstanceNodeList.filter(f=>f.status == 9)
					this.data = res.data
					this.data.status3 = status3
					this.data.status7 = status7
					this.data.status9 = status9
					this.loading = false
				})
			}
		}
	}
</script>

<style scoped>
	.el-descriptions {
		margin-top: 20px;
	}

	/deep/.el-descriptions__header {
		justify-content: center;
	}

	/deep/.el-descriptions__title {}
</style>