import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/hyc";


// 查询设备树列表
export function listBizDeviceTree(query) {
    return request({
      url: '/biz/device/tree/list',
      method: 'get',
      params: query
    })
}

// 查询设备树详细
export function getBizDeviceTree(id) {
    return request({
      url: '/biz/device/tree/getById/' + parseStrEmpty(id),
      method: 'get'
    })
  }


// 新增设备树
export function addBizDeviceTree(data) {
    return request({
      url: '/biz/device/tree/add',
      method: 'post',
      data: data
    })
  }
  
  // 修改设备树
  export function updateBizDeviceTree(data) {
    return request({
      url: '/biz/device/tree/update',
      method: 'put',
      data: data
    })
  }

// 删除设备树
export function delBizDeviceTree(id) {
    return request({
      url: '/biz/device/tree/delete/' + id,
      method: 'delete'
    })
  }