import request from '@/utils/request'

const add = (params) =>{
	return request.post('point/add', params)
}

const remove = (ids) =>{
	return request.get('point/remove/' + ids)
}

const edit = (params) =>{
	return request.post('point/edit', params)
}

const getList = (params) =>{
	return request.get('point/getList', {params: params})
}

const getAll = (params) =>{
	return request.get('point/getAll', {params: params})
}

const getOne = (id) =>{
	return request.get('point/getOne/' + id)
}

const uploadImg = (data) => {
  return request({
    url: 'point/uploadImg',
    method: 'post',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    data: data
  })
}

const getDeviceTree = (params) =>{
	return request.get('point/getDeviceTree', {params: params})
}

const paramListSave = (params) =>{
	return request.post('point/paramListSave', params)
}

const AIRecognition = (params) =>{
	return request.post('point/AIRecognition', params)
}

export default {
	add, remove, edit, getList, getAll, getOne, uploadImg, getDeviceTree, paramListSave, AIRecognition
}