<template>

  <el-dialog v-model="dialogFormVisible" title="审核识别结果" class="dialog-diy">

    <el-row>
      <el-col :span="11">
        <el-card>
          <div class="block text-center">

            <el-carousel height="auto" :autoplay="false">
              <el-carousel-item style="height: 300px" v-for=" url in imageList ">
                <el-image :src="url" :preview-src-list="imageList" fit="cover" :preview-teleported="true"
                          show-progress></el-image>
              </el-carousel-item>
            </el-carousel>
          </div>
          <div style="padding-top: 20px;">

            <el-form-item label="备注">
              <el-input v-model="baseItem.remark" type="textarea"/>
            </el-form-item>
          </div>
        </el-card>
      </el-col>

      <el-col :span="13" style="padding-left: 10px;">
        <el-card v-if="rules.show">
          <template #header>
            <div class="card-header">
              <span>规则信息</span>
            </div>
          </template>
          <div>{{ rules.rulesMessage }}</div>
        </el-card>
        <el-card>
          <template #header>
            <div class="card-header">
              <span>识别结果</span>
            </div>
          </template>
          <div>

            <el-table :data="tableData" style="width: 100%">
              <el-table-column prop="idenModelParamName" label="参数" width="100"/>
              <el-table-column prop="paramValue" label="识别值" width="80">
                <template #default="scope">
                  {{ scope.row.dataType == '1' ? scope.row.floatValue : scope.row.enumValue }}
                </template>
              </el-table-column>
              <el-table-column prop="isTrue" label="是否正确">
                <template #default="scope">
                  <el-radio-group v-model="scope.row.correctFlag">
                    <el-radio value="1">正确</el-radio>
                    <el-radio value="0">错误</el-radio>
                  </el-radio-group>
                </template>

              </el-table-column>
              <el-table-column prop="actualValue" label="实际值" width="150">
                <template #default="scope">
                  <el-input v-model="scope.row.actualFloatValue" v-if="scope.row.dataType==1"></el-input>
                  <el-select v-if="scope.row.dataType==2" clearable v-model="scope.row.actualEnumValue">
                    <el-option v-for="val in JSON.parse(scope.row.idenTypeParam.valueScopeJson)" :label="val"
                               :value="val"/>
                  </el-select>
                </template>
              </el-table-column>
            </el-table>

          </div>
        </el-card>
      </el-col>
    </el-row>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="success" @click="approvalSubmit(1)">
          通过
        </el-button>
        <el-button type="danger" @click="approvalSubmit(2)">
          不通过
        </el-button>
      </div>
    </template>
  </el-dialog>

</template>
<script setup>
import {getTaskInstanceNode, approvalInstanceNode} from '@/api/datacenter/datacenter'

const {proxy} = getCurrentInstance()
const imageList = ref([]);
const dialogFormVisible = ref(false)
const tableData = ref([]);
const baseItem = ref([])
const url= import.meta.env.VITE_APP_BASE_API;
const rules = ref({show: false, rulesMessage: ''})

function approval(item) {
  baseItem.value = item;
  if (item.taskInstanceNodeId == null || item.taskInstanceNodeId == '') {
    proxy.$modal.msgSuccess("无审核信息");
    return;
  } else {
    if (item.taskInstanceNodeAuditStatus=='1' || item.taskInstanceNodeAuditStatus=='2'){
      proxy.$modal.msgSuccess("已审核,无需再审");
      return;
    }
    imageList.value = [];
    tableData.value = [];
    rules.value = {show: false, rulesMessage: ''};
    getTaskInstanceNode({id: item.taskInstanceNodeId}).then((res) => {
      dialogFormVisible.value = true;
      tableData.value = res.data.taskInstanceNodeResultList;
      if (res.data.ruleExpressionText == null || res.data.ruleExpressionText == '') {
        rules.value.show = false;
      } else {
        rules.value.show = true;
        rules.value.rulesMessage = res.data.ruleExpressionText;
      }
      if (res.data.mediaFileList.length>0){
        res.data.mediaFileList.forEach(item=>{
          imageList.value.push(url+item.thumbnailUrl)
        })
      }
    })
  }
}

function approvalSubmit(type) {
  let params = {}
  params.id = baseItem.value.taskInstanceNodeId;
  params.auditStatus = type;
  params.remark = baseItem.value.remark;
  params.taskInstanceNodeResultList = tableData.value;

  approvalInstanceNode(params).then(res => {
    proxy.$modal.msgSuccess("审核成功！");
    dialogFormVisible.value = false
  })
}


defineExpose({
  approval
})

</script>

<style scoped>

.dialog-diy {
  border-radius: 15px;
  width: 1100px;
}


</style>