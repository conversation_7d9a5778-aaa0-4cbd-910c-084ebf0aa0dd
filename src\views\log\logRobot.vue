<template>
	<div class="app-container">

		<el-form class="query-form" :model="queryParams" :inline="true" label-width="85px">
			<el-form-item label="记录时间">
				<el-date-picker v-model="queryParams.recordTime" type="datetimerange" start-placeholder="开始时间" end-placeholder="结束时间" value-format="YYYY-MM-DD HH:mm:ss" />
			</el-form-item>
			<el-form-item label="机器人名称">
				<el-input v-model="queryParams.robotName" placeholder="请输入机器人名称"></el-input>
			</el-form-item>
			<el-form-item label="类型">
				<el-select v-model="queryParams.logType" placeholder="请选择类型">
					<el-option v-for="item in logTypeList" :value="item" :label="item"></el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="日志内容">
				<el-input v-model="queryParams.content" placeholder="请输入日志内容"></el-input>
			</el-form-item>			
			<el-form-item>
				<el-button icon="Search" type="primary" @click="handleQuery">搜索</el-button>
				<el-button icon="Refresh" @click="resetQuery">重置</el-button>
			</el-form-item>
		</el-form>
		
		<el-row :gutter="10" class="mb8">
			<el-col :span="1.5">
				<el-button icon="Download" type="warning" plain @click="handleExport">导出</el-button>
			</el-col>
		</el-row>
		
		<el-table v-loading="loading" :data="dataList">
			<el-table-column label="记录时间" align="center" prop="createTime">
				<template #default="scope">
					{{ parseTime(scope.row.createTime) }}
				</template>
			</el-table-column>
			<el-table-column label="机器人名称" align="center" prop="robotName" />
			<el-table-column label="类型" align="center" prop="logType" />
			<el-table-column label="日志内容" align="center" prop="content" />
		</el-table>

		<pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
			v-model:limit="queryParams.pageSize" @pagination="getList" />
	</div>
</template>

<script>
	import logRobot from '@/api/log/logRobot';

	export default {
		data() {
			return {
				proxy: getCurrentInstance().proxy,
				dataList: [],
				loading: false,
				total: 0,
				queryParams: {
					pageNum: 1,
					pageSize: 10
				},
				logTypeList: []
			}
		},
		mounted() {
			this.getList()
			this.getLogTypeList()
		},
		methods: {
			getList() {
				this.loading = true
				logRobot.getList(this.queryParams).then(res => {
					this.dataList = res.rows
					this.total = res.total
					this.loading = false
				})
			},	
			getLogTypeList () {
				logRobot.getLogTypeList().then(res=>{
					this.logTypeList = res.data
				})
			},
			resetQuery() {
				this.queryParams = {
					pageNum: 1,
					pageSize: 10
				}
				this.handleQuery()
			},			
			handleQuery() {
				if (this.queryParams.recordTime) {
					this.queryParams.startTime = this.queryParams.recordTime[0]
					this.queryParams.endTime = this.queryParams.recordTime[1]
				}
				this.getList()
			},
			handleExport() {
				this.download("logRobot/export", {
				  ...this.queryParams
				},`机器人日志_${new Date().getTime()}.xlsx`)
			}
		}
	}
</script>

<style>

</style>