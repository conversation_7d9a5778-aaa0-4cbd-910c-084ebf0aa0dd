<template>
  <div class="center-panel-main">
    <div class="map-container">
      <!-- 没有选择地图或没有上传任何图层时显示空状态 -->
      <div class="placeholder-map" v-if="!currentMap || !cadLayerUrl">
        <el-empty description="请选择或新建地图">
          <template #image>
            <el-icon size="64" color="var(--el-color-info)">
              <MapLocation />
            </el-icon>
          </template>
        </el-empty>
      </div>
      <!-- 有地图且上传了CAD图层时显示内容 -->
      <div class="map-content" v-else>
        <!-- 使用ECharts显示地图图层 -->
        <div ref="echartsContainer" class="echarts-container"></div>

        <!-- 缩放信息显示 -->
        <div class="zoom-info" v-if="cadLayerUrl">
          <span>{{ Math.round(viewState.scale * 100) }}%</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="CenterPanelMain">
import { ref, reactive, watch, onMounted, onBeforeUnmount, nextTick } from 'vue'
import { MapLocation } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { calculateBezierPoints, calculateArcPoints } from '../utils/MapEchartsRenderer'

// Props
const props = defineProps({
  currentMap: {
    type: Object,
    default: null
  },
  cadLayerUrl: {
    type: String,
    default: ""
  },
  cadTransform: {
    type: Object,
    required: true
  },
  showWorkPoints: {
    type: Boolean,
    default: false
  },
  showObstaclePoints: {
    type: Boolean,
    default: false
  }
})

// Refs
const echartsContainer = ref(null)

// Data
const echartsInstance = ref(null)
const cadWidthPx = ref(0)
const cadHeightPx = ref(0)
const cadWidth = ref(0)
const cadHeight = ref(0)
const pt2px = ref(96 / 72)
const baseScale = ref(1)
const baseX = ref(0)
const baseY = ref(0)

// 用户交互状态
const mouseState = reactive({
  isDragging: false,
  lastX: 0,
  lastY: 0
})

// 视图状态 - 用于记忆缩放和平移
const viewState = reactive({
  scale: 1,
  translateX: 0,
  translateY: 0
})

// 缩放配置
const zoomConfig = {
  min: 0.2,  // 最小缩放比例
  max: 20,   // 最大缩放比例
  step: 0.1  // 每次缩放步长
}

// Watchers
watch(() => props.cadLayerUrl, () => {
  initEcharts()
})

watch(() => props.cadTransform, () => {
  updateEchartsLayers()
}, { deep: true })

watch(() => props.currentMap, () => {
  updateMapElements()
}, { deep: true })

watch(() => props.showWorkPoints, () => {
  updateEchartsLayers()
})

watch(() => props.showObstaclePoints, () => {
  updateEchartsLayers()
})

// Lifecycle
onMounted(() => {
  setTimeout(() => {
    initEcharts()
  }, 100)

  window.addEventListener('resize', resizeEcharts)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', resizeEcharts)
  cleanupMouseEvents()

  if (echartsInstance.value) {
    echartsInstance.value.dispose()
  }
})

// Methods
const initEcharts = () => {
  console.log('CenterPanelMain.initEcharts 被调用')

  nextTick(() => {
    try {
      if (echartsInstance.value) {
        console.log('销毁现有的ECharts实例')
        echartsInstance.value.dispose()
        echartsInstance.value = null
      }

      if (!props.cadLayerUrl) {
        console.log('没有CAD图层URL，不初始化ECharts')
        return
      }

      if (!echartsContainer.value) {
        console.error('ECharts容器不存在，无法初始化')
        return
      }

      const containerWidth = echartsContainer.value.clientWidth
      const containerHeight = echartsContainer.value.clientHeight

      console.log('ECharts容器尺寸:', containerWidth, 'x', containerHeight)

      if (containerWidth <= 0 || containerHeight <= 0) {
        console.error('ECharts容器尺寸为0，延迟初始化')
        setTimeout(() => {
          initEcharts()
        }, 200)
        return
      }

      echartsInstance.value = echarts.init(echartsContainer.value)
      setupMouseEvents()

      preloadImages().then(() => {
        updateEchartsLayers()
        echartsInstance.value.on('click', handleChartClick)
      }).catch(error => {
        console.error('预加载图片失败:', error)
      })
    } catch (error) {
      console.error('初始化ECharts实例时出错:', error)
    }
  })
}

const preloadImages = () => {
  const promises = []

  if (props.cadLayerUrl) {
    const cadPromise = new Promise((resolve) => {
      const img = new Image()
      img.onload = () => {
        cadWidthPx.value = img.width
        cadHeightPx.value = img.height

        const cadWidthPt = props.currentMap && props.currentMap.cadWidth
        const cadHeightPt = props.currentMap && props.currentMap.cadHeight

        if (cadWidthPt && cadHeightPt) {
          pt2px.value = Math.max(
            cadWidthPx.value / cadWidthPt,
            cadHeightPx.value / cadHeightPt
          )
          cadWidth.value = cadWidthPt
          cadHeight.value = cadHeightPt
        } else {
          pt2px.value = 96 / 72
          cadWidth.value = cadWidthPx.value / pt2px.value
          cadHeight.value = cadHeightPx.value / pt2px.value
        }

        resolve()
      }
      img.onerror = () => {
        console.error('CAD图层加载失败')
        resolve()
      }
      img.src = props.cadLayerUrl
    })
    promises.push(cadPromise)
  }

  return Promise.all(promises)
}

const updateEchartsLayers = () => {
  if (!echartsInstance.value) return

  const containerWidth = echartsContainer.value.clientWidth
  const containerHeight = echartsContainer.value.clientHeight

  let cadScale = 1
  if (cadWidthPx.value && cadHeightPx.value) {
    const scaleX = containerWidth / cadWidthPx.value
    const scaleY = containerHeight / cadHeightPx.value
    cadScale = Math.min(scaleX, scaleY)
  }

  const option = {
    backgroundColor: '#f5f7fa',
    animation: false,
    hoverLayerThreshold: Infinity,
    grid: {
      show: false,
      left: 0,
      right: 0,
      top: 0,
      bottom: 0,
      containLabel: false
    },
    xAxis: {
      show: false,
      type: 'value',
      min: 0,
      max: containerWidth
    },
    yAxis: {
      show: false,
      type: 'value',
      min: 0,
      max: containerHeight
    },
    tooltip: {
      trigger: 'item',
      confine: true,
      enterable: true,
      appendToBody: true,
      backgroundColor: 'rgba(50,50,50,0.9)',
      borderColor: '#333',
      borderWidth: 1,
      padding: [10, 15],
      textStyle: {
        color: '#fff'
      }
    },
    series: [],
    graphic: []
  }

  // 添加CAD图层
  if (props.cadLayerUrl) {
    const centeredX = (containerWidth - cadWidthPx.value * cadScale) / 2
    const centeredY = (containerHeight - cadHeightPx.value * cadScale) / 2

    const finalScale = cadScale * props.cadTransform.scale * viewState.scale
    const finalX = centeredX + props.cadTransform.translateX + viewState.translateX
    const finalY = centeredY + props.cadTransform.translateY + viewState.translateY

    option.graphic.push({
      type: 'image',
      id: 'cadLayer',
      style: {
        image: props.cadLayerUrl,
        opacity: 1,
        width: cadWidthPx.value,
        height: cadHeightPx.value
      },
      position: [finalX, finalY],
      rotation: props.cadTransform.rotation * Math.PI / 180,
      scale: [finalScale, finalScale],
      origin: [0, 0],
      z: 1,
      draggable: false
    })

    baseScale.value = cadScale
    baseX.value = centeredX
    baseY.value = centeredY
  }

  // 添加工作点位和路径
  addWorkPointsAndPaths(option, containerWidth, containerHeight)

  echartsInstance.value.setOption(option, true)
}

// 添加工作点位和路径
const addWorkPointsAndPaths = (option, containerWidth, containerHeight) => {
  console.log('showWorkPoints', props.currentMap.advancedPoints.length)

  // 添加工作点位 - 只在showWorkPoints为true时添加
  if (props.showWorkPoints && props.currentMap && props.currentMap.advancedPoints && props.currentMap.advancedPoints.length > 0) {
    // 创建散点图系列用于显示点位
    const pointData = []

    // 使用CAD图层的坐标系，同时考虑交互状态
    const activeScale = baseScale.value * props.cadTransform.scale * viewState.scale
    const activeTranslateX = baseX.value + props.cadTransform.translateX + viewState.translateX
    const activeTranslateY = baseY.value + props.cadTransform.translateY + viewState.translateY

    // 处理每个点位
    props.currentMap.advancedPoints.forEach((point, index) => {
      // 1. 首先将pt单位转换为px单位
      const pointXPx = point.newX * pt2px.value
      const pointYPx = point.newY * pt2px.value

      // 2. 然后考虑CAD图层的缩放
      const scaledX = pointXPx * activeScale
      const scaledY = pointYPx * activeScale

      // 3. 最后加上CAD图层的偏移量
      const x = activeTranslateX + scaledX
      const y = activeTranslateY + scaledY

      // 4. 因为echart是左下角原点，所以需要Y轴翻转
      const finalY = containerHeight - y
      const finalX = x

      const instanceName = point.instanceName || `点位${index + 1}`
      const displayName = point.name || `${instanceName}`

      // 保存点位的所有属性，用于鼠标悬浮提示
      pointData.push([
        finalX,
        finalY,
        displayName,
        point.id,
        point.angle,
        point.theta,
        point.newX,
        point.newY,
        point.instanceName,
        point.x,
        point.y,
      ])
    })

    // 添加散点图系列
    option.series.push({
      type: 'scatter',
      id: 'workPoints',
      data: pointData,
      symbolSize: 20, // 点的大小
      symbol: 'pin', // 使用图钉形状，更加明显
      itemStyle: {
        color: '#FF5722', // 点的颜色
        borderColor: '#FFFFFF', // 边框颜色
        borderWidth: 2, // 边框宽度
        shadowBlur: 10,
        shadowColor: 'rgba(0, 0, 0, 0.5)'
      },
      label: {
        show: true,
        position: 'top',
        distance: 5,
        formatter: '{@[2]}', // 显示点位名称
        backgroundColor: '#333',
        padding: [3, 5],
        borderRadius: 3,
        color: '#fff'
      },
      tooltip: {
        formatter: function(params) {
          return `
            <div style="font-weight:bold;margin-bottom:5px;">${params.data[2]}</div>
            <div>ID: ${params.data[3] || '无'}</div>
            <div>角度: ${params.data[4] || '无'}</div>
            <div>方向: ${params.data[5] || '无'}</div>
            <div>X坐标: ${params.data[9] || '无'}</div>
            <div>Y坐标: ${params.data[10] || '无'}</div>
            <div>点位名称: ${params.data[8] || '无'}</div>
          `
        },
        backgroundColor: 'rgba(50,50,50,0.9)',
        borderColor: '#333',
        borderWidth: 1,
        padding: [10, 15],
        textStyle: {
          color: '#fff'
        }
      },
      zlevel: 10 // 确保点位显示在最上层
    })
  }

  // 添加检修点位
  if (props.showObstaclePoints && props.currentMap && props.currentMap.obstacles && props.currentMap.obstacles.length > 0) {
    // 创建散点图系列用于显示点位
    const pointData = []

    // 使用CAD图层的坐标系，同时考虑交互状态
    const activeScale = baseScale.value * props.cadTransform.scale * viewState.scale
    const activeTranslateX = baseX.value + props.cadTransform.translateX + viewState.translateX
    const activeTranslateY = baseY.value + props.cadTransform.translateY + viewState.translateY

    // 处理每个点位
    props.currentMap.obstacles.forEach((point, index) => {
      // 1. 首先将pt单位转换为px单位
      const pointXPx = point.newPosX * pt2px.value
      const pointYPx = point.newPosY * pt2px.value

      // 2. 然后考虑CAD图层的缩放
      const scaledX = pointXPx * activeScale
      const scaledY = pointYPx * activeScale

      // 3. 最后加上CAD图层的偏移量
      const x = activeTranslateX + scaledX
      const y = activeTranslateY + scaledY

      // 4. 因为echart是左下角原点，所以需要Y轴翻转
      const finalY = containerHeight - y
      const finalX = x

      const instanceName = point.instanceName || `检修点${index + 1}`
      const displayName = point.name || `${instanceName}`

      // 保存点位的所有属性，用于鼠标悬浮提示
      pointData.push([
        finalX,
        finalY,
        displayName,
        point.id,
        point.angle,
        point.theta,
        point.newPosX,
        point.newPosY,
        point.instanceName
      ])
    })

    // 添加散点图系列
    option.series.push({
      type: 'scatter',
      id: 'obstaclePoints',
      data: pointData,
      symbolSize: 20, // 点的大小
      symbol: 'path://M835.55 383.17l-15.77-15.77a20.83 20.83 0 0 0-29.46 0L602.87 554.87a62.5 62.5 0 0 0-11.79 15.77l-176.35-176.35a20.83 20.83 0 0 0-29.46 0l-15.77 15.77a20.83 20.83 0 0 0 0 29.46l176.35 176.35a62.5 62.5 0 0 0-15.77 11.79L343.62 814.13a20.83 20.83 0 0 0 0 29.46l15.77 15.77a20.83 20.83 0 0 0 29.46 0l186.46-186.46a62.5 62.5 0 1 0 88.39-88.39l171.85-171.85a20.83 20.83 0 0 0 0-29.46z', // 检修点使用扳手图标
      itemStyle: {
        color: '#FF5722', // 点的颜色
        borderColor: '#FFFFFF', // 边框颜色
        borderWidth: 2, // 边框宽度
        shadowBlur: 10,
        shadowColor: 'rgba(0, 0, 0, 0.5)'
      },
      label: {
        show: true,
        position: 'top',
        distance: 5,
        formatter: '{@[2]}', // 显示点位名称
        backgroundColor: '#333',
        padding: [3, 5],
        borderRadius: 3,
        color: '#fff'
      },
      tooltip: {
        formatter: function(params) {
          return `
            <div style="font-weight:bold;margin-bottom:5px;">${params.data[2]}</div>
            <div>ID: ${params.data[3] || '无'}</div>
            <div>角度: ${params.data[4] || '无'}</div>
            <div>方向: ${params.data[5] || '无'}</div>
            <div>X坐标: ${params.data[6] || '无'}</div>
            <div>Y坐标: ${params.data[7] || '无'}</div>
            <div>点位名称: ${params.data[8] || '无'}</div>
          `
        },
        backgroundColor: 'rgba(50,50,50,0.9)',
        borderColor: '#333',
        borderWidth: 1,
        padding: [10, 15],
        textStyle: {
          color: '#fff'
        }
      },
      zlevel: 10 // 确保点位显示在最上层
    })
  }

  // 处理路径信息
  addPaths(option, containerWidth, containerHeight)
}

// 添加路径
const addPaths = (option, containerWidth, containerHeight) => {
  if (props.currentMap && props.currentMap.advancedPaths && props.currentMap.advancedPaths.length > 0) {
    // 使用CAD图层的坐标系，同时考虑交互状态
    const activeScale = baseScale.value * props.cadTransform.scale * viewState.scale
    const activeTranslateX = baseX.value + props.cadTransform.translateX + viewState.translateX
    const activeTranslateY = baseY.value + props.cadTransform.translateY + viewState.translateY

    // 遍历所有路径
    props.currentMap.advancedPaths.forEach((path, index) => {
      // 1. 首先将pt单位转换为px单位
      const startXPx = path.startPos.newX * pt2px.value
      const startYPx = path.startPos.newY * pt2px.value
      const endXPx = path.endPos.newX * pt2px.value
      const endYPx = path.endPos.newY * pt2px.value
      const ctrlX1Px = path.ctrlPos1.newX * pt2px.value
      const ctrlY1Px = path.ctrlPos1.newY * pt2px.value
      const ctrlX2Px = path.ctrlPos2.newX * pt2px.value
      const ctrlY2Px = path.ctrlPos2.newY * pt2px.value

      // 2. 计算最终坐标
      const startX = activeTranslateX + startXPx * activeScale
      const startY = containerHeight - (activeTranslateY + startYPx * activeScale)
      const endX = activeTranslateX + endXPx * activeScale
      const endY = containerHeight - (activeTranslateY + endYPx * activeScale)
      const ctrlX1 = activeTranslateX + ctrlX1Px * activeScale
      const ctrlY1 = containerHeight - (activeTranslateY + ctrlY1Px * activeScale)
      const ctrlX2 = activeTranslateX + ctrlX2Px * activeScale
      const ctrlY2 = containerHeight - (activeTranslateY + ctrlY2Px * activeScale)

      // 3. 根据路径类型绘制不同的路径
      let graphicItem = null

      switch (path.routeType) {
        case 'straight_line': // 直线
          graphicItem = {
            type: 'line',
            coordinateSystem: 'cartesian2d',
            data: [[startX, startY], [endX, endY]],
            lineStyle: {
              stroke: '#4CAF50',
              width: 3
            },
            z: 5
          }
          option.series.push(graphicItem)
          break

        case 'bezier_curve': // 贝塞尔曲线
          const bezierPoints = calculateBezierPoints(
            [startX, startY],
            [ctrlX1, ctrlY1],
            [ctrlX2, ctrlY2],
            [endX, endY],
            100 // 生成100个点以获得平滑的曲线
          )

          graphicItem = {
            type: 'line',
            symbolSize: 1,
            symbol: 'bezierCurve',
            smooth: true,
            coordinateSystem: 'cartesian2d',
            data: bezierPoints,
            lineStyle: {
              opacity: 0.8,
              width: 3,
              color: '#2196F3'
            },
          }
          option.series.push(graphicItem)
          break

        case 'convex': // 凸弧线
        case 'concave_arc': // 凹弧线
          const p1 = [startX, startY]
          const p2 = [endX, endY]
          // 生成弧线点
          const arcPoints = calculateArcPoints(p1, p2, path.radian, true)

          graphicItem = {
            type: 'lines',
            coordinateSystem: 'cartesian2d',
            polyline: true,
            data: [{
              coords: arcPoints,
              lineStyle: {
                width: 3,
                color: '#4CAF50'
              }
            }],
            effect: { show: false },
            lineStyle: {
              opacity: 0.8,
              width: 1,
            },
            z: 5,
          }

          option.series.push(graphicItem)
          break

        default:
          console.warn(`未知的路径类型: ${path.routeType}`)
          break
      }
    })
  }
}

const resizeEcharts = () => {
  if (echartsInstance.value) {
    echartsInstance.value.resize()
    updateEchartsLayers()
  }
}

// 设置鼠标事件监听
const setupMouseEvents = () => {
  if (!echartsInstance.value || !echartsContainer.value) return

  const container = echartsContainer.value

  // 鼠标滚轮事件 - 缩放
  container.addEventListener('wheel', handleMouseWheel)

  // 鼠标按下事件 - 开始拖动
  container.addEventListener('mousedown', handleMouseDown)

  // 鼠标移动事件 - 拖动中
  container.addEventListener('mousemove', handleMouseMove)

  // 鼠标松开事件 - 结束拖动
  container.addEventListener('mouseup', handleMouseUp)
  container.addEventListener('mouseleave', handleMouseUp)

  // 设置初始鼠标样式
  container.style.cursor = 'grab'

  console.log('已设置鼠标事件监听')
}

// 清理鼠标事件监听
const cleanupMouseEvents = () => {
  if (!echartsContainer.value) return

  const container = echartsContainer.value

  container.removeEventListener('wheel', handleMouseWheel)
  container.removeEventListener('mousedown', handleMouseDown)
  container.removeEventListener('mousemove', handleMouseMove)
  container.removeEventListener('mouseup', handleMouseUp)
  container.removeEventListener('mouseleave', handleMouseUp)

  console.log('已清理鼠标事件监听')
}

// 处理鼠标滚轮事件 - 缩放
const handleMouseWheel = (event) => {
  event.preventDefault()

  // 获取鼠标位置相对于容器的坐标
  const rect = echartsContainer.value.getBoundingClientRect()
  const mouseX = event.clientX - rect.left
  const mouseY = event.clientY - rect.top

  // 计算缩放方向和大小
  const delta = event.deltaY > 0 ? -zoomConfig.step : zoomConfig.step
  const newScale = Math.max(
    zoomConfig.min,
    Math.min(zoomConfig.max, viewState.scale + delta)
  )

  // 如果缩放没有变化，不进行操作
  if (newScale === viewState.scale) return

  // 计算缩放中心点相对于图像的位置
  const imageX = mouseX - viewState.translateX
  const imageY = mouseY - viewState.translateY

  // 计算新的平移量，保持鼠标位置不变
  const scaleFactor = newScale / viewState.scale
  const newTranslateX = mouseX - imageX * scaleFactor
  const newTranslateY = mouseY - imageY * scaleFactor

  // 更新视图状态
  viewState.scale = newScale
  viewState.translateX = newTranslateX
  viewState.translateY = newTranslateY

  // 更新图层显示
  updateEchartsLayers()
}

// 处理鼠标按下事件 - 开始拖动
const handleMouseDown = (event) => {
  // 只响应左键
  if (event.button !== 0) return

  mouseState.isDragging = true
  mouseState.lastX = event.clientX
  mouseState.lastY = event.clientY

  // 改变鼠标样式
  echartsContainer.value.style.cursor = 'grabbing'
}

// 处理鼠标移动事件 - 拖动中
const handleMouseMove = (event) => {
  if (!mouseState.isDragging) return

  // 计算鼠标移动距离
  const deltaX = event.clientX - mouseState.lastX
  const deltaY = event.clientY - mouseState.lastY

  // 更新鼠标位置
  mouseState.lastX = event.clientX
  mouseState.lastY = event.clientY

  // 更新视图状态
  viewState.translateX += deltaX
  viewState.translateY += deltaY

  // 更新图层显示
  updateEchartsLayers()
}

// 处理鼠标松开事件 - 结束拖动
const handleMouseUp = () => {
  if (!mouseState.isDragging) return

  mouseState.isDragging = false

  // 恢复鼠标样式
  echartsContainer.value.style.cursor = 'grab'
}

const handleChartClick = (params) => {
  console.log('图表点击事件:', params)
}

// Lifecycle
onMounted(() => {
  console.log('CenterPanelMain mounted')
  initEcharts()
  window.addEventListener('resize', resizeEcharts)
})

onBeforeUnmount(() => {
  console.log('CenterPanelMain unmounted')
  cleanupMouseEvents()
  window.removeEventListener('resize', resizeEcharts)
  if (echartsInstance.value) {
    echartsInstance.value.dispose()
  }
})

const updateMapElements = () => {
  console.log('更新地图元素')
  if (echartsInstance.value) {
    updateEchartsLayers()
  }
}

// 暴露方法给父组件
defineExpose({
  updateMapElements
})
</script>

<style lang="scss" scoped>
.center-panel-main {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--el-bg-color);
}

.map-container {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.placeholder-map {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.map-content {
  height: 100%;
  position: relative;
}

.echarts-container {
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  position: relative;
  cursor: grab;

  &:active {
    cursor: grabbing;
  }
}

.zoom-info {
  position: absolute;
  top: 16px;
  right: 16px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  pointer-events: none;
  z-index: 1000;

  span {
    font-family: 'Courier New', monospace;
  }
}
</style>
