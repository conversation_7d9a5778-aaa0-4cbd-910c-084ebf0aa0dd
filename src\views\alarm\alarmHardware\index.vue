<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="88px">
         <el-form-item label="报警内容" prop="alarmContent">
            <el-input
               v-model="queryParams.alarmContent"
               placeholder="请输入报警内容"
               clearable
               style="width: 240px"
               @keyup.enter="handleQuery"
            />
         </el-form-item>
         <el-form-item label="机器人名称" prop="robotName">
            <el-input
               v-model="queryParams.robotName"
               placeholder="请输入机器人名称"
               clearable
               style="width: 240px"
               @keyup.enter="handleQuery"
            />
         </el-form-item>
         <el-form-item label="报警时间" style="width: 438px;">
            <el-date-picker
               v-model="dateRange"
               value-format="YYYY-MM-DD HH:mm:ss"
               type="datetimerange"
               range-separator="至"
               start-placeholder="开始日期"
               end-placeholder="结束日期"
            ></el-date-picker>
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
         </el-form-item>
      </el-form>


      <el-table v-loading="loading" :data="dataList">
         <el-table-column label="记录时间" align="center" prop="createTime" width="180">
            <template #default="scope">
               <span>{{ parseTime(scope.row.createTime) }}</span>
            </template>
         </el-table-column>
         <el-table-column label="机器人名称" align="center" prop="robotName" />
         <el-table-column label="报警内容" align="center" prop="alarmContent" :show-overflow-tooltip="true" />

      </el-table>

      <pagination
         v-show="total > 0"
         :total="total"
         v-model:page="queryParams.pageNum"
         v-model:limit="queryParams.pageSize"
         @pagination="getList"
      />


   </div>
</template>

<script setup name="AlarmEscort">


import { listPageList } from "@/api/alarm/alarmHardware"


const { proxy } = getCurrentInstance()
const { sys_yes_no, rule_result, iden_type, sign_type ,confirm_status} = proxy.useDict("sys_yes_no", "rule_result", "iden_type", "sign_type", "confirm_status")

const dataList = ref([])
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const dateRange = ref([])



const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    alarmContent: undefined,
    robotName: undefined
  }
})

const { queryParams} = toRefs(data)




/** 查询陪同报警列表 */
function getList() {
  loading.value = true
  if(dateRange.value.length > 0){
    queryParams.value.beginTime = dateRange.value[0]
    queryParams.value.endTime = dateRange.value[1]
  }
  listPageList(queryParams.value).then(response => {
    dataList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}


/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = []
   queryParams.value.beginTime = null;
  queryParams.value.endTime = null;
  proxy.resetForm("queryRef")
  handleQuery()
}




getList()
</script>
