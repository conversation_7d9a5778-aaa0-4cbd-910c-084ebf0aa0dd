import request from '@/utils/request'

const add = (params) =>{
	return request.post('logIndustrialControl/add', params)
}

const remove = (ids) =>{
	return request.get('logIndustrialControl/remove/' + ids)
}

const edit = (params) =>{
	return request.post('logIndustrialControl/edit', params)
}

const getList = (params) =>{
	return request.get('logIndustrialControl/getList', {params: params})
}

const getAll = (params) =>{
	return request.get('logIndustrialControl/getAll', {params: params})
}

const getLogTypeList = () =>{
	return request.get('logIndustrialControl/getLogTypeList')
}

const getOne = (id) =>{
	return request.get('logIndustrialControl/getOne/' + id)
}

export default {
	add, remove, edit, getList, getAll, getOne, getLogTypeList
}