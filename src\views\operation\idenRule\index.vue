<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true">
      <el-form-item label="规则名称" prop="idenRuleName">
        <el-input
            v-model="queryParams.idenRuleName"
            placeholder="请输入规则名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item label="规则类别" >
        <el-select placeholder="请选择规则类别" style="width: 180px;" clearable v-model="queryParams.idenRuleType">
          <el-option v-for="dict in iden_rule_type" :key="dict.value" :label="dict.label" :value="dict.value" ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="点位模型" >
        <el-select placeholder="请选择点位模型" style="width: 180px;" clearable v-model="queryParams.idenModelId" >
          <el-option v-for="dict in idenModelList" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-form :model="queryParams" ref="queryRef" :inline="true">
      <el-form-item>
        <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
        <el-button type="danger" :icon="Delete" plain icon="Plus" @click="handleDeleteAll">删除</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="table.tableList" style="width: 100%;" @selection-change="selectionChange">
      <el-table-column type="selection" width="55"/>
      <el-table-column label="序号" width="50" type="index" align="center">
        <template #default="scope">
          <span>{{ (table.pageNum - 1) * table.pageSize + scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="规则名称" align="center" prop="idenRuleName" :show-overflow-tooltip="true"/>
      <el-table-column label="规则类别" align="center" prop="idenRuleType" :show-overflow-tooltip="true">
        <template #default="scope">
          <div v-for=" item in filteredIdenTypes(scope.row.idenRuleType)">
            {{ item.label }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="点位模型" align="center" prop="idenModelId" :show-overflow-tooltip="true">
        <template #default="scope">
          <div v-for=" item in filteredIdenModelId(scope.row.idenModelId)">
            {{ item.label }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="规则数量" align="center" prop="ruleNum" :show-overflow-tooltip="true"/>

      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Notebook" @click="handleRulesList(scope.row)"
                     v-hasPermi="['monitor:online:forceLogout']">规则列表
          </el-button>
          <el-button link type="primary" icon="Edit" @click="handleEdit(scope.row)"
                     v-hasPermi="['monitor:online:forceLogout']">编辑
          </el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
                     v-hasPermi="['iden:rule:remove']">删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="table.total > 0" :total="table.total" v-model:page="table.pageNum"
                v-model:limit="table.pageSize" @pagination="getList"/>

    <!-- 添加规则 -->
    <el-dialog :title="rulesForm.title" v-model="rulesForm.dialogOpen" width="820px" append-to-body>
      <el-form ref="rulesFormRef" :model="rulesForm" :rules="rules" label-width="120px">
        <el-form-item label="规则名称" prop="idenRuleName">
          <el-input v-model="rulesForm.idenRuleName" placeholder="请输入任务名称"/>
        </el-form-item>
        <el-form-item label="规则类别" prop="idenRuleType">
          <el-select v-model="rulesForm.idenRuleType" placeholder="请选择">
            <el-option v-for="dict in iden_rule_type" :key="dict.value" :label="dict.label"
                       :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="点位模型" prop="idenModelId" v-if="rulesForm.idenRuleType=='1'">
          <el-select v-model="rulesForm.idenModelId" placeholder="请选择">
            <el-option v-for="dict in idenModelList" :key="dict.value" :label="dict.label"
                       :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm()">确 定</el-button>
          <el-button @click="rulesForm.dialogOpen=false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <IdenRuleExpr ref="idenRuleExpr"  ></IdenRuleExpr>
  </div>
</template>

<script setup name="IdenRule">

import {
  list as initData,
  addIdenRule,
  updateIdenRule,
  remove,
  removeAll,
  getIdenModelList
} from "@/api/operation/idenRule"

import IdenRuleExpr from "@/views/operation/idenRuleExpr"
import {Delete} from "@element-plus/icons-vue";

const {proxy} = getCurrentInstance()
const {iden_rule_type} = proxy.useDict("iden_rule_type")
const table = reactive({tableList: [], total: 0, pageNum: 1, pageSize: 10})
const queryParams = reactive({idenRuleName: '',idenRuleType:'',idenModelId:''})
const loading = ref(true)
const selectable = ref([])
const data = reactive({
  rulesForm: {dialogOpen: false, title: '', id: '', idenRuleName: '', idenRuleType: '', idenModelId: ''},
  rules: {
    idenRuleName: [{required: true, message: "规则名称不能为空", trigger: "blur"}],
    idenRuleType: [{required: true, message: "规则类别不能为空", trigger: "change"}],
    idenModelId: [{required: true, message: "点位模型不能为空", trigger: "change"}]
  }
})
const {rulesForm, rules} = toRefs(data)
const idenModelList = ref([]);


function selectionChange(item) {
  selectable.value = item;
}


function initidenModelList() {
  loading.value = true
  getIdenModelList().then(response => {
    idenModelList.value = [];
    response.data.forEach(item => idenModelList.value.push({value: item.id, label: item.idenModelName}));
    handleQuery();
  })
}



function getList() {
  loading.value = true

  initData({
    idenRuleType:queryParams.idenRuleType,
    idenModelId:queryParams.idenModelId,
    idenRuleName: queryParams.idenRuleName,
    pageNum: table.pageNum,
    pageSize: table.pageSize
  }).then(response => {
    table.tableList = response.rows
    table.total = Number(response.total)
    loading.value = false
  })
}


/** 搜索按钮操作 */
function callSearch() {
  getList()
}

/** 搜索按钮操作 */
function handleQuery() {
  table.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  queryParams.idenRuleName = '';
  queryParams.idenRuleType = '';
  queryParams.idenModelId = '';
  handleQuery()
}

/** 规则列表 */
function handleRulesList(row) {
  proxy.$refs["idenRuleExpr"].showTableList(row)
}


/** 删除按钮操作 */
function handleDeleteAll() {
  proxy.$modal.confirm('是否批量删除数据项?').then(function () {
    removeAll(selectable.value.map(item => item.id))
  }).then(() => {
    selectable.value = [];
    handleQuery()
    if (res.code=='200'){
      proxy.$modal.msgSuccess("删除成功")
    }else{
      proxy.$modal.msgError(res.message)
    }
  }).catch(() => {
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$modal.confirm('是否确认删除规则名称为"' + row.idenRuleName + '"的数据项?').then(function () {
    return remove(row.id)
  }).then(res => {
    handleQuery()
    if (res.code=='200'){
      proxy.$modal.msgSuccess("删除成功")
    }else{
      proxy.$modal.msgError(res.message)
    }
  }).catch(() => {
  })
}

/** 编辑按钮操作 */
function handleEdit(row) {
  resFrom(true, "编辑规则状态");
  rulesForm.value.id = row.id;
  rulesForm.value.idenRuleName = row.idenRuleName;
  rulesForm.value.idenRuleType = row.idenRuleType + '';
  rulesForm.value.idenModelId = row.idenModelId;
  rulesForm.value.temp = row;
}

/** 添加规则操作 */
function handleAdd() {
  resFrom(true, "添加规则状态");
}

/** 添加规则操作 */
function submitForm() {
  proxy.$refs["rulesFormRef"].validate(valid => {
    if (valid) {
      if (rulesForm.value.idenRuleType == '2' || rulesForm.value.idenRuleType == '3') {
        rulesForm.value.idenModelId = ''
      }
      if (rulesForm.value.id === '') {
        addIdenRule(rulesForm.value).then(res => {
          if (res.code == '200') {
            proxy.$modal.msgSuccess("添加成功");
            resFrom(false, '')
            handleQuery();
          } else {
            proxy.$modal.msgError(res.msg);
          }
        })
      } else {
        if (rulesForm.value.idenModelId != rulesForm.value.temp.idenModelId) {
          proxy.$modal.confirm('变更点位模型会清空规则信息,是否确认变更?').then(function () {
            updateIdenRule(rulesForm.value)
          }).then(() => {
            proxy.$modal.msgSuccess("编辑成功");
            resFrom(false, '')
            handleQuery();
          }).catch(() => {
          })
        } else {
          updateIdenRule(rulesForm.value).then(res => {
            proxy.$modal.msgSuccess("编辑成功");
            resFrom(false, '')
            handleQuery();
          })
        }
      }
    }
  })
}


/**重置表单**/
function resFrom(dialogOpen, title) {
  rulesForm.value = {dialogOpen: dialogOpen, title: title, id: '', idenRuleName: '', idenRuleType: '', idenModelId: ''}
  try {
    proxy.$refs["rulesFormRef"].resetFields();
  } catch (e) {
  }
}

function filteredIdenModelId(idenModelId) {
  return idenModelList.value.filter(item => item.value == idenModelId);
}

function filteredIdenTypes(idenRuleType) {
  return iden_rule_type.value.filter(item => item.value == idenRuleType);
}

onMounted(()=>{
  /**初始化加载*/
  initidenModelList()
})
provide('callSearch', callSearch);
</script>
