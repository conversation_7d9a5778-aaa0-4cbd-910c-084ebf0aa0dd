<template>
  <div class="app-container"  v-loading="loading">

    <el-row :gutter="20">
      <el-col :span="4">
        <el-card style="height: calc(100vh - 160px);">
          <el-input
              v-model="tree.filterText"
              class="w-60 mb-2"
              placeholder="查询"
          />
          <div style="height: calc(100vh - 160px); overflow-y: auto; margin-top: 5px;">
          <el-tree
              ref="treeRef"
              style="max-width: 600px;"
              class="filter-tree"
              :data="tree.treeData"
              :props="tree.defaultProps"
              :filter-node-method="filterNode"
              default-expand-all
              :render-content="renderContent"
          />
          </div>
        </el-card>
      </el-col>
      <el-col :span="20" >
        <el-card>
          <el-form ref="queryForm" :inline="true" label-width="68px">
            <el-form-item label="时间范围" prop="alias">
              <el-date-picker
                  v-model="times"
                  type="datetimerange"
                  range-separator="-"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" :icon="Search" @click="search">搜索</el-button>
              <el-button type="success" :icon="Plus" @click="exportExecl"> 导出</el-button>
            </el-form-item>
          </el-form>
        </el-card>
        <el-card style="margin-top: 10px;">
          <div id="historyChart" ref="historyChart" style="width: 100%;height: calc(100vh - 295px);">

          </div>

        </el-card>

      </el-col>
    </el-row>

    <el-dialog v-model="chartsListData.show" title="历史数据" style="width: 1500px;">

        <el-table :data="chartsListData.table" border style="width: 100% " :height="600" >
          <el-table-column v-for="(item, index) in chartsListData.header" :key="index" :label="item" :prop="item">
          </el-table-column>
        </el-table>
    </el-dialog>
  </div>
</template>

<script setup>
import {getMoreHistoryCurve, getTreeList} from '@/api/datacenter/ca'
import * as echarts from "echarts";
import {Plus, Search} from "@element-plus/icons-vue";
import dayjs from 'dayjs';
import * as XLSX from 'xlsx';


const tree = reactive({
  filterText: '',
  treeData: [],
  defaultProps: {children: 'children', label: 'label'},
  checkBoxList: []
})
const loading = ref(false)
const treeRef = ref(null)
const historyChart = ref(null)
const times = ref([dayjs(new Date()).format('YYYY-MM-DD') + " 00:00:00", dayjs(new Date(Date.parse(new Date()) + 86400000)).format('YYYY-MM-DD') + " 00:00:00"]);
const {proxy} = getCurrentInstance()
var chartInstance = null;
const option = {
  toolbox: {
    feature: {
      myCustomButton: {
        show: true,
        title: '数据列表',
        icon: 'path://M10 10 L20 20 L30 10 Z',  // 自定义图标的 SVG 路径
        onclick:()=> chartsList()
      },
      saveAsImage: {}
    }
  },
  grid: {
    left: '1%',
    right: '1%',
    bottom: '3%',
    containLabel: true
  },
  title: {
    text: '对比分析曲线'
  },
  tooltip: {
    trigger: 'item',
    formatter: function (params) {

      let tooltipContent = '<div>';
      tooltipContent += params.marker;
      tooltipContent += '时间: ' + echarts.format.formatTime('yyyy-MM-dd hh:mm', params.value[0]) + '&nbsp;'; // 显示时间
      tooltipContent += '值: ' + params.value[1] + '<br>'; // 显示数值
      tooltipContent += '</div>';
      return tooltipContent;
    },
  },
  legend: {
    data: [],
    orient: 'horizontal',  // 水平布局
    width: '70%',
  },
  xAxis: {
    type: 'time',
    axisLabel: {
      formatter: function (value) {
        return echarts.format.formatTime('MM-dd hh:mm', value); // 格式化时间
      }
    }
  },
  yAxis: [{}],
  color: ['#FF5733', '#33FF57', '#3357FF', '#F4C542','#62143e','#000000','#fff166','#99f5ff','#99fffa','#800025'],
  series: [],
  dataZoom: [
    {
      type: 'slider',  // 这是滑动条
      show: true,      // 显示滑动条
      xAxisIndex: [0], // 与 x 轴绑定
    }
  ]
}

const chartsListData=reactive({show:false,header:[],table:[]})

function chartsList(){
  chartsListData.header=[];
  chartsListData.table = [];
  chartsListData.header.push("时间")
  option.series.forEach(item=> chartsListData.header.push(item.name));
  let setTimes = [...new Set(option.series.flatMap(item => item.data.map(child => child[0])))];
  chartsListData.table = setTimes.map(time => {
    const data = {'时间': echarts.format.formatTime('yyyy-MM-dd hh:mm',time)};
    option.series.forEach(ser => {
      const item = ser.data.find(res => res[0] === time);
      data[ser.name] = (item ? item[1] : '');
    });
    return data;
  });
  chartsListData.show=true;
}

//初始化echarts
function initChart() {
  chartInstance = echarts.init(historyChart.value)
  chartInstance.setOption(option)
  window.addEventListener('resize', function () {
    chartInstance.resize();
  });
}


function search() {

  if (tree.checkBoxList.length == 0) {
    resetCharts();
    proxy.$modal.msgError("请选择点位参数");
    return;
  }
  if (times.value.length == 0) {
    resetCharts();
    proxy.$modal.msgError("请选择时间");
    return;
  }
  if (tree.checkBoxList.length > 10) {
    proxy.$modal.msgError("最大支持10条曲线");
    return;
  }
  loading.value = true;

  let params = tree.checkBoxList.map(node => {
    return {startTime: times.value[0], endTime: times.value[1], bizPointId: node.parentId, idenModelParamId: node.id,className:node.className}
  });
  getMoreHistoryCurve({paramList: params}).then(res => {
    resetCharts();
    res.data.forEach((item,i) => {
      const node = tree.checkBoxList.find(box => box.id == item.idenModelParamId && box.parentId==item.bizPointId);
      const name = node.parentName+'-'+node.label;
      const id = node.parentId+'-'+ node.id;
      let x = {id: id, name: name,yAxisIndex:i+1, type: 'line', data: []}
      let y = {type: 'category', position:'left', data: [],axisLabel: {interval: 0 },offset: 0, axisLine: {show: true,lineStyle: {color: option.color[i]}}}
      if (node.dataType == '2') {
        y.type = 'category'
        y.data = JSON.parse(node.valueScopeJson)
      } else {
        y.type = 'value'
      }
      item.data.forEach(item => { x.data.push([new Date(item.idenTime).getTime(), item.dataType == 1 ? item.floatValue : item.enumValue]) })
      y.position = (i+1)%2!==0?'left':'right';
      if (i>1){
        let num = option.yAxis.filter(child=> child.position==y.position).length;
        if (num>0){
          y.offset = num*40;
        }
      }
      option.yAxis.push(y)
      option.series.push(x)
      option.legend.data.push(name)
    })
    let l = option.series.length;
    if (l>2){
      option.grid.left=l+'%';
      option.grid.right=l+'%';
    }
    chartInstance.setOption(option, true);
    loading.value = false;
  }).catch(error=> loading.value = false)
}

function resetCharts() {
  option.yAxis= [{}];
  option.xAxis.data = [];
  option.series = [];
  option.legend.data=[];
  option.grid.left  = '1%';
  option.grid.right = '1%';
  chartInstance.setOption(option, true);
}

/*****树****/
watch(() => tree.filterText, (val) => {
  treeRef.value.filter(val)
})

function filterNode(value, data) {
  if (!value) return true
  return data.label.includes(value)
}

function renderContent(h, {node, data}) {
  if (data.checkBoxType) {
    return h('span', [
      h('input', {
        type: 'checkbox', onclick: (event) => {
          checkBoxClick(event, data)
        }
      }),
      h('span', data.label)
    ])
  } else {
    return h('span', data.label)
  }
}

function checkBoxClick(object, data) {
  if (object.target.checked) {
    tree.checkBoxList.push(data);
    search();
  } else {
    tree.checkBoxList = tree.checkBoxList.filter(item => item.id != data.id);
    search();
  }
}

function getTree() {
  getTreeList().then(res => {
    tree.treeData = res.data.treeList;
  })
}

function exportExecl() {
  let execlData = [];
  let execlHeader= [];
  execlHeader.push("时间")
  option.series.forEach(item=> execlHeader.push(item.name));
  execlData.push(execlHeader)
  let setTimes = [...new Set(option.series.flatMap(item => item.data.map(child => child[0])))];
  setTimes.forEach(time => {
    const data = [echarts.format.formatTime('yyyy-MM-dd hh:mm',time)];
    option.series.forEach(ser => {
      const item = ser.data.find(res => res[0] === time);
      data.push(item ? item[1] : '');
    });
    execlData.push(data)
  });
    const ws = XLSX.utils.aoa_to_sheet(execlData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, '对比分析');
    XLSX.writeFile(wb, '对比分析.xlsx');
}



onMounted(() => {
  getTree();
  initChart();
})
</script>

<style>


</style>
