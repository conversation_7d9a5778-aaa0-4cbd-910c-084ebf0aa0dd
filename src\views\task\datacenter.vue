<template>
	<div class="app-container">
		<el-row>
			<el-col :span="4">
				<leftTree></leftTree>
			</el-col>
			<el-col :span="20">
				<taskInstance ref="taskInstanceRef"></taskInstance>
			</el-col>
		</el-row>
	</div>
</template>

<script setup>
	import leftTree from "@/views/datacenter/tree/index";
	import taskInstance from "./taskInstance";
	import { dataCenterTreeStore } from '@/store/modules/DataCenterTree'
	
	const globalStore = dataCenterTreeStore()
	const { proxy } = getCurrentInstance()
	
	watch(() => globalStore.treeId, (newValue) => {
	  proxy.$refs.taskInstanceRef.datacenterSend(newValue)
	})
	
	
</script>

<style>

</style>