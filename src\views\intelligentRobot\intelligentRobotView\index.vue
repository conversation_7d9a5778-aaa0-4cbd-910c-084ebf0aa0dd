<template>
  <div class="app-container">
    <el-row :gutter="20">

      <div class="content_box">
        <div class="content_table_item" v-for="robot in robotList">
          <el-card :body-style="{ padding: '0px !important' }" class="ft-card" :style="getStyle(robot.status)" >
            <div class="ft-head">

              <div class="ft-tag">
                <span class="ml-title">
                 <el-popover placement="right"  trigger="click">
                    <template #reference>
                      <el-button link type="primary">{{robot.robotName}}</el-button>
                    </template>
                   <el-form>
                    <el-form-item label="当前位置" label-position="right" style="text-align: right;">{{ robot.reliability }}</el-form-item>
                    <el-form-item label="移动速度" label-position="right" style="text-align: right;">{{ robot.velocity }}</el-form-item>
                    <el-form-item label="电池电量" label-position="right" style="text-align: right;"> {{ robot.battery }}</el-form-item>
                   </el-form>

                  </el-popover>
                </span>
                <el-tag class="ml-3" :type="getTagStyle(robot.status)" style="float: right;">{{ coverRobotStatus(robot.status) }}</el-tag>
              </div>
              <div class="ft-body">
                <div class="ft-body-image">
                  <el-image style="height: 100%" src="/profile.jpg" :initial-index="0" :zoom-rate="1.2" fit="fill"></el-image>
                </div>
                <div class="ft-body-item">
                  <div class="item-mb">序列号： {{ robot.sn }}</div>
                  <div class="item-mb">型号： {{ robot.robotModel }}</div>
                  <div class="item-mb">运行里程： {{ coverm(robot.robotDynamicAttr.totalOdom) }}</div>
                  <div class="item-mb">运行时间： {{ covers(robot.robotDynamicAttr.totalRunning) }}</div>
                  <div class="item-mb">投运时间：{{ formatDate(robot.beginUseTime) }}</div>
                </div>
              </div>
            </div>

          </el-card>
        </div>
      </div>
      <el-dialog  v-model="viewForm.open" width="200px" append-to-body>
        <el-form >
          <el-form-item label="当前位置" label-position="right" style="text-align: right;">
            暂无
          </el-form-item>
          <el-form-item label="移动速度" label-position="right" style="text-align: right;">
            暂无
          </el-form-item>
          <el-form-item label="电池电量" label-position="right" style="text-align: right;">

          </el-form-item>
        </el-form>
      </el-dialog>
    </el-row>

  </div>
</template>

<script setup>
import {list as initData} from "@/api/intelligentRobot/intelligentRobotView"
import dayjs from "dayjs";
const robotList = ref([])
const {proxy} = getCurrentInstance()
const viewForm =reactive({open:false})
//获取列表
function getList() {
  initData({}).then(res => {
    robotList.value = res.data;
  })
}



function coverm(totalOdom) {
  if (totalOdom == null) {
    return "0m"
  }
  if (totalOdom < 1000) {
    return totalOdom + "m"
  } else {
    return (totalOdom / 1000).toFixed(2) + "km"
  }
}

function covers(totalOdom) {
  const days = Math.floor(totalOdom / (24 * 3600));  // 计算天数
  totalOdom %= (24 * 3600);  // 获取剩余的秒数
  const hours = Math.floor(totalOdom / 3600);  // 计算小时数
  totalOdom %= 3600;  // 获取剩余的秒数
  const minutes = Math.floor(totalOdom / 60);  // 计算分钟数
  const remainingSeconds = totalOdom % 60;  // 剩余的秒数
  //`${days}天 ${hours}小时 ${minutes}分钟 ${remainingSeconds}秒`;
  return `${days}天 ${hours}小时`;
}
function formatDate(date) {
  if (date == null) {
    return "";
  }
  return dayjs(new Date(date)).format('YYYY-MM-DD HH:ss')
}
function getStyle(status) {
   status = Number(status);
   if (status==0||status==8){
     return ' background:linear-gradient(0deg,rgb(222, 225, 227) 0%,rgba(255,255,255,0) 70%)';
   }
  if (status==1){
      return ' background:linear-gradient(0deg,rgb(216.2, 246, 240) 0%,rgba(255,255,255,0) 70%)';
  }
  if (status==4||status==6){
    return ' background:linear-gradient(0deg,#e8f3ff 0%,rgba(255,255,255,0) 70%)';
  }
  if (status==701||status==702||status==703||status==9){
    return ' background:linear-gradient(0deg,rgb(253, 225.6, 225.6) 0%,rgba(255,255,255,0) 70%)';
  }
}

function getTagStyle(status) {
  status = Number(status);
  if (status==0||status==8){
    return 'info';
  }
  if (status==1){
    return 'success';
  }
  if (status==4||status==6){
    return 'primary';
  }
  if (status==701||status==702||status==703||status==9){
    return 'danger';
  }
}

function coverRobotStatus(status) {
  let statusName;
  status = Number(status);
  switch (status) {
    case 0:
      statusName = "待机";
      break;
    case 1:
      statusName = "工作";
      break;
    case 4:
      statusName = "暂停";
      break;
    case 6:
      statusName = "定位中";
      break;
    case 701:
      statusName = "载入地图失败";
      break;
    case 702:
      statusName = "定位失败";
      break;
    case 703:
      statusName = "导航失败";
      break;
    case 8:
      statusName = "离线";
      break;
    case 9:
      statusName = "故障";
      break;
    default:
      statusName = '';
      break
  }
  return statusName;
}



getList();
</script>

<style scoped>
.content_box {
  width: 100%;
  display: flex;
  flex-wrap: wrap;

  .content_table_item {
    position: relative;
    margin-left: 48px;
    margin-top: 20px;
    width: 400px;
    height: 195px;
    box-sizing: border-box;
    flex-direction: column;
  }

}

.el-card.ft-card {
  width: 100%;
  height: 100%;

  .ft-tag {
    padding: 10px;
    height: 40px;

    .ml-3 {
      margin-left: 6px;
    }

    .ml-title {
      font-size: 14px;
    }

    border-bottom: 1px solid var(--el-card-border-color);
  }

  .ft-head {
    width: 100%;
    height: 170px;

  }

  .ft-body {
    margin-top: 5px;
    width: 400px;
    height: 130px;
    padding: 10px;
    display: flex;
    justify-content: space-between;

    .ft-body-image {
      width: 40%;
      height: 100px;
      text-align: center;
      margin-right: 10px;
    }

    .ft-body-item {
      width: 60%;
    }

    .item-mb {
      width: 100%;
      margin-bottom: 8px;
      text-overflow: ellipsis;
      font-size: 14px;
    }
  }
}


</style>
