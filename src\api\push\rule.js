import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/hyc";


// 查询规则列表
export function listRule(query) {
    return request({
      url: '/push/rule/list',
      method: 'get',
      params: query
    })
}

// 查询规则详细
export function getRule(id) {
    return request({
      url: '/push/rule/getById/' + parseStrEmpty(id),
      method: 'get'
    })
  }



// 新增规则
export function addRule(data) {
    return request({
      url: '/push/rule/add',
      method: 'post',
      data: data
    })
  }
  
  // 修改规则
  export function updateRule(data) {
    return request({
      url: '/push/rule/update',
      method: 'put',
      data: data
    })
  }

// 删除规则
export function delRule(id) {
    return request({
      url: '/push/rule/delete/' + id,
      method: 'delete'
    })
  }

  export function getPointTree() {
    return request({
      url: '/push/rule/getPointTree' ,
      method: 'get'
    })
  }
  
