<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true">

      <el-form-item label="执行时间" prop="alias">
        <el-date-picker
            v-model="times"
            type="datetimerange"
            range-separator="-"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
        />
      </el-form-item>

      <el-form-item label="任务名称" prop="taskName">
        <el-input
            v-model="queryParams.taskName"
            placeholder="请输入任务名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="机器人" prop="taskName">
        <el-select v-model="queryParams.robotId" placeholder="请选择机器人" filterable clearable style="width: 180px;">
          <el-option v-for="dict in robotList" :key="dict.robotId" :label="dict.robotName"
                     :value="dict.robotId"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="执行状态" prop="taskName">
        <el-select v-model="queryParams.status" placeholder="请选择执行状态" clearable style="width: 180px;">
          <el-option v-for="dict in status" :key="dict.key" :label="dict.value" :value="dict.key"></el-option>
        </el-select>
      </el-form-item>


      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-form :model="queryParams" ref="queryRef" :inline="true">
      <el-form-item>
        <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="tableList">
      <el-table-column label="任务名称" align="center" prop="taskName"/>
      <el-table-column label="机器人" align="center">
        <template #default="scope">
          {{ getRobotName(scope.row) }}
        </template>
      </el-table-column>
      <el-table-column label="等待点位" align="center">
        <template #default="scope">
          {{ getBizPoint(scope.row.waitPointId) }}
        </template>
      </el-table-column>
      <el-table-column label="到达点位" align="center">
        <template #default="scope">
          {{ getBizPoint(scope.row.arrivePointId) }}
        </template>
      </el-table-column>
      <el-table-column label="执行时间" align="center" prop="execTime"/>
      <el-table-column label="等待超时时间（分）" align="center" prop="timeoutMin"/>
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          {{ getStatus(scope.row.status) }}
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" :show-overflow-tooltip="true"/>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Notebook" @click="handleEdit(scope.row)" v-if="scope.row.status!='009'">编辑
          </el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-if="scope.row.status=='001'">删除
          </el-button>
          <el-button link type="primary" icon="VideoPlay" @click="startTasks(scope.row)" v-if="scope.row.status=='001'">立刻执行
          </el-button>
          <el-button link type="primary" icon="VideoPlay" @click="endTasks(scope.row)" v-if="scope.row.status!='001'&&scope.row.status!='009'">终止任务
          </el-button>

          <el-button link type="primary" icon="Tickets" @click="handleLog(scope.row)">日志
          </el-button>
          <el-button link type="primary" icon="Bell" v-if="scope.row.pauseMin==null&&scope.row.status!='009'" @click="closeAlarmShow(scope.row.id)">关闭报警</el-button>
          <el-button link type="primary" icon="Bell" v-if="scope.row.pauseMin!=null&&scope.row.status!='009'" @click="resetAlarm(scope.row)">恢复报警</el-button>


        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="queryParams.total > 0" :total="queryParams.total" v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize" @pagination="getList"/>

    <!-- 编辑任务 -->
    <el-dialog :title="taskForm.title" v-model="taskForm.dialogOpen" style="width:500px;" append-to-body>
      <el-form ref="taskFormRef" :model="taskForm" :rules="rules" label-width="120px">
        <el-form-item label="任务名称" prop="taskName" label-width="130">
          <el-input v-model="taskForm.taskName" placeholder="请输入任务名称"/>
        </el-form-item>
        <el-form-item label="机器人" prop="robotId" label-width="130">
          <el-select v-model="taskForm.robotId" placeholder="请选择机器人" filterable clearable :disabled="taskForm.taskStatus!=undefined&&taskForm.taskStatus!='001'" >
            <el-option v-for="dict in robotList" :key="dict.robotId" :label="dict.robotName"
                       :value="dict.robotId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="等待点位" prop="waitPointId" label-width="130">
          <el-select v-model="taskForm.waitPointId" placeholder="请选择等待点位" filterable clearable>
            <el-option v-for="dict in bizPointList" :key="dict.id" :label="dict.instanceName"
                       :value="dict.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="到达点位" prop="arrivePointId" label-width="130">
          <el-select v-model="taskForm.arrivePointId" placeholder="请选择到达点位" filterable clearable>
            <el-option v-for="dict in bizPointList" :key="dict.id" :label="dict.instanceName"
                       :value="dict.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="执行类型" prop="execType" label-width="130">
          <el-select v-model="taskForm.execType" placeholder="请选择执行类型" :disabled="taskForm.taskStatus!=undefined&&taskForm.taskStatus!='001'" >
            <el-option v-for="dict in exec_type" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="执行时间" prop="execTime" label-width="130" v-if="taskForm.execType=='2'">
          <el-date-picker v-model="taskForm.execTime" type="datetime" placeholder="请选择执行时间"
                          value-format="YYYY-MM-DD HH:mm:ss" :disabled="taskForm.taskStatus!=undefined&&taskForm.taskStatus!='001'" /> &nbsp;
        </el-form-item>
        <el-form-item label="等待超时时间(分)" prop="timeoutMin" label-width="130">
          <el-input v-model="taskForm.timeoutMin" placeholder="请输入等待超时时间"/>
        </el-form-item>
        <el-form-item label="UWB卡" prop="uwbCardId" label-width="130">
          <el-select v-model="taskForm.uwbCardId" placeholder="请选UWB卡">
            <el-option v-for="dict in 1" :key="dict" :label="'测试'" :value="dict"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="主办负责人" prop="superintendent"   label-width="130">
          <el-select v-model="taskForm.superintendent" multiple clearable placeholder="请选择主办负责人">
            <el-option v-for="dict in userList" :key="dict.userId" :label="dict.nickName"
                       :value="dict.userId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark" label-width="130">
          <el-input type="textarea" v-model="taskForm.remark"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm()">确 定</el-button>
          <el-button @click="taskForm.dialogOpen=false">取 消</el-button>
        </div>
      </template>
    </el-dialog>


    <el-dialog :title="'日志'" v-model="logDaLog.open" style="width:800px;">
      <el-table v-loading="loading" :data="logList">
        <el-table-column label="序号" width="50" type="index" align="center">
          <template #default="scope">
            <span>{{ (logDaLog.pageNum - 1) * logDaLog.pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column label="时间" align="center" prop="createTime"/>
        <el-table-column label="日志内容" align="center" prop="content"/>
      </el-table>
      <pagination v-show="logDaLog.total > 0" :total="logDaLog.total" v-model:page="logDaLog.pageNum" v-model:limit="logDaLog.pageSize" @pagination="getLogList"/>
    </el-dialog>


    <el-dialog :title="'关闭报警'" v-model="alarm.open" style="width:400px;">

      <el-form  label-width="120px">
        <el-form-item label="关闭时间(分)" prop="" label-width="130">
          <el-input v-model="alarm.pauseMin" type="Number"  maxlength="3" placeholder="请输入关闭时间"/>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="closeAlarmSubmit()">确 定</el-button>
          <el-button @click="alarm.open=false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="taskEscort">
import {
  addTaskEscort,
  editTaskEscort,
  getDict,
  getLogs,
  list,
  removeTaskEscort,
  startTask,
  closeAlarm,
  openAlarm,
  endTask
} from "@/api/robot/taskEscort"
import dayjs from "dayjs";

const {proxy} = getCurrentInstance()
const {exec_type} = proxy.useDict("exec_type")
const tableList = ref([])
const robotList = ref([])
const bizPointList = ref([])
const userList = ref([])
const logList = ref([])
const logDaLog = reactive({open: false, taskEscortId: '', total: 0, pageNum: 1, pageSize: 10})
const times = ref([dayjs(new Date()).format('YYYY-MM-DD') + " 00:00:00", dayjs(new Date(Date.parse(new Date()) + 86400000)).format('YYYY-MM-DD') + " 00:00:00"]);
const status = ref([
  {key: "001", value: "未开始"},
  {key: "009", value: "终止"},
  {key: "101", value: "接访客"},
  {key: "102", value: "抵达等待点"},
  {key: "109", value: "终止"},
  {key: "201", value: "陪同中"},
  {key: "202", value: "暂停"},
  {key: "209", value: "终止"},
  {key: "301", value: "抵达"},
  {key: "302", value: "暂停报警"},
  {key: "309", value: "终止"},
  {key: "401", value: "送访客"},
  {key: "402", value: "抵达等待点"},
  {key: "409", value: "终止"},
  {key: "501", value: "已完成"}
]);
const queryParams = reactive({
  taskName: '',
  robotId: '',
  status: '',
  startTime: '',
  endTime: '',
  total: 0,
  pageNum: 1,
  pageSize: 10
})
const loading = ref(true)
const data = reactive({
  taskForm: {
    dialogOpen: false,
    title: '',
    id: '',
    taskName: '',
    robotId: '',
    waitPointId: '',
    arrivePointId: '',
    execType: '',
    execTime: '',
    timeoutMin: '',
    uwbCardId: '',
    superintendent: '',
    remark: ''
  },
  rules: {
    taskName: [{required: true, message: "请输入任务名称", trigger: "blur"}],
    execTime: [{required: true, message: "请选择执行时间", trigger: "blur"}],
    robotId: [{required: true, message: "请选择机器人", trigger: "change"}],
    waitPointId: [{required: true, message: "请选择等待点位", trigger: "change"}],
    arrivePointId: [{required: true, message: "请选择到达点位", trigger: "change"}],
    execType: [{required: true, message: "请选择执行时间", trigger: "change"}],
    timeoutMin: [{required: true, message: "请输入等待超时时间", trigger: "change"}],
    uwbCardId: [{required: true, message: "请选择UWB卡", trigger: "change"}],
    superintendent: [{required: true, message: "请选择主办负责人", trigger: "change"}],
  }
})
const {taskForm, rules} = toRefs(data)

const alarm=ref({open:false,id:'',pauseMin:''})


function closeAlarmShow(id){
  alarm.value.open = true;
  alarm.value.id = id;
  alarm.value.pauseMin='';
}

function closeAlarmSubmit(){
  if (alarm.value.pauseMin==''){
    proxy.$modal.msgError("请输入关闭时间！")
    return ;
  }
  closeAlarm(alarm.value).then(res=>{
    alarm.value.open = false;
    handleQuery();
    proxy.$modal.msgSuccess("关闭成功")
  })
}


function resetAlarm(row) {
  proxy.$modal.confirm('是否恢复【' + row.taskName + '】报警?').then(function () {
    openAlarm({id: row.id})
  }).then(res => {
    handleQuery()
    if (res.code=='200'){
      proxy.$modal.msgSuccess("恢复报警成功")
    }else{
      proxy.$modal.msgError(res.message)
    }

  }).catch(() => {
  })
}



function getList() {
  loading.value = true
  if (times.value.length != 0) {
    queryParams.startTime = times.value[0];
    queryParams.endTime = times.value[1];
  } else {
    queryParams.startTime = '';
    queryParams.endTime = '';
  }
  list(queryParams).then(res => {
    tableList.value = res.rows
    queryParams.total = Number(res.total)
    loading.value = false
  })
}

function getDicts() {
  getDict().then(res => {
    bizPointList.value = res.data.bizPoints;
    robotList.value = res.data.robots;
    userList.value = res.data.users;
  })
}

/** 搜索按钮操作 */
function handleQuery() {
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  times.value = [dayjs(new Date()).format('YYYY-MM-DD') + " 00:00:00", dayjs(new Date(Date.parse(new Date()) + 86400000)).format('YYYY-MM-DD') + " 00:00:00"];
  queryParams.taskName = '';
  queryParams.robotId = '';
  queryParams.status = '';
  queryParams.startTime = times[0];
  queryParams.endTime = times[1];
  queryParams.total = 0;
  queryParams.pageNum = 1;
  queryParams.pageSize = 10
  handleQuery()
}

function handleLog(row) {
  logDaLog.open = true;
  logDaLog.taskEscortId = row.id;
  getLogList()
}

function getLogList() {
  loading.value = true
  getLogs(logDaLog).then(res => {
    logList.value = res.rows
    logDaLog.total = Number(res.total)
    loading.value = false
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  if (row.status != '001') {
    proxy.$modal.msgError("当前任务不允许删除！")
    return;
  }
  proxy.$modal.confirm('是否删除【' + row.taskName + '】数据项?').then(function () {
    removeTaskEscort({id: row.id})
  }).then(res => {
    handleQuery()
    if (res.code=='200'){
      proxy.$modal.msgSuccess("删除成功")
    }else{
      proxy.$modal.msgError(res.message)
    }

  }).catch(() => {
  })
}

function startTasks(row) {
  if (row.status != '001') {
    proxy.$modal.msgError("当前任务已执行！")
    return;
  }
  proxy.$modal.confirm('是否立即执行【' + row.taskName + '】数据项?').then(function () {
    startTask({id: row.id})
  }).then(res => {
    handleQuery()
    if (res.code=='200'){
      proxy.$modal.msgSuccess("执行成功！")
    }else{
      proxy.$modal.msgError(res.message)
    }

  }).catch(() => {
  })
}


function endTasks(row) {
  if (row.status == '001') {
    proxy.$modal.msgError("当前任务未开始！")
    return;
  }
  proxy.$modal.confirm('是否终止【' + row.taskName + '】任务?').then(function () {
    endTask({id: row.id})
  }).then(() => {
    handleQuery()
    proxy.$modal.msgSuccess("执行成功！")
  }).catch(() => {
  })
}



/** 编辑按钮操作 */
function handleEdit(row) {
  taskForm.value = {
    dialogOpen: true, title: '编辑陪同任务',
    id: row.id, taskName: row.taskName, robotId: row.robotId, waitPointId: row.waitPointId
    , arrivePointId: row.arrivePointId, execType: row.execType + '', execTime: row.execTime, timeoutMin: row.timeoutMin,
    uwbCardId: Number(row.uwbCardId),  remark: row.remark,taskStatus:row.status,
  }

  taskForm.value.superintendent = JSON.parse(row.superintendent)
}

/** 添加规则操作 */
function handleAdd() {
  resetForm();
  taskForm.value.dialogOpen = true;
  taskForm.value.title = '新增陪同任务';
}


function resetForm() {
  taskForm.value = {
    dialogOpen: false,
    title: '',
    id: '',
    taskName: '',
    robotId: '',
    waitPointId: '',
    arrivePointId: '',
    execType: '',
    execTime: '',
    timeoutMin: '',
    uwbCardId: '',
    superintendent: '',
    remark: ''
  }
  try {
    proxy.$refs["taskFormRef"].resetFields();
  } catch (e) {
  }
}

function submitForm() {

  proxy.$refs["taskFormRef"].validate(valid => {
    if (valid) {
      let form = JSON.parse(JSON.stringify(taskForm.value))
      form.superintendent = JSON.stringify(form.superintendent)
      if (taskForm.value.id != '') {
        editTaskEscort(form).then(res => {
          if (res.code=='200'){
            proxy.$modal.msgSuccess("编辑成功")
            taskForm.value.dialogOpen = false;
          }else{
            proxy.$modal.msgError(res.message)
          }
          handleQuery();
        })
      } else {
        addTaskEscort(form).then(res => {
          if (res.code=='200'){
            proxy.$modal.msgSuccess("添加成功")
            taskForm.value.dialogOpen = false;
          }else{
            proxy.$modal.msgError(res.message)
          }
          handleQuery();
        })
      }

    }
  })
}


function getStatus(st) {
  let sta = status.value.find(item => item.key == st);
  if (sta != null) {
    return sta.value;
  } else {
    return '';
  }
}

function getBizPoint(pointId) {
  let point = bizPointList.value.find(item => item.id == pointId);
  if (point != null) {
    return point.instanceName;
  } else {
    return '';
  }
}

function getRobotName(row) {
  let robot = robotList.value.find(item => item.robotId == row.robotId);
  if (robot != null) {
    return robot.robotName;
  } else {
    return '';
  }
}


onMounted(() => {
  /**初始化加载*/
  getDicts();
  getList();
})
</script>
