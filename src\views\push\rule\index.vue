<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="88px">
         <el-form-item label="规则名称" prop="ruleName">
            <el-input
               v-model="queryParams.ruleName"
               placeholder="请输入规则名称"
               clearable
               style="width: 240px"
               @keyup.enter="handleQuery"
            />
         </el-form-item>
         <el-form-item label="推送类型" prop="pushType">
            <el-select v-model="queryParams.pushType" placeholder="请选择推送类型" clearable style="width: 240px">
               <el-option
                  v-for="dict in push_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
               />
            </el-select>
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
         </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
         <el-col :span="1.5">
            <el-button
               type="primary"
               plain
               icon="Plus"
               @click="handleAdd"
            >新增</el-button>
         </el-col>

         <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" v-if="false"/>
      <el-table-column label="规则名称" align="center" prop="ruleName" />
      <el-table-column label="推送渠道" align="center" prop="pushChannelNames"  :show-overflow-tooltip="true"/>
      <el-table-column label="推送类型" align="center" prop="pushType">
       <template #default="scope">
          <dict-tag :options="push_type" :value="scope.row.pushType" />
        </template>
      </el-table-column>
      <el-table-column label="推送模版" align="center" prop="templateName" />
      <el-table-column label="推送人员" align="center" prop="userNames" :show-overflow-tooltip="true"/>
      <el-table-column label="推送点位" align="center" prop="pointNames" :show-overflow-tooltip="true"/>
      <el-table-column label="备注" align="center" prop="remark" :show-overflow-tooltip="true"/>
      <el-table-column label="创建时间" align="center" prop="createBy" width="180">
        <template #default="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
        </el-table-column>
      <el-table-column label="操作" align="center" width="180" class-name="small-padding fixed-width">
        <template #default="scope">
               <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" >修改</el-button>
               <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" >删除</el-button>
        </template>
        </el-table-column>
      </el-table>

      <pagination
         v-show="total > 0"
         :total="total"
         v-model:page="queryParams.pageNum"
         v-model:limit="queryParams.pageSize"
         @pagination="getList"
      />

      <!-- 添加或修改规则配置对话框 -->
      <el-dialog :title="title" v-model="open" width="40%" append-to-body>
         <el-form ref="ruleRef" :model="form" :rules="rules" label-width="100px">
            <el-form-item label="规则名称" prop="ruleName">
               <el-input v-model="form.ruleName" placeholder="请输入规则名称" />
            </el-form-item>
            <el-form-item label="推送渠道" prop="pushChannelList">
                <el-checkbox-group v-model="form.pushChannelList">
                <el-checkbox v-for="item in push_channel" :label="item.value" :key="item.value">{{item.label}}</el-checkbox>
                </el-checkbox-group>
            </el-form-item>
             <el-form-item label="推送类型" prop="pushType">
                <el-select v-model="form.pushType" placeholder="请选择推送类型" clearable  @change="pushTypeChange">
                    <el-option v-for="dict in push_type" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="报警等级" prop="alarmLevelList" v-if="form.pushType == '1'">
                <el-checkbox-group v-model="form.alarmLevelList">
                <el-checkbox v-for="item in rule_result" :label="item.value" :key="item.value">{{item.label}}</el-checkbox>
                </el-checkbox-group>
            </el-form-item>
            <el-form-item label="推送人员" prop="userIdsList"  v-if="form.pushType == '1'">
                <el-transfer filterable :titles="['待选人员', '已选人员']" filter-placeholder="请输入人员名称" :props="{key: 'userId',label: 'userName'}"
                v-model="form.userIdsList" :data="userList"></el-transfer>
            </el-form-item>
            <el-form-item label="推送点位" prop="pointIdsList"  v-if="form.pushType == '1'">
                <el-tree
                    class="tree-border"
                    :data="pointOptions"
                    show-checkbox
                    node-key="id"
                    :check-strictly="false"
                    empty-text="加载中，请稍候"
                    ref="pointTree"
                    :props="pointDefaultProps"
                    :default-checked-keys="selectedKeys"
                    @check-change="handleCheckChange"
                    heigth="200px"
                ></el-tree>
            </el-form-item>
            <el-form-item label="推送模板">
                <el-button @click="mailAlarmTemplate = true">选择模板</el-button>&nbsp;
                <span style="color: green;" v-if="form.templateConfigId != undefined">模板已选择</span>
            </el-form-item>
            <el-form-item label="启 用">
                <el-switch v-model="form.status" :active-value="'0'" :inactive-value="'1'"></el-switch>
            </el-form-item>
            <el-form-item label="备注" prop="remark">
                <el-input type="textarea" id="textarea" v-model="form.remark" rows="5" placeholder="请输入备注"></el-input>
            </el-form-item>
         </el-form>
         <template #footer>
            <div class="dialog-footer">
               <el-button type="primary" @click="submitForm">确 定</el-button>
               <el-button @click="cancel">取 消</el-button>
            </div>
         </template>

        <el-dialog title="选择模板" width="500px" v-model="mailAlarmTemplate" append-to-body>
            <el-row :gutter="10">
                <el-col :span="12" v-for="item in templateConfigList" :key="item.id">
                <a @click="form.templateConfigId = item.id; mailAlarmTemplate = false">
                    <el-card :style="{'color':(form.templateConfigId == item.id ? 'green' : '')}" style="height: 225px;">
                    <div><span style="font-weight: bold;">名 称：</span><span>{{item.templateName}}</span></div>
                    <div><span style="font-weight: bold;">描 述：</span><span>{{item.templateDescription}}</span></div>
                    <div><span style="font-weight: bold;">内 容：</span><span>{{item.templateContent}}</span></div>
                    </el-card>
                </a>
                </el-col>
            </el-row>
        </el-dialog>
      </el-dialog>
   </div>
</template>

<script setup name="PushRule">

import { getByPushType } from "@/api/push/templateConfig"
import { listRule, getRule, addRule, updateRule, delRule, getPointTree } from "@/api/push/rule"
import { getUserList} from '@/api/system/user'

const { proxy } = getCurrentInstance()
const { sys_yes_no, push_type, push_channel, rule_result } = proxy.useDict("sys_yes_no", "push_type", "push_channel", "rule_result")

const dataList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")
const dateRange = ref([])


const mailAlarmTemplate = ref(false)

const userList = ref([])
const templateConfigList = ref([])


const pointTree = ref(null);

//点位树
const pointOptions = ref([])
//选中的节点
const selectedKeys = ref([])
const pointDefaultProps= ref({
    children: "children",
    label: "name"
})

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    ruleName: undefined,
    pushType: undefined
  },
  rules: {
    ruleName: [{ required: true, message: "规则名称不能为空", trigger: "blur" }],
    pushChannelList: [{ required: true, message: "推送渠道不能为空", trigger: "blur" }],   
    pushType: [{ required: true, message: "推送类型不能为空", trigger: "blur" }],
    alarmLevelList: [{ required: true, message: "报警等级不能为空", trigger: "blur" }],
    userIdsList: [{ required: true, message: "推送人员不能为空", trigger: "blur" }],
    pointIdsList: [{ required: true, message: "推送点位不能为空", trigger: "blur" }],
    templateConfigId: [{ required: true, message: "推送模版不能为空", trigger: "blur" }]
  }
})

const { queryParams, form, rules } = toRefs(data)


function listAllUser() {
    getUserList().then(res => {
        userList.value = res.data;
        //不知道为什么返回的userId是字符串类型，导致el-transfer穿梭框由于类型原因反选不上
        userList.value.forEach(element => {
            element['userId'] = parseInt(element['userId'])
        });
    })
}

function listPointTree() {
    getPointTree().then(res => {
        pointOptions.value = res.data
    })
}

//根据选择的推送类型查询配置参数
function pushTypeChange(){
    form.value.templateConfigId = null;
    templateConfigList.value = [];
    getTemplateByPushType();
}

function getTemplateByPushType() {
    if(form.value.pushType){
        getByPushType(form.value.pushType).then(response => {
            templateConfigList.value = response.data;
        });
    }else{
        templateConfigList.value = [];
    }
}


//树节点选中事件
function  handleCheckChange(data, checked, node) {
    const checkedNodes = pointTree.value.getCheckedKeys();
    selectedKeys.value = checkedNodes;
    form.value.pointIdsList = checkedNodes;
}

/** 查询规则列表 */
function getList() {
  loading.value = true
  listRule(proxy.addDateRange(queryParams.value, dateRange.value)).then(response => {
    dataList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

/** 取消按钮 */
function cancel() {
  open.value = false
  reset()
}

/** 表单重置 */
function reset() {
  form.value = {
    id: undefined,
    ruleName: undefined,
    pushChannelList: [],
    pushType: undefined,
    alarmLevelList: [],
    userIdsList: [],
    pointIdsList: [],
    templateConfigId: undefined,
    status: '0',
    remark: undefined
  }
  selectedKeys.value = [];
  proxy.resetForm("ruleRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = []
  proxy.resetForm("queryRef")
  handleQuery()
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加规则"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const id = row.id || ids.value
  getRule(id).then(response => {

    form.value = response.data
    open.value = true
    title.value = "修改规则"

    // getPointTree().then(res => {
    //     pointOptions.value = res.data;
    //     pointTree.value.setCheckedKeys(form.value.pointIdsList)
    // })


    selectedKeys.value = form.value.pointIdsList;

    getTemplateByPushType();

  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["ruleRef"].validate(valid => {
    if (valid) {
      if(form.value.templateConfigId == null || form.value.templateConfigId == '' || form.value.templateConfigId == undefined){
        proxy.$modal.msgError("请选择模版")
        return false;
      }
      if (form.value.id != undefined) {
        updateRule(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        addRule(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const ids = row.id || ids.value
  proxy.$modal.confirm('是否确认删除规则配置数据项？').then(function () {
    return delRule(ids)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

listPointTree()
listAllUser();

getList()
</script>
