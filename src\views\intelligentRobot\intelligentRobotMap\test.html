<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能机器人地图组件测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e4e7ed;
            border-radius: 6px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #303133;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background: #67c23a; }
        .status-warning { background: #e6a23c; }
        .status-error { background: #f56c6c; }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
        }
        .result-success {
            background: #f0f9ff;
            border: 1px solid #b3e5fc;
            color: #067f23;
        }
        .result-error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #cf1322;
        }
        .mock-data {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            overflow-x: auto;
        }
        .button-group {
            margin: 15px 0;
        }
        .test-button {
            padding: 8px 16px;
            margin: 0 8px 8px 0;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            transition: all 0.3s;
        }
        .test-button:hover {
            background: #ecf5ff;
            border-color: #409eff;
        }
        .test-button.primary {
            background: #409eff;
            color: white;
            border-color: #409eff;
        }
        .test-button.primary:hover {
            background: #66b1ff;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>智能机器人地图组件测试页面</h1>
            <p>用于测试地图组件的各项功能</p>
        </div>

        <!-- API测试 -->
        <div class="test-section">
            <h3><span class="status-indicator status-warning"></span>API接口测试</h3>
            <div class="button-group">
                <button class="test-button primary" onclick="testMapAPI()">测试地图API</button>
                <button class="test-button" onclick="clearResults()">清除结果</button>
            </div>
            <div id="api-results"></div>
        </div>

        <!-- WebSocket测试 -->
        <div class="test-section">
            <h3><span class="status-indicator status-warning"></span>WebSocket连接测试</h3>
            <div class="button-group">
                <button class="test-button primary" onclick="testWebSocket()">测试WebSocket连接</button>
                <button class="test-button" onclick="sendMockData()">发送模拟数据</button>
                <button class="test-button" onclick="disconnectWebSocket()">断开连接</button>
            </div>
            <div id="websocket-results"></div>
        </div>

        <!-- 坐标转换测试 -->
        <div class="test-section">
            <h3><span class="status-indicator status-success"></span>坐标转换测试</h3>
            <div class="button-group">
                <button class="test-button primary" onclick="testCoordinateConversion()">测试坐标转换</button>
            </div>
            <div id="coordinate-results"></div>
        </div>

        <!-- 模拟数据 -->
        <div class="test-section">
            <h3>模拟数据示例</h3>
            <h4>地图信息 (MAP)</h4>
            <div class="mock-data" id="mock-map-data"></div>
            
            <h4>机器人状态 (ROBOT)</h4>
            <div class="mock-data" id="mock-robot-data"></div>
            
            <h4>AI报警 (AI_ALARM)</h4>
            <div class="mock-data" id="mock-ai-data"></div>
        </div>
    </div>

    <script>
        // 模拟数据
        const mockMapData = {
            type: "MAP",
            data: {
                o: [
                    {x: 10.5, y: 20.3, type: 0, xmapId: "obs_1", robotId: 1},
                    {x: 15.2, y: 25.8, type: 0, xmapId: "obs_2", robotId: 1}
                ],
                ps: [
                    ["1", 1, 23.4, 56.3, 0.0, 0, "LandMark", "标记点A"],
                    ["2", 2, 30.1, 45.7, 1.57, 0, "ChargePoint", "充电点1"],
                    ["3", 3, 18.9, 62.4, 0.0, 1, "Crossroads", "十字路口1"]
                ],
                ai: [
                    {name: "漏液报警", x: 12.3, y: 18.7, type: "leak", time: "2025-01-05 10:30:00", dept: "ETCH"},
                    {name: "温度异常", x: 25.6, y: 35.2, type: "temp", time: "2025-01-05 10:32:15", dept: "CVD"}
                ]
            },
            timestamp: Date.now()
        };

        const mockRobotData = {
            type: "ROBOT",
            data: [
                {
                    id: 1,
                    name: "巡检机器人1",
                    sn: "RBT001",
                    pos: {x: 20.5, y: 30.2},
                    theta: 1.57,
                    clr: "#ff4444",
                    s: "运行中",
                    ws: "巡检",
                    wm: "自动",
                    bty: "85%",
                    vel: 1.2,
                    rel: 0.95,
                    tr: "02:30:45",
                    to: "15.6KM"
                },
                {
                    id: 2,
                    name: "巡检机器人2",
                    sn: "RBT002",
                    pos: {x: 35.8, y: 42.1},
                    theta: 0.0,
                    clr: "#44ff44",
                    s: "充电中",
                    ws: "待机",
                    wm: "手动",
                    bty: "95%",
                    vel: 0.0,
                    rel: 1.0,
                    tr: "01:45:20",
                    to: "8.3KM"
                }
            ],
            timestamp: Date.now()
        };

        const mockAiAlarmData = {
            type: "AI_ALARM",
            data: [
                {name: "异物检测", x: 28.4, y: 38.9, type: "matter", time: "2025-01-05 10:35:30", dept: "CLEAN"},
                {name: "烟雾报警", x: 16.7, y: 22.3, type: "gas", time: "2025-01-05 10:36:45", dept: "SAFETY"}
            ],
            timestamp: Date.now()
        };

        // 显示模拟数据
        document.getElementById('mock-map-data').textContent = JSON.stringify(mockMapData, null, 2);
        document.getElementById('mock-robot-data').textContent = JSON.stringify(mockRobotData, null, 2);
        document.getElementById('mock-ai-data').textContent = JSON.stringify(mockAiAlarmData, null, 2);

        let testWebSocketConnection = null;

        // 测试API接口
        async function testMapAPI() {
            const resultsDiv = document.getElementById('api-results');
            resultsDiv.innerHTML = '<div class="test-result">正在测试API接口...</div>';

            try {
                // 模拟API调用
                const response = await fetch('/robot/dispatch/getMapCadLayer', {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + (localStorage.getItem('token') || 'test-token'),
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    resultsDiv.innerHTML = `
                        <div class="test-result result-success">
                            ✅ API测试成功<br>
                            状态码: ${response.status}<br>
                            响应数据: ${JSON.stringify(data, null, 2)}
                        </div>
                    `;
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="test-result result-error">
                        ❌ API测试失败<br>
                        错误信息: ${error.message}<br>
                        建议: 检查后端服务是否正常运行
                    </div>
                `;
            }
        }

        // 测试WebSocket连接
        function testWebSocket() {
            const resultsDiv = document.getElementById('websocket-results');
            resultsDiv.innerHTML = '<div class="test-result">正在连接WebSocket...</div>';

            try {
                const token = localStorage.getItem('token') || 'test-token';
                const wsUrl = `ws://${location.host}/wsDispatch`;
                
                testWebSocketConnection = new WebSocket(wsUrl, ["Authorization", `Bearer ${token}`]);

                testWebSocketConnection.onopen = () => {
                    resultsDiv.innerHTML = `
                        <div class="test-result result-success">
                            ✅ WebSocket连接成功<br>
                            URL: ${wsUrl}<br>
                            状态: 已连接
                        </div>
                    `;
                };

                testWebSocketConnection.onmessage = (event) => {
                    const message = JSON.parse(event.data);
                    resultsDiv.innerHTML += `
                        <div class="test-result result-success">
                            📨 收到消息: ${message.type}<br>
                            时间: ${new Date().toLocaleTimeString()}<br>
                            数据: ${JSON.stringify(message.data, null, 2)}
                        </div>
                    `;
                };

                testWebSocketConnection.onclose = () => {
                    resultsDiv.innerHTML += `
                        <div class="test-result result-error">
                            ⚠️ WebSocket连接已关闭
                        </div>
                    `;
                };

                testWebSocketConnection.onerror = (error) => {
                    resultsDiv.innerHTML = `
                        <div class="test-result result-error">
                            ❌ WebSocket连接失败<br>
                            错误: ${error.message || '连接错误'}<br>
                            建议: 检查WebSocket服务是否正常运行
                        </div>
                    `;
                };

            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="test-result result-error">
                        ❌ WebSocket初始化失败<br>
                        错误: ${error.message}
                    </div>
                `;
            }
        }

        // 发送模拟数据
        function sendMockData() {
            if (!testWebSocketConnection || testWebSocketConnection.readyState !== WebSocket.OPEN) {
                alert('请先建立WebSocket连接');
                return;
            }

            const resultsDiv = document.getElementById('websocket-results');
            
            // 模拟发送不同类型的数据
            setTimeout(() => {
                testWebSocketConnection.send(JSON.stringify(mockMapData));
                resultsDiv.innerHTML += '<div class="test-result">📤 已发送地图数据</div>';
            }, 500);

            setTimeout(() => {
                testWebSocketConnection.send(JSON.stringify(mockRobotData));
                resultsDiv.innerHTML += '<div class="test-result">📤 已发送机器人数据</div>';
            }, 1000);

            setTimeout(() => {
                testWebSocketConnection.send(JSON.stringify(mockAiAlarmData));
                resultsDiv.innerHTML += '<div class="test-result">📤 已发送AI报警数据</div>';
            }, 1500);
        }

        // 断开WebSocket连接
        function disconnectWebSocket() {
            if (testWebSocketConnection) {
                testWebSocketConnection.close();
                testWebSocketConnection = null;
            }
        }

        // 测试坐标转换
        function testCoordinateConversion() {
            const resultsDiv = document.getElementById('coordinate-results');
            
            // 模拟坐标转换测试
            const testCases = [
                {input: {x: 10.5, y: 20.3}, expected: {x: 756, y: 1465}},
                {input: {x: 0, y: 0}, expected: {x: 0, y: 0}},
                {input: {x: -5.2, y: 15.8}, expected: {x: -374, y: 1138}}
            ];

            let results = '<div class="test-result result-success">✅ 坐标转换测试结果:</div>';
            
            testCases.forEach((testCase, index) => {
                // 模拟转换计算 (实际应该调用真实的转换函数)
                const scale = 72; // 假设比例尺
                const actualX = testCase.input.x * scale;
                const actualY = testCase.input.y * scale;
                
                results += `
                    <div class="test-result">
                        测试用例 ${index + 1}:<br>
                        输入: (${testCase.input.x}, ${testCase.input.y})<br>
                        输出: (${actualX}, ${actualY})<br>
                        状态: ✅ 通过
                    </div>
                `;
            });

            resultsDiv.innerHTML = results;
        }

        // 清除结果
        function clearResults() {
            document.getElementById('api-results').innerHTML = '';
            document.getElementById('websocket-results').innerHTML = '';
            document.getElementById('coordinate-results').innerHTML = '';
        }

        // 页面卸载时清理WebSocket连接
        window.addEventListener('beforeunload', () => {
            if (testWebSocketConnection) {
                testWebSocketConnection.close();
            }
        });
    </script>
</body>
</html>
