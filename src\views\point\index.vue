<template>
	<div class="app-container">

		<el-form class="query-form" :model="queryParams" :inline="true" label-width="68px">
			<el-form-item label="点位名称">
				<el-input v-model="queryParams.instanceName" placeholder="请输入点位名称"></el-input>
			</el-form-item>
			<el-form-item label="部门名称">
				<el-input v-model="queryParams.deptName" placeholder="请输入部门名称"></el-input>
			</el-form-item>
			<el-form-item label="Toolgroup">
				<el-input v-model="queryParams.toolGroupName" placeholder="请输入Toolgroup"></el-input>
			</el-form-item>
			<el-form-item label="点位类型">
				<el-select v-model="queryParams.className" placeholder="请选择点位类型">
					<el-option v-for="item in point_class_name" :value="item.value" :label="item.label"></el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="点位模型">
				<el-select v-model="queryParams.idenModelId" placeholder="请选择点位模型">
					<el-option v-for="item in idenModelList" :label="item.idenModelName" :value="item.id"></el-option>
				</el-select>
			</el-form-item>
			<el-form-item>
				<el-button icon="Search" type="primary" @click="handleQuery">搜索</el-button>
				<el-button icon="Refresh" @click="resetQuery">重置</el-button>
			</el-form-item>
		</el-form>

		<el-row :gutter="10" class="mb8">
			<el-col :span="1.5">
				<router-link to="/point/add">
					<el-button icon="Plus" type="primary" plain @click="">添加</el-button>
				</router-link>
				
			</el-col>
			<el-col :span="1.5">
				<el-button icon="Upload" type="info" plain @click="handleImport">导入</el-button>
			</el-col>
		</el-row>

		<el-table v-loading="loading" :data="dataList">
			<el-table-column label="点位名称" align="center" prop="instanceName" />
			<el-table-column label="所属部门" align="center" prop="deptName" />
			<el-table-column label="Toolgroup" align="center" prop="toolGroupName" />
			<el-table-column label="Toolid" align="center" prop="toolId" />
			<el-table-column label="点位类型" align="center" prop="className" >
				<template #default="scope">
					<dict-tag :options="point_class_name" :value="scope.row.className" />
				</template>
			</el-table-column>
			<el-table-column label="点位模型" align="center" prop="idenModelName" />
			<el-table-column label="编辑状态" align="center">
				<template #default="scope">
					<dict-tag :options="edit_status" :value="scope.row.editStatus" />
				</template>
			</el-table-column>
			<el-table-column label="操作" align="center" width="150" class-name="small-padding fixed-width">
				<template #default="scope">
					<el-button link type="primary" @click="handleUpdate(scope.row)">编辑</el-button>
					<el-button link type="primary" @click="handleDelete(scope.row)">删除</el-button>
					
					<el-popover placement="right" :width="500" trigger="click">
						<template #reference>
							<el-button link type="primary">参数</el-button>
						</template>
						<div v-for="(item, index) in scope.row.bizPointAiParamList">
							<el-input v-model="item.bizPointId" style="width: 120px;" v-show="false"></el-input>
							<span>key：</span>&nbsp;
							<el-input v-model="item.paramKey" style="width: 120px;"></el-input>&nbsp;
							<span>value：</span>&nbsp;
							<el-input v-model="item.paramValue" style="width: 120px;"></el-input>&nbsp;
							<el-button link type="primary" v-if="index != 0" @click="paramListRemove(scope.row, index)">删除</el-button>
							<el-button link type="primary" v-if="index == 0" @click="paramListAdd(scope.row)">添加</el-button>
							<el-button link type="primary" v-if="index == 0" @click="paramListSave(scope.row)">确认</el-button>
						</div>
						
					</el-popover>
				</template>
			</el-table-column>
		</el-table>
		<pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />

		<el-dialog :title="upload.title" v-model="upload.open" width="400px" append-to-body>
			<el-upload ref="uploadRef" :limit="1" accept=".xlsx, .xls" :headers="upload.headers" :action="upload.url + '?updateSupport=' + upload.updateSupport" :disabled="upload.isUploading" :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
				<el-icon class="el-icon--upload"><upload-filled /></el-icon>
				<div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
				<template #tip>
					<div class="el-upload__tip text-center">
						<!-- <div class="el-upload__tip">
							<el-checkbox v-model="upload.updateSupport" />是否更新已经存在的点位数据
						</div> -->
						<span>仅允许导入xls、xlsx格式文件。</span>
						<el-link type="primary" :underline="false" style="font-size: 12px; vertical-align: baseline" @click="importTemplate">下载模板</el-link>
					</div>
				</template>
			</el-upload>
			<template #footer>
				<div class="dialog-footer">
					<el-button type="primary" @click="submitFileForm">确 定</el-button>
					<el-button @click="upload.open = false">取 消</el-button>
				</div>
			</template>
		</el-dialog>
	</div>
</template>

<script>
	import { getToken } from "@/utils/auth";
	import point from '@/api/point/point';
	import {
		getAll
	} from '@/api/operation/idenModel';

	export default {
		data() {
			return {
				idenModelList: [],
				proxy: getCurrentInstance().proxy,
				point_class_name: getCurrentInstance().proxy.useDict("point_class_name").point_class_name,
				edit_status: getCurrentInstance().proxy.useDict("edit_status").edit_status,
				dataList: [],
				loading: false,
				total: 0,
				upload: {
					// 是否显示弹出层
					open: false,
					// 弹出层标题
					title: "点位导入",
					// 是否禁用上传
					isUploading: false,
					// 是否更新已经存在的数据
					updateSupport: 0,
					// 设置上传的请求头部
					headers: {
						Authorization: "Bearer " + getToken()
					},
					// 上传的地址
					url: import.meta.env.VITE_APP_BASE_API + "/point/importData"
				},
				form: {},
				queryParams: {
					pageNum: 1,
					pageSize: 10
				},
				rules: {}
			}
		},
		mounted() {
			this.getList()
			this.loadIdenModel()
		},
		methods: {
			loadIdenModel() {
				getAll().then(res => {
					this.idenModelList = res.data
				})
			},
			paramListSave(row) {
				point.paramListSave(row.bizPointAiParamList).then(res=>{
					this.getList()
					this.$modal.msgSuccess('保存成功')
				})
			},
			paramListRemove(row, index) {
				row.bizPointAiParamList.splice(index, 1)
			},
			paramListAdd(row) {	
				row.bizPointAiParamList.push({ paramKey: '', paramValue: '', bizPointId: row.id})
			},
			getList() {
				point.getList(this.queryParams).then(res=>{					
					this.dataList = res.rows
					this.total = res.total
					this.loading = false
				})
			},
			resetQuery() {
				this.queryParams = {
					pageNum: 1,
					pageSize: 10
				}
				this.handleQuery()
			},
			handleQuery() {
				this.getList()
			},
			handleUpdate(row) {
				console.log(row)
				this.$router.push({ path: '/point/add', query: { id: row.id }})
			},
			handleDelete(row) {
				let ids = this.ids || row.id
				this.$modal.confirm('是否确认删除编号为 ' + ids + ' 的点位？').then(function () {
				  return point.remove(ids);
				}).then(() => {
				  this.getList()
				  this.$modal.msgSuccess('删除成功')
				}).catch(() => {})
			},
			importTemplate() {
			  this.download("point/importTemplate", {
			  }, `点位导入模板_${new Date().getTime()}.xlsx`)
			},
			handleImport() {
				this.upload.open = true
			},
			handleFileUploadProgress(event, file, fileList) {
				this.upload.isUploading = true
			},
			handleFileSuccess(response, file, fileList) {
				this.upload.open = false
				this.upload.isUploading = false
				this.$refs["uploadRef"].handleRemove(file)
				this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
					response.msg + "</div>", "导入结果", {
						dangerouslyUseHTMLString: true
					})
				this.getList()
			},
			submitFileForm() {
				this.$refs["uploadRef"].submit()
			}
		}
	}
</script>

<style>

</style>