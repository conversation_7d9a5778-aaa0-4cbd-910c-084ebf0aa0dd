/**
 * 地图坐标计算工具类
 * 用于机器人坐标与地图坐标系之间的转换
 */

/**
 * 将机器人坐标，依据当前xmap的坐标系及scale、offset转换成为基于左上角原点的pt坐标
 * 1.先将此坐标转换为左上角(0,0)坐标系的新坐标(nrx,nry)
 * 2.再将新坐标转换为当前地图pt单位的坐标(ptx,pty)
 * 3.最后在平移
 *
 * @param {number} x - 机器人给出的X坐标值
 * @param {number} minX - 笛卡尔坐标系左下角坐标值，不一定是0
 * @param {number} scale - 地图缩放比
 * @param {number} translationX - X轴平移值
 * @returns {number} 新X坐标
 */
export function calcNewCoordinateXToPt(x, minX, scale, translationX) {
  return calcNewCoordinateX(x, minX * -1, scale) + translationX
}

/**
 * 将机器人坐标，依据当前xmap的坐标系及scale、offset转换成为基于左上角原点的pt坐标
 * 1.先将此坐标转换为左上角(0,0)坐标系的新坐标(nrx,nry)
 * 2.再将新坐标转换为当前地图pt单位的坐标(ptx,pty)
 * 3.最后在平移
 *
 * @param {number} y - 机器人给出的Y坐标值
 * @param {number} maxY - 笛卡尔坐标系右上角Y坐标值
 * @param {number} scale - 地图缩放比
 * @param {number} translationY - Y轴平移值
 * @returns {number} 新Y坐标
 */
export function calcNewCoordinateYToPt(y, maxY, scale, translationY) {
  return calcNewCoordinateY(y, maxY * -1, scale) + translationY
}

/**
 * 机器人给出一个坐标(rx)
 * 1.先将此坐标转换为左上角(0,0)坐标系的新坐标(nrx)
 * 2.再将新坐标转换为当前地图pt单位的坐标(ptx)
 *
 * @param {number} x - 机器人给出的坐标值
 * @param {number} offsetX - 坐标偏移值（基于左上角0,0）
 * @param {number} scale - 地图缩放比
 * @returns {number} 新坐标
 */
function calcNewCoordinateX(x, offsetX, scale) {
  // 1.先将此坐标转换为左上角(0,0)坐标系的新坐标(nrx,nry)
  const nrx = getNewX(x, offsetX)
  // 2.再将新坐标转换为当前地图pt单位的坐标(ptx,pty)
  return calcPtByRealMeter(nrx, scale)
}

/**
 * 机器人给出一个坐标(ry)
 * 1.先将此坐标转换为左上角(0,0)坐标系的新坐标(nry)
 * 2.再将新坐标转换为当前地图pt单位的坐标(pty)
 *
 * @param {number} y - 机器人给出的坐标值
 * @param {number} offsetY - 坐标偏移值（基于左上角0,0）
 * @param {number} scale - 地图缩放比
 * @returns {number} 新坐标
 */
function calcNewCoordinateY(y, offsetY, scale) {
  // 1.先将此坐标转换为左上角(0,0)坐标系的新坐标(nrx,nry)
  const nry = getNewY(y, offsetY)
  // 2.再将新坐标转换为当前地图pt单位的坐标(ptx,pty)
  return calcPtByRealMeter(nry, scale)
}

/**
 * 计算X轴偏移后的值
 *
 * @param {number} x - 原始X坐标
 * @param {number} offsetX - X轴偏移量
 * @returns {number} 偏移后的X坐标
 */
export function getNewX(x, offsetX) {
  return x + offsetX
}

/**
 * 计算Y轴偏移后的值
 *
 * @param {number} y - 原始Y坐标
 * @param {number} offsetY - Y轴偏移量
 * @returns {number} 偏移后的Y坐标
 */
function getNewY(y, offsetY) {
  return -1 * (y + offsetY)
}

/**
 * 根据物理真实尺寸和比例尺，计算出pt单位的值
 *
 * @param {number} realMeter - 物理真实尺寸（米）
 * @param {number} scale - 地图的比例尺
 * @returns {number} pt单位的值
 */
export function calcPtByRealMeter(realMeter, scale) {
  return realMeter * scale * 72000 / 25.4
}

/**
 * 地图坐标计算工具类的默认导出
 * 包含所有公共方法
 */
export default {
  calcNewCoordinateXToPt,
  calcNewCoordinateYToPt,
  getNewX,
  calcPtByRealMeter
}