import request from '@/utils/request'

// 点位列表
export function getBizPointList(data) {
    return request({
        url: '/dataCenter/dataCenter/getBizPointList',
        params: data,
        method: 'get'
    })
}
export function getTaskInstanceNode(data) {
    return request({
        url: '/dataCenter/dataCenter/getTaskInstanceNode',
        params: data,
        method: 'get'
    })
}

export function approvalInstanceNode(data) {
    return request({
        url: '/dataCenter/dataCenter/approvalInstanceNode',
        method: 'post',
        data: data
    })
}

export function getIdenModelParam(data) {
    return request({
        url: '/dataCenter/dataCenter/getIdenModelParam',
        params: data,
        method: 'get'
    })
}

export function getHistoryCurve(data) {
    return request({
        url: '/dataCenter/dataCenter/getHistoryCurve',
        params: data,
        method: 'get'
    })
}

export function getHistory(data) {
    return request({
        url: '/dataCenter/dataCenter/getHistory',
        params: data,
        method: 'get'
    })
}

export function exportHistory(data) {
    return request({
        url: '/dataCenter/dataCenter/exportHistory',
        params: data,
        method: 'get'
    })
}


