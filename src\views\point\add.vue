<template>
	<div class="app-container">
		<el-form ref="formRef" :model="form" :rules="rules" label-width="120px">

			<el-row :gutter="15">

				<el-col :span="5" style="border: 1px solid #d1d1d1; padding: 10px;">
					<el-tag type="primary" size="large" style="margin-top: 0;">机器人控制</el-tag><br />
					<el-select filterable placeholder="请选择机器人" v-model="robotSelection" @change="robotSelectionChange">
						<el-option v-for="item in robotList" :label="item.robotName" :key="item.id"
							:value="item.id"></el-option>
					</el-select>
					<el-button @click="clickStartRealPlay" style="display: none;">开始预览</el-button> <br />
					<el-tag type="primary" size="large">车体控制</el-tag><br />
					<el-button>前</el-button><br />
					<el-button>后</el-button><br />

					<el-tag type="primary" size="large">伸缩杆</el-tag><br />
					<el-button>上</el-button><br />
					<el-button>下</el-button><br />

					<el-tag type="primary" size="large">可见光摄像头</el-tag><br />
					<div class="cloud_terrace">
						<el-row>
							<el-col :span="16" class="direction">
								<a @mousedown="mouseDownPTZControl(5)" @mouseup="mouseUpPTZControl()"><img
										src="@/assets/icons/cloud_terrace_left_top.png" /></a>
								<a @mousedown="mouseDownPTZControl(1)" @mouseup="mouseUpPTZControl()"><img
										src="@/assets/icons/cloud_terrace_top.png" /></a>
								<a @mousedown="mouseDownPTZControl(7)" @mouseup="mouseUpPTZControl()"><img
										src="@/assets/icons/cloud_terrace_right_top.png" /></a><br />
								<a @mousedown="mouseDownPTZControl(3)" @mouseup="mouseUpPTZControl()"><img
										src="@/assets/icons/cloud_terrace_left.png" /></a>

								<el-popconfirm @confirm="cameraReset" class="box-item" title="是否初始化云台" placement="top"
									confirm-button-text="确认" cancel-button-text="取消">
									<template #reference>
										<a><img src="@/assets/icons/cloud_terrace_auto.png" /></a>
									</template>
								</el-popconfirm>

								<a @mousedown="mouseDownPTZControl(4)" @mouseup="mouseUpPTZControl()"><img
										src="@/assets/icons/cloud_terrace_right.png" /></a><br />
								<a @mousedown="mouseDownPTZControl(6)" @mouseup="mouseUpPTZControl()"><img
										src="@/assets/icons/cloud_terrace_left_down.png" /></a>
								<a @mousedown="mouseDownPTZControl(2)" @mouseup="mouseUpPTZControl()"><img
										src="@/assets/icons/cloud_terrace_down.png" /></a>
								<a @mousedown="mouseDownPTZControl(8)" @mouseup="mouseUpPTZControl()"><img
										src="@/assets/icons/cloud_terrace_right_down.png" /></a>
							</el-col>
							<el-col :span="6" class="function">
								<el-tooltip class="box-item" effect="dark" content="变倍+" placement="top">
									<a @mousedown="PTZZoomIn()" @mouseup="PTZZoomStop()"><img
											src="@/assets/icons/cloud_terrace_zoom1.png" /></a>
								</el-tooltip>&nbsp;
								<el-tooltip class="box-item" effect="dark" content="变倍-" placement="top">
									<a @mousedown="PTZZoomout()" @mouseup="PTZZoomStop()"><img
											src="@/assets/icons/cloud_terrace_zoom2.png" /></a>
								</el-tooltip>
								<el-tooltip class="box-item" effect="dark" content="变焦+" placement="top">
									<a @mousedown="PTZFocusIn()" @mouseup="PTZFoucusStop()"><img
											src="@/assets/icons/cloud_terrace_focal1.png" /></a>
								</el-tooltip>&nbsp;
								<el-tooltip class="box-item" effect="dark" content="变焦-" placement="top">
									<a @mousedown="PTZFoucusOut()" @mouseup="PTZFoucusStop()"><img
											src="@/assets/icons/cloud_terrace_focal2.png" /></a>
								</el-tooltip>
								<el-tooltip class="box-item" effect="dark" content="光圈+" placement="top">
									<a @mousedown="PTZIrisIn()" @mouseup="PTZIrisStop()"><img
											src="@/assets/icons/cloud_terrace_aperture1.png" /></a>
								</el-tooltip>&nbsp;
								<el-tooltip class="box-item" effect="dark" content="光圈-" placement="top">
									<a @mousedown="PTZIrisOut()" @mouseup="PTZIrisStop()"><img
											src="@/assets/icons/cloud_terrace_aperture2.png" /></a>
								</el-tooltip>

							</el-col>
						</el-row>
					</div>
				</el-col>

				<el-col :span="19">
					<div style="border: 1px solid #d1d1d1; padding: 10px;">
						<el-row :gutter="15">
							<el-col style="margin-bottom: 10px;">
								<div id="divPlugin" style="height: 280px;"></div>
							</el-col>
							<el-col :span="12">
								<el-form-item label="设备树">
									<el-tree-select v-model="form.treeId" :data="treeList"
										:props="{ value: 'id', label: 'deviceName', children: 'children' }"
										value-key="id" placeholder="请选择设备树" check-strictly />
								</el-form-item>
							</el-col>
							<el-col :span="12">
								<el-form-item label="所属部门" prop="deptId">
									<el-input v-model="form.deptName" placeholder="请选择设备树" disabled></el-input>
									<el-input v-model="form.deptId" placeholder="请选择设备树" v-show="false"></el-input>
								</el-form-item>
							</el-col>
							<el-col :span="12">
								<el-form-item label="Toolgroup">
									<el-input v-model="form.toolGroupName" type="text" placeholder="请选择设备树"
										disabled></el-input>
									<el-input v-model="form.toolGroupId" type="text" placeholder="请选择设备树"
										v-show="false"></el-input>
								</el-form-item>
							</el-col>
							<el-col :span="12">
								<el-form-item label="Toolid">
									<el-input v-model="form.toolId" type="text" placeholder="请输入Toolid"></el-input>
								</el-form-item>
							</el-col>
							<el-col :span="12">
								<el-form-item label="点位名称" prop="instanceName">
									<el-input v-model="form.instanceName" placeholder="请输入点位名称"></el-input>
								</el-form-item>
							</el-col>
							<el-col :span="12">
								<el-form-item label="点位类型" prop="className">
									<el-select v-model="form.className" placeholder="请选择点位类型" @change="classNameChange">
										<el-option v-for="item in point_class_name" :label="item.label"
											:value="item.value"></el-option>
									</el-select>
								</el-form-item>
							</el-col>
							<el-col :span="12">
								<el-form-item label="当前点位ID" prop="xmapId">
									<el-input v-model="form.xmapId" placeholder="请输入当前点位ID"></el-input>
								</el-form-item>
							</el-col>
							<el-col :span="12">
								<el-form-item label="云台角度上下">
									<el-input v-model="form.ptzAngleTb" placeholder="停止后自动获取" disabled></el-input>
								</el-form-item>
							</el-col>
							<el-col :span="12">
								<el-form-item label="云台角度左右">
									<el-input v-model="form.ptzAngleLr" placeholder="停止后自动获取" disabled></el-input>
								</el-form-item>
							</el-col>
							<el-col :span="12">
								<el-form-item label="摄像头聚焦">
									<el-input v-model="form.cameraFocus" placeholder="停止后自动获取" disabled></el-input>
								</el-form-item>
							</el-col>
							<el-col :span="12">
								<el-form-item label="摄像头变倍">
									<el-input v-model="form.cameraZoom" placeholder="停止后自动获取" disabled></el-input>
								</el-form-item>
							</el-col>
							<el-col :span="12" v-show="idenModelShow">
								<el-form-item label="点位模型">
									<el-select filterable v-model="form.idenModelId" placeholder="请选择设备类型"
										popper-class="custom-dropdown" @change="idenModelIdChange"
										style="width: 180px; margin-right: 10px;">
										<el-option v-for="item in idenModelList" :label="item.idenModelName"
											:value="item.id">
											<span style="float: left">{{ item.idenModelName }}</span>
											<el-image style="float: right; width: 50px; height: 50px"
												:src="apiUrl + item.samplePhotoUrl" />
										</el-option>
									</el-select>

								</el-form-item>
							</el-col>
							<el-col :span="12">
								<el-form-item label="伸缩杆高度">
									<el-input v-model="form.telescopicRodH" placeholder="停止后自动获取" disabled></el-input>
								</el-form-item>
							</el-col>
							<el-col :span="12" v-show="idenRuleShow">
								<el-form-item label="规则状态">
									<el-select filterable v-model="form.idenRuleId" placeholder="请选择规则状态">
										<el-option v-for="item in ruleList" :label="item.idenRuleName"
											:value="item.id"></el-option>
									</el-select>
								</el-form-item>
							</el-col>
							<el-col :span="12">
								<el-form-item label="设备类型">
									<el-select v-model="form.deviceClass" placeholder="请选择设备类型">
										<el-option v-for="item in machine_type" :label="item.label"
											:value="item.value"></el-option>
									</el-select>
								</el-form-item>
							</el-col>

							<el-col>
								<el-form-item>
									<el-button @click="resetParams">重置参数</el-button>
									<el-button @click="recognitionClick()">识别配置</el-button>
									<el-button @click="handelConfirm" style="margin-right: 10px;">保存</el-button>
									<router-link to="/operation/point">
										<el-button @click="goBack">返回</el-button>
									</router-link>
								</el-form-item>
							</el-col>


						</el-row>
					</div>
				</el-col>

			</el-row>
		</el-form>

		<el-dialog v-model="recognitionOpen" width="1000px" append-to-body draggable :close-on-click-modal="false"
			:close-on-press-escape="false" :show-close="false" @closed="showPlugin">
			<el-row :gutter="15" style="height: 660px; overflow-y: auto;">
				<el-col :span="16">
					<image-editor ref="imageEditor" @params="imgSaveParams" @params_="imgSaveParams2"
						:imgUrl="imgUrl"></image-editor>
				</el-col>
				<el-col :span="8">
					<el-col>
						<el-button type="text" @click="imageEditorClick">保存</el-button>
						<el-button type="text" @click="recognitionOpen = false">返回</el-button>
						<el-button type="text" @click="clickCapturePicData" v-show="form.id">重新抓图</el-button>
					</el-col>
					<el-col>
						<el-button type="text" @click="markClick">标记</el-button>
						<el-button type="text" @click="lineClick">水平线标记</el-button>
					</el-col>
					<el-col>
						<el-button type="text" @click="AIRecognition">识别</el-button>
						<el-input rows="19" type="textarea" v-model="AIParams" readonly></el-input>
					</el-col>

				</el-col>
				<el-col>
					<el-table :data="dataList">
						<el-table-column label="参数名称" align="center" prop="paramName" />
						<el-table-column label="识别名称" align="center" prop="idenTypeName" />
						<el-table-column label="索引" align="center" prop="seq" />
						<el-table-column label="单位" align="center" prop="unit" />
						<el-table-column label="偏移量" align="center" prop="offsetValue" />
						<el-table-column label="小数点" align="center" prop="precisionNum" />
						<el-table-column label="缩放位" align="center" prop="scaling" />
						<el-table-column label="量程（最小）" align="center" prop="minLimit" />
						<el-table-column label="量程（最大）" align="center" prop="maxLimit" />
					</el-table>
				</el-col>
			</el-row>

		</el-dialog>
	</div>
</template>

<script>
	import imageEditor from './imageEditor';
	import robot from '@/api/robot/robot';
	import {
		getAll
	} from '@/api/operation/idenModel';
	import {
		getByModelId,
		getRuleAll
	} from '@/api/operation/idenRule';
	import {
		getAllModelParam
	} from "@/api/operation/idenModelParam"
	import point from '@/api/point/point';
	import Cookies from 'js-cookie';

	let g_iWndIndex = 0; //可以不用设置这个变量，有窗口参数的接口中，不用传值，开发包会默认使用当前选择窗口
	let g_oLocalConfig = null; //本地配置	
	let ERROR_CODE_UNKNOWN = 1000; //未知错误
	let ERROR_CODE_NETWORKERROR = 1001; //网络错误
	let ERROR_CODE_PARAMERROR = 1002; //缺少插件元素		
	let ERROR_CODE_LOGIN_NOLOGIN = 2000; // 未登录
	let ERROR_CODE_LOGIN_REPEATLOGIN = 2001; //设备已登录，重复登录
	let ERROR_CODE_LOGIN_NOSUPPORT = 2002; //当前设备不支持Digest登录
	let ERROR_CODE_PLAY_PLUGININITFAIL = 3000; //插件初始化失败
	let ERROR_CODE_PLAY_NOREPEATPLAY = 3001; //当前窗口已经在预览
	let ERROR_CODE_PLAY_PLAYBACKABNORMAL = 3002; //回放异常
	let ERROR_CODE_PLAY_PLAYBACKSTOP = 3003; //回放停止
	let ERROR_CODE_PLAY_NOFREESPACE = 3004; //录像过程中，硬盘容量不足
	let ERROR_CODE_TALK_FAIL = 5000; //语音对讲失败
	let version = "V3.3.0build20230322";
	let g_szRecordType = "";
	let g_bPTZAuto = false;
	let g_iSearchTimes = 0;
	let g_iDownloadID = -1;
	let g_tDownloadProcess = 0;
	let g_tUpgrade = 0;
	let g_bEnableDraw = false;

	export default {
		components: {
			imageEditor
		},
		data() {
			return {
				AIParams: null,
				idenRuleShow: false,
				idenModelShow: false,
				imgUrl: '/static/default.jpg',
				samplePhotoUrl: '',
				apiUrl: import.meta.env.VITE_APP_BASE_API,
				machine_type: getCurrentInstance().proxy.useDict("machine_type").machine_type,
				point_class_name: getCurrentInstance().proxy.useDict("point_class_name").point_class_name,
				robotSelection: null,
				dataList: [],
				recognitionOpen: false,
				title: '点位添加',
				formRef: null,
				form: {
					ptzAngleTb: 0,
					ptzAngleLr: 0,
					cameraFocus: 0,
					cameraZoom: 0
				},
				rules: {
					instanceName: [{
						required: true,
						message: '请输入点位名称',
						trigger: 'blur'
					}],
					deptId: [{
						required: true,
						message: '请输入所属部门',
						trigger: 'blur'
					}],
					xmapId: [{
						required: true,
						message: '请输入当前点位ID',
						trigger: 'blur'
					}],
					className: [{
						required: true,
						message: '请输入点位类型',
						trigger: 'blur'
					}],
				},
				dialogVisible: false,
				robotList: [],
				idenModelList: [],
				ruleList: [],
				treeList: [],
				originalTree: [],
				mouseDownTime: null,
				iPTZIndex: null,
				camera: {
					loginip: null,
					port: null,
					username: null,
					password: null,
					ip: null
				}

			}
		},
		watch: {
			'form.treeId'(newVal) {
				if (!newVal) return;
				this.form.deptName = null
				this.form.toolGroupName = null
				let data = this.originalTree.filter(f => f.id == newVal)[0]
				if (data.nodeType == 0) {
					this.form.deptId = data.id
					this.form.deptName = data.deviceName
				} else {
					this.form.toolGroupId = data.id
					this.form.toolGroupName = data.deviceName
					let parent = this.originalTree.filter(f => f.id == data.parentId)[0]
					this.form.deptId = parent.id
					this.form.deptName = parent.deviceName
				}
			}
		},
		mounted() {
			this.robotInit()
			this.loadRobot()
			this.loadIdenModel()
			this.getDeviceTree()
			this.init()
		},
		methods: {
			goBack() {
				this.stopAllPlay()
				this.destroyPlugin()
			},
			init() {
				if (this.$route.query && this.$route.query.id) {
					point.getOne(this.$route.query.id).then(res => {
						this.form = res.data
						if (this.form.markedPhotoUrl) {
							this.imgUrl = this.form.markedPhotoUrl
						}
						if (this.form.className) {
							this.classNameChange(this.form.className, 1)
						}
						if (this.form.idenModelId) {
							this.idenModelIdChange(this.form.idenModelId, 1)
						}
						console.log(this.form)
					})
				}
			},
			markClick() {
				let idenModel = this.idenModelList.filter(f => f.id == this.form.idenModelId)[0];
				this.$refs.imageEditor.markClick(this.dataList, 0, idenModel)
			},
			lineClick() {
				this.$refs.imageEditor.lineClick()
			},
			imgSaveParams(row) {
				let bizPointIdenConfList = this.coordData(row.coord)
				this.form.markedPhotoUrl = row.markImgUrl
				this.form.onSitePhotoUrl = row.rawImgUrl
				this.form.bizPointIdenConfList = bizPointIdenConfList
				this.form.lastIdenConfInfo = JSON.stringify(row.coord)
				this.recognitionOpen = false
			},
			// 向AI请求识别
			imgSaveParams2(row) {
				if (!this.form.idenModelId) {
					return this.$modal.msgWarning('请先选择点位模型');
				}
				if (this.form.xmapId == null || this.form.xmapId == '') {
					return this.$modal.msgWarning('当前点位ID不能为空')
				}
				let bizPointIdenConfList = this.coordData(row.coord)
				console.log("bizPointIdenConfList：", bizPointIdenConfList)
				let bizPointAiParamList = []
				if(this.form.bizPointAiParamList && this.form.bizPointAiParamList.length > 0) {
					bizPointAiParamList = this.form.bizPointAiParamList
				}
				let photoUrl = row.rawImgUrl
				let query = {
					id: this.form.xmapId,
					idenModelId: this.form.idenModelId,
					photoUrl: photoUrl,
					bizPointIdenConfList: bizPointIdenConfList,
					bizPointAiParamList: bizPointAiParamList
				}
				point.AIRecognition(query).then(res => {
					console.log(res)
					if (res.code && res.code == 200) {
						let data = ''
						res.data.map(item => {
							data += item.iden_name
							let vals = ''
							item.params.map((item2, index) => {
								let key = ''
								if(item.iden_name.includes('行') || item.iden_name.includes('双圈')) {
									key = item2.key
								}
								vals += (key + item2.value + (index + 1 == item.params.length?'':'、'))
							})
							data += (' ' + vals + ';\n')
						})
						this.AIParams = data
					} else {
						this.$modal.msgWarning('识别失败！');
					}
				})
			},
			coordData(row) {
				console.log(row)
				let keys = Object.keys(row);
				let bizPointIdenConfList = []
				let bizPointIdenConf = {}
				let standardList = []

				keys.map(item => {
					let standard = {}
					let graph = row[item]
					if (!graph.text && graph.fill == 'yellow') {
						standard.lineStartX = graph.aCoords.tl.x
						standard.lineStartY = graph.aCoords.tl.y
						standard.lineEndX = graph.aCoords.tr.x
						standard.lineEndY = graph.aCoords.tr.y
						standardList.push(standard)
					}
				})
				keys.map((item, index) => {
					let graph = row[item]
					if (!graph.text && (graph.stroke == 'red' || graph.stroke == '#ff4040')) {
						bizPointIdenConf = {}
						bizPointIdenConf.minPosX = graph.aCoords.tl.x
						bizPointIdenConf.minPosY = graph.aCoords.tl.y
						bizPointIdenConf.maxPosX = graph.aCoords.br.x
						bizPointIdenConf.maxPosY = graph.aCoords.br.y
					} else if (graph.text && graph.text.includes('起点')) {
						bizPointIdenConf.beginScalarX = (graph.aCoords.tl.x + graph.aCoords.tr.x) / 2
						bizPointIdenConf.beginScalarY = (graph.aCoords.tl.y + graph.aCoords.tr.y) / 2
					} else if (graph.text && graph.text.includes('终点')) {
						bizPointIdenConf.endScalarX = (graph.aCoords.tl.x + graph.aCoords.tr.x) / 2
						bizPointIdenConf.endScalarY = (graph.aCoords.tl.y + graph.aCoords.tr.y) / 2

						let jl = null
						let jlIndex = -1
						standardList.map((item2, index) => {
							let j = item2.lineStartX - bizPointIdenConf.minPosX
							if (!jl && j > 0) {
								jl = j
								jlIndex = index
							} else if (jl && j > 0 && j < jl) {
								jl = j
								jlIndex = index
							}
						})
						if (jl) {
							bizPointIdenConf.lineStartX = standardList[jlIndex].lineStartX
							bizPointIdenConf.lineStartY = standardList[jlIndex].lineStartY
							bizPointIdenConf.lineEndX = standardList[jlIndex].lineEndX
							bizPointIdenConf.lineEndY = standardList[jlIndex].lineEndY
						}

					} else if (graph.text) {
						bizPointIdenConf.idenName = graph.text
					}
					if (index + 1 < keys.length) {
						let g = row[keys[index + 1]]
						if (!g.text && (g.stroke == 'red' || g.stroke == '#ff4040') || !g.text && g.fill == 'yellow' || graph.text && graph
							.text.includes('终点')) {
							bizPointIdenConfList.push(bizPointIdenConf);
						}
					} else {
						if (graph.text && (graph.fill == 'red' || graph.fill == '#ff4040') || graph.text && graph.text.includes('终点')) {
							bizPointIdenConfList.push(bizPointIdenConf);
						}
					}


				})
				return bizPointIdenConfList;
			},
			AIRecognition() {
				this.$refs.imageEditor.save(1)
			},
			resetParams() {
				this.form.treeId = null
				this.form.deptName = null
				this.form.deptId = null
				this.form.toolGroupName = null
				this.form.toolGroupId = null
				this.form.toolId = null
				this.form.instanceName = null
				this.form.className = null
				this.form.xmapId = null
				this.form.idenModelId = null
				this.form.idenRuleId = null
				this.form.deviceClass = null
			},
			getDeviceTree() {
				point.getDeviceTree().then(res => {
					this.originalTree = res.data
					let tree = this.handleTree(res.data)
					this.treeList = tree

					if (this.form.toolGroupId) {
						this.form.treeId = this.form.toolGroupId;
					} else if (this.form.deptId) {
						this.form.treeId = this.form.deptId;
					}
				})
			},
			recognitionClick() {
				let oWndInfo = WebVideoCtrl.I_GetWindowStatus(0)
				if (oWndInfo == null && !this.form.markedPhotoUrl) {
					return this.$modal.msgWarning('请先选择机器人连接摄像机')
				}
				if (this.form.idenModelId == null) {
					return this.$modal.msgWarning('请先选择点位模型')
				}

				if (!this.form.markedPhotoUrl) {
					this.capturePic();
				}
				if (this.form.idenModelId) {
					getAllModelParam({
						idenModelId: this.form.idenModelId
					}).then(res => {
						this.dataList = res.data
						this.hidPlugin()
						this.recognitionOpen = true
					})
				} else {
					this.hidPlugin()
					this.recognitionOpen = true
				}
			},
			clickCapturePicData() {
				let that = this
				let oWndInfo = WebVideoCtrl.I_GetWindowStatus(0)
				if (oWndInfo == null) {
					return this.$modal.msgWarning('请先选择机器人连接摄像机')
				}
				that.$modal.confirm('是否确认重新抓图？').then(function() {
					return that.capturePic()
				}).then(() => {

				}).catch(() => {})

			},
			capturePic() {
				let that = this
				var oWndInfo = WebVideoCtrl.I_GetWindowStatus(g_iWndIndex),
					szInfo = "";
				console.log(oWndInfo)
				if (oWndInfo != null) {
					WebVideoCtrl.I_CapturePicData({
						iWndIndex: 0
					}).then(function(res) {
						const data = window.atob(res)
						const ia = new Uint8Array(data.length)
						for (let i = 0; i < data.length; i++) {
							ia[i] = data.charCodeAt(i)
						}
						const blob = new Blob([ia], {
							type: 'image/png'
						})
						let form = new FormData()
						form.append('image', blob)
						point.uploadImg(form).then(res => {
							that.imgUrl = res.msg
							szInfo = "抓图上传成功！";
							console.log(oWndInfo.szDeviceIdentify + " " + szInfo);
						})
					}, function() {
						szInfo = "抓图失败！";
						console.log(oWndInfo.szDeviceIdentify + " " + szInfo);
					});
				}
			},
			arrayBufferToBase64(buffer) {
				let binary = '';
				let bytes = new Uint8Array(buffer);
				let len = bytes.byteLength;
				for (let i = 0; i < len; i++) {
					binary += String.fromCharCode(bytes[i]);
				}
				return window.btoa(binary);
			},
			classNameChange(val, index) {
				if (!index) {
					this.form.idenRuleId = null
					this.form.idenModelId = null
				}
				if (val == 'IdenPoint') {
					this.idenRuleShow = true
					this.idenModelShow = true
				} else if (val == 'InfraredDetection') {
					this.idenModelShow = false
					this.idenRuleShow = true
					getRuleAll().then(res => {
						this.ruleList = res.data.filter(f => f.idenRuleType == '3')
					})
				} else {
					this.idenRuleShow = false
					this.idenModelShow = false
					this.ruleList = []
				}
			},
			idenModelIdChange(val, index) {
				// let idenModel = this.idenModelList.filter(f => f.id == val)[0]
				// this.samplePhotoUrl = idenModel.samplePhotoUrl;

				if (!index) {
					this.form.idenRuleId = null
				}
				getByModelId(val).then(res => {
					this.ruleList = res.data
				})
			},
			loadRobot() {
				robot.getAll().then(res => {
					this.robotList = res.data
				})
			},
			loadIdenModel() {
				getAll().then(res => {
					this.idenModelList = res.data
				})
			},
			handelConfirm() {
				this.$refs.formRef.validate((valid) => {
					if (!valid) return;

					if (this.form.className == 'IdenPoint' && this.form.idenModelId == null) {
						return this.$modal.msgWarning('类型为识别时点位模型不可为空！');
					}
					if ((this.form.className == 'IdenPoint' || this.form.className == 'InfraredDetection') && this
						.form.idenRuleId == null) {
						return this.$modal.msgWarning('类型为识别或红外测温时规则状态不可为空！');
					}
					if (this.form.className == 'IdenPoint' || this.form.className == 'InfraredDetection') {
						if (this.form.toolGroupName == '' || this.form.toolGroupName == null) {
							return this.$modal.msgWarning('类型为识别或红外测温时Toolgroup不可为空！');
						}
					}

					console.log(this.form)
					if (!this.form.id) {
						point.add(this.form).then(res => {
							this.$router.push({
								path: '/operation/point'
							})
						})
					} else {
						point.edit(this.form).then(res => {
							this.$router.push({
								path: '/operation/point'
							})
						})
					}
					this.goBack()
				})
			},
			imageEditorClick() {
				this.$refs.imageEditor.save()
			},
			robotSelectionChange(row) {
				let robot = this.robotList.filter(f => f.id == row)[0]

				let cameraInfo = null
				if (robot.robotHardwareList.length > 0) {
					robot.robotHardwareList.map(item => {
						if (item.hardwareType == '5') {
							cameraInfo = item
						}
					})
				}
				console.log(cameraInfo)
				if (!cameraInfo) {
					return this.$modal.msgWarning('机器人没有配置可见光摄像机');
				}

				this.camera.loginip = cameraInfo.ip;
				this.camera.port = cameraInfo.port;
				this.camera.username = cameraInfo.account;
				this.camera.password = cameraInfo.pwd;

				this.clickLogout()

				setTimeout(() => {
					this.clickLogin(1)
				}, 500)

				setTimeout(() => {
					this.clickStartRealPlay()
				}, 1000)
			},
			clickLogout() {
				let that = this
				var szDeviceIdentify = this.camera.ip;

				if (null == szDeviceIdentify) {
					return;
				}

				WebVideoCtrl.I_Logout(szDeviceIdentify).then(() => {
					that.camera.ip = null
					console.log(szDeviceIdentify + " " + "退出成功！");
				}, () => {
					console.log(szDeviceIdentify + " " + "退出失败！");
				});
			},
			clickLogin(szProtoType) {
				let that = this
				let szIP = this.camera.loginip
				let szPort = this.camera.port
				let szUsername = this.camera.username
				let szPassword = this.camera.password

				if (!szIP || !szPort || !szUsername || !szPassword) {
					return this.$modal.msgWarning('摄像机配置信息不完整');
				}

				var szDeviceIdentify = szIP + "_" + szPort;

				WebVideoCtrl.I_Login(szIP, szProtoType, szPort, szUsername, szPassword, {
					success: function(xmlDoc) {
						console.log(szDeviceIdentify + " 登录成功！");
						that.camera.ip = szDeviceIdentify
					},
					error: function(oError) {
						if (ERROR_CODE_LOGIN_REPEATLOGIN === oError.errorCode) {
							console.log(szDeviceIdentify + " 已登录过！");
							that.camera.ip = szDeviceIdentify
						} else {
							if (oError.errorCode === 401) {
								console.log(szDeviceIdentify + " 登录失败，已自动切换认证方式！");
							} else {
								console.log(szDeviceIdentify + " 登录失败！", oError.errorCode, oError
									.errorMsg);
							}
						}
					},

				});

			},
			clickGoPreset() {
				var oWndInfo = WebVideoCtrl.I_GetWindowStatus(g_iWndIndex)
				// 34初始点位
				let iPresetID = 34;

				if (oWndInfo != null) {
					WebVideoCtrl.I_GoPreset(iPresetID, {
						success: function(xmlDoc) {
							console.log(oWndInfo.szDeviceIdentify + " 调用初始点成功！");
						},
						error: function(oError) {
							console.log(oWndInfo.szDeviceIdentify + " 调用初始点失败！", oError.errorCode, oError
								.errorMsg);
						}
					});
				}
			},
			clickStartRealPlay(iStreamType) {
				let that = this
				var oWndInfo = WebVideoCtrl.I_GetWindowStatus(g_iWndIndex),
					szDeviceIdentify = this.camera.ip;

				if (null == szDeviceIdentify) {
					return this.$modal.msgWarning('摄像机配置信息不正确');
				}

				WebVideoCtrl.I_StopAllPlay().then(() => {
					console.log('关闭全部视频播放成功！')
					for (let i = 0; i < 2; i++) {
						that.startRealPlay(szDeviceIdentify, i, i + 1);
					}
				}).catch(() => {
					console.log('失败！')
				})


			},
			startRealPlay(szDeviceIdentify, iWndIndex, iChannelID) {
				let that = this
				WebVideoCtrl.I_StartRealPlay(szDeviceIdentify, {
					iWndIndex: iWndIndex,
					iChannelID: iChannelID,
					success: function() {
						let szInfo = "开始预览成功！";
						console.log(szDeviceIdentify + " " + szInfo);
						that.clickGoPreset()
					},
					error: function(oError) {
						console.log(szDeviceIdentify + " 开始预览失败！", oError.errorCode, oError
							.errorMsg);
					}
				});
			},
			robotInit() {
				let that = this
				// 初始化插件参数及插入插件
				WebVideoCtrl.I_InitPlugin({
					cbSelWnd: function(xmlDoc) {
						g_iWndIndex = parseInt($(xmlDoc).find("SelectWnd").eq(0).text(), 10);
						var szInfo = "当前选择的窗口编号：" + g_iWndIndex;
						console.log(szInfo);
					},
					cbDoubleClickWnd: function(iWndIndex, bFullScreen) {
						var szInfo = "当前放大的窗口编号：" + iWndIndex;
						if (!bFullScreen) {
							szInfo = "当前还原的窗口编号：" + iWndIndex;
						}
						console.log(szInfo);
					},
					cbInitPluginComplete: function() {
						WebVideoCtrl.I_InsertOBJECTPlugin("divPlugin").then(() => {
							// 检查插件是否最新
							WebVideoCtrl.I_CheckPluginVersion().then((bFlag) => {
								if (bFlag) {
									alert("检测到新的插件版本，请安装确认后下载的HCWebSDKPluginsUserSetup.exe");
								} else {
									WebVideoCtrl.I_ArrangeWindow("1*2").then(() => {
										console.log("窗口分割成功！");
									}, (oError) => {
										var szInfo = "窗口分割失败！";
										console.log(szInfo, oError.errorCode, oError
											.errorMsg);
									});
								}
							});
						}, () => {
							alert("插件初始化失败，请确认是否已安装插件；如果未安装，请安装确认后下载的HCWebSDKPluginsUserSetup.exe");
							that.goOnLink()
						});
					}

				});

			},
			goOnLink() {
				const a = document.createElement('a') // 创建一个<a></a>标签
				a.href = '/HCWebSDKPluginsUserSetup.exe' // 给a标签的href属性值加上地址
				a.download = 'HCWebSDKPluginsUserSetup.exe' // 设置下载文件文件名，这里加上.xlsx指定文件类型，pdf文件就指定.fpd即可
				a.style.display = 'none' // 障眼法藏起来a标签
				document.body.appendChild(a) // 将a标签追加到文档对象中
				a.click() // 模拟点击了a标签，会触发a标签的href的读取，浏览器就会自动下载了
				a.remove() // 一次性的，用完就删除a标签

			},
			destroyPlugin() {
				let that = this
				WebVideoCtrl.I_DestroyPlugin().then(() => {
					console.log('销毁成功！')
				}).catch(() => {
					console.log('销毁失败！')
				})
			},
			stopAllPlay() {
				WebVideoCtrl.I_StopAllPlay().then(() => {
					console.log('关闭全部视频播放成功！')
				}).catch(() => {
					console.log('关闭全部视频播放失败！')
				})
			},
			hidPlugin() {
				WebVideoCtrl.I_HidPlugin().then(() => {
					console.log('隐藏成功！')
				}).catch(() => {
					console.log('隐藏失败！')
				})
			},
			showPlugin() {
				WebVideoCtrl.I_ShowPlugin().then(() => {
					console.log('展示成功！')
				}).catch(() => {
					console.log('展示失败！')
				})
			},
			mouseDownPTZControl(iPTZIndex) {
				this.mouseDownTime = Date.now()
				this.iPTZIndex = iPTZIndex
				var oWndInfo = WebVideoCtrl.I_GetWindowStatus(g_iWndIndex),
					iPTZSpeed = 4

				if (oWndInfo != null) {
					if (9 == iPTZIndex && g_bPTZAuto) {
						iPTZSpeed = 0; // 自动开启后，速度置为0可以关闭自动
					} else {
						g_bPTZAuto = false; // 点击其他方向，自动肯定会被关闭
					}

					WebVideoCtrl.I_PTZControl(iPTZIndex, false, {
						iPTZSpeed: iPTZSpeed,
						success: function(xmlDoc) {
							if (9 == iPTZIndex && g_bPTZAuto) {
								console.log(oWndInfo.szDeviceIdentify + " 停止云台成功！");
							} else {
								console.log(oWndInfo.szDeviceIdentify + " 开启云台成功！");
							}
							if (9 == iPTZIndex) {
								g_bPTZAuto = !g_bPTZAuto;
							}
						},
						error: function(oError) {
							console.log(oWndInfo.szDeviceIdentify + " 开启云台失败！", oError.errorCode, oError
								.errorMsg);
						}
					});
				}
			},
			mouseUpPTZControl() {
				let timeDifference = (Date.now() - this.mouseDownTime) / 1000;
				console.log(timeDifference)
				let tb = Math.round(timeDifference * 17.67)
				let lr = Math.round(timeDifference * 32.54)

				if (timeDifference >= 1) {
					if (this.iPTZIndex == 5) {
						this.form.ptzAngleTb += tb;
						this.form.ptzAngleLr += lr;
					} else if (this.iPTZIndex == 1) {
						this.form.ptzAngleTb += tb;
					} else if (this.iPTZIndex == 7) {
						this.form.ptzAngleTb += tb;
						this.form.ptzAngleLr -= lr;
					} else if (this.iPTZIndex == 3) {
						this.form.ptzAngleLr += lr;
					} else if (this.iPTZIndex == 4) {
						this.form.ptzAngleLr -= lr;
					} else if (this.iPTZIndex == 6) {
						this.form.ptzAngleTb -= tb;
						this.form.ptzAngleLr += lr;
					} else if (this.iPTZIndex == 2) {
						this.form.ptzAngleTb -= tb;
					} else if (this.iPTZIndex == 8) {
						this.form.ptzAngleTb -= tb;
						this.form.ptzAngleLr -= lr;
					}
				} else {
					if (this.iPTZIndex == 5) {
						this.form.ptzAngleTb++;
						this.form.ptzAngleLr++;
					} else if (this.iPTZIndex == 1) {
						this.form.ptzAngleTb++;
					} else if (this.iPTZIndex == 7) {
						this.form.ptzAngleTb++;
						this.form.ptzAngleLr--;
					} else if (this.iPTZIndex == 3) {
						this.form.ptzAngleLr++;
					} else if (this.iPTZIndex == 4) {
						this.form.ptzAngleLr--;
					} else if (this.iPTZIndex == 6) {
						this.form.ptzAngleTb--;
						this.form.ptzAngleLr++;
					} else if (this.iPTZIndex == 2) {
						this.form.ptzAngleTb--;
					} else if (this.iPTZIndex == 8) {
						this.form.ptzAngleTb--;
						this.form.ptzAngleLr--;
					}
				}
				if (this.form.ptzAngleTb > 90) {
					this.form.ptzAngleTb = 90
				} else if (this.form.ptzAngleTb < -90) {
					this.form.ptzAngleTb = -90
				}


				var oWndInfo = WebVideoCtrl.I_GetWindowStatus(g_iWndIndex);
				if (oWndInfo != null) {
					WebVideoCtrl.I_PTZControl(1, true, {
						success: function(xmlDoc) {
							console.log(oWndInfo.szDeviceIdentify + " 停止云台成功！");
						},
						error: function(oError) {
							console.log(oWndInfo.szDeviceIdentify + " 停止云台失败！", oError.errorCode, oError
								.errorMsg);
						}
					});
				}
			},
			PTZZoomIn() {
				this.form.cameraZoom++;
				var oWndInfo = WebVideoCtrl.I_GetWindowStatus(g_iWndIndex);

				if (oWndInfo != null) {
					WebVideoCtrl.I_PTZControl(10, false, {
						iWndIndex: g_iWndIndex,
						success: function(xmlDoc) {
							console.log(oWndInfo.szDeviceIdentify + " 调焦+成功！");
						},
						error: function(oError) {
							console.log(oWndInfo.szDeviceIdentify + "  调焦+失败！", oError.errorCode, oError
								.errorMsg);
						}
					});
				}
			},
			PTZZoomout() {
				this.form.cameraZoom--;
				var oWndInfo = WebVideoCtrl.I_GetWindowStatus(g_iWndIndex);

				if (oWndInfo != null) {
					WebVideoCtrl.I_PTZControl(11, false, {
						iWndIndex: g_iWndIndex,
						success: function(xmlDoc) {
							console.log(oWndInfo.szDeviceIdentify + " 调焦-成功！");
						},
						error: function(oError) {
							console.log(oWndInfo.szDeviceIdentify + "  调焦-失败！", oError.errorCode, oError
								.errorMsg);
						}
					});
				}
			},
			PTZZoomStop() {
				var oWndInfo = WebVideoCtrl.I_GetWindowStatus(g_iWndIndex);

				if (oWndInfo != null) {
					WebVideoCtrl.I_PTZControl(11, true, {
						iWndIndex: g_iWndIndex,
						success: function(xmlDoc) {
							console.log(oWndInfo.szDeviceIdentify + " 调焦停止成功！");
						},
						error: function(oError) {
							console.log(oWndInfo.szDeviceIdentify + "  调焦停止失败！", oError.errorCode, oError
								.errorMsg);
						}
					});
				}
			},
			PTZFocusIn() {
				this.form.cameraFocus++;
				var oWndInfo = WebVideoCtrl.I_GetWindowStatus(g_iWndIndex);

				if (oWndInfo != null) {
					WebVideoCtrl.I_PTZControl(12, false, {
						iWndIndex: g_iWndIndex,
						success: function(xmlDoc) {
							console.log(oWndInfo.szDeviceIdentify + " 聚焦+成功！");
						},
						error: function(oError) {
							console.log(oWndInfo.szDeviceIdentify + "  聚焦+失败！", oError.errorCode, oError
								.errorMsg);
						}
					});
				}
			},
			PTZFoucusOut() {
				this.form.cameraFocus--;
				var oWndInfo = WebVideoCtrl.I_GetWindowStatus(g_iWndIndex);

				if (oWndInfo != null) {
					WebVideoCtrl.I_PTZControl(13, false, {
						iWndIndex: g_iWndIndex,
						success: function(xmlDoc) {
							console.log(oWndInfo.szDeviceIdentify + " 聚焦-成功！");
						},
						error: function(oError) {
							console.log(oWndInfo.szDeviceIdentify + "  聚焦-失败！", oError.errorCode, oError
								.errorMsg);
						}
					});
				}
			},
			PTZFoucusStop() {
				var oWndInfo = WebVideoCtrl.I_GetWindowStatus(g_iWndIndex);

				if (oWndInfo != null) {
					WebVideoCtrl.I_PTZControl(12, true, {
						iWndIndex: g_iWndIndex,
						success: function(xmlDoc) {
							console.log(oWndInfo.szDeviceIdentify + " 聚焦停止成功！");
						},
						error: function(oError) {
							console.log(oWndInfo.szDeviceIdentify + "  聚焦停止失败！", oError.errorCode, oError
								.errorMsg);
						}
					});
				}
			},
			PTZIrisIn() {
				var oWndInfo = WebVideoCtrl.I_GetWindowStatus(g_iWndIndex);

				if (oWndInfo != null) {
					WebVideoCtrl.I_PTZControl(14, false, {
						iWndIndex: g_iWndIndex,
						success: function(xmlDoc) {
							console.log(oWndInfo.szDeviceIdentify + " 光圈+成功！");
						},
						error: function(oError) {
							console.log(oWndInfo.szDeviceIdentify + "  光圈+失败！", oError.errorCode, oError
								.errorMsg);
						}
					});
				}
			},
			PTZIrisOut() {
				var oWndInfo = WebVideoCtrl.I_GetWindowStatus(g_iWndIndex);

				if (oWndInfo != null) {
					WebVideoCtrl.I_PTZControl(15, false, {
						iWndIndex: g_iWndIndex,
						success: function(xmlDoc) {
							console.log(oWndInfo.szDeviceIdentify + " 光圈-成功！");
						},
						error: function(oError) {
							console.log(oWndInfo.szDeviceIdentify + "  光圈-失败！", oError.errorCode, oError
								.errorMsg);
						}
					});
				}
			},
			PTZIrisStop() {
				var oWndInfo = WebVideoCtrl.I_GetWindowStatus(g_iWndIndex);

				if (oWndInfo != null) {
					WebVideoCtrl.I_PTZControl(14, true, {
						iWndIndex: g_iWndIndex,
						success: function(xmlDoc) {
							console.log(oWndInfo.szDeviceIdentify + " 光圈停止成功！");
						},
						error: function(oError) {
							console.log(oWndInfo.szDeviceIdentify + "  光圈停止失败！", oError.errorCode, oError
								.errorMsg);
						}
					});
				}
			},
			cameraReset() {
				this.clickGoPreset()
				this.form.ptzAngleTb = 0
				this.form.ptzAngleLr = 0
				this.form.cameraFocus = 0
				this.form.cameraZoom = 0
			}
		}
	}
</script>

<style scoped>
	.app-container {
		padding: 10px 20px 10px 20px;
	}

	.app-container .el-tag {
		margin: 20px 0 10px 0;
	}

	.cloud_terrace .direction img {
		width: 70px;
		height: 70px;
	}

	.cloud_terrace .function img {
		width: 40px;
		height: 40px;
		margin-top: 23px;
	}

	.custom-dropdown .el-select-dropdown__item {
		height: 50px;
		line-height: 50px;
	}
</style>