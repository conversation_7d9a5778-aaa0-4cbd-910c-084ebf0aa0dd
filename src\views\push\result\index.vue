<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
         <el-form-item label="接收者" prop="receiver">
            <el-input
               v-model="queryParams.receiver"
               placeholder="请输入接收者"
               clearable
               style="width: 240px"
               @keyup.enter="handleQuery"
            />
         </el-form-item>
         <el-form-item label="发送内容" prop="content">
            <el-input
               v-model="queryParams.content"
               placeholder="请输入发送内容"
               clearable
               style="width: 240px"
               @keyup.enter="handleQuery"
            />
         </el-form-item>
         <el-form-item label="推送渠道" prop="pushChannel">
            <el-select v-model="queryParams.pushChannel" placeholder="请选择推送渠道" clearable style="width: 240px">
               <el-option
                  v-for="dict in push_channel"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
               />
            </el-select>
         </el-form-item>
         <el-form-item label="发送时间" style="width: 438px;">
            <el-date-picker
               v-model="dateRange"
               value-format="YYYY-MM-DD HH:mm:ss"
               type="datetimerange"
               range-separator="至"
               start-placeholder="开始日期"
               end-placeholder="结束日期"
            ></el-date-picker>
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
         </el-form-item>
      </el-form>


      <el-table v-loading="loading" :data="dataList">
         <el-table-column label="发送者" align="center" prop="sender" />
         <el-table-column label="接收者" align="center" prop="receiver" />
         <el-table-column label="推送类型" align="center" prop="pushChannel">
            <template #default="scope">
               <dict-tag :options="push_channel" :value="scope.row.pushChannel" />
            </template>
         </el-table-column>
         <el-table-column label="发送内容" align="center" prop="content" :show-overflow-tooltip="true" />
         <el-table-column label="是否成功" align="center" prop="status">
            <template #default="scope">
               <dict-tag :options="sys_common_status" :value="scope.row.status" />
            </template>
         </el-table-column>
         <el-table-column label="发送时间" align="sendTime" prop="createTime" width="180">
            <template #default="scope">
               <span>{{ parseTime(scope.row.createTime) }}</span>
            </template>
         </el-table-column>

      </el-table>

      <pagination
         v-show="total > 0"
         :total="total"
         v-model:page="queryParams.pageNum"
         v-model:limit="queryParams.pageSize"
         @pagination="getList"
      />


   </div>
</template>

<script setup name="PushResult">


import { listPageList } from "@/api/push/result"


const { proxy } = getCurrentInstance()
const { push_channel, sys_common_status} = proxy.useDict("push_channel", "sys_common_status")

const dataList = ref([])
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const dateRange = ref([])



const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    receiver: undefined,
    content: undefined,
    pushChannel: undefined
  }
})

const { queryParams} = toRefs(data)




/** 查询陪同报警列表 */
function getList() {
  loading.value = true
  if(dateRange.value.length > 0){
    queryParams.value.beginTime = dateRange.value[0]
    queryParams.value.endTime = dateRange.value[1]
  }
  listPageList(queryParams.value).then(response => {
    dataList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}


/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = []
   queryParams.value.beginTime = null;
  queryParams.value.endTime = null;
  proxy.resetForm("queryRef")
  handleQuery()
}




getList()
</script>
