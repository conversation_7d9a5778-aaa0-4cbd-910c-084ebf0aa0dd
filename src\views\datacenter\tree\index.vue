<template>
  <el-input
      v-model="filterText"
      class="w-60 mb-2"
      placeholder="查询"
  />

  <el-tree
      ref="treeRef"
      style="max-width: 600px"
      class="filter-tree"
      :data="data"
      :props="defaultProps"
      default-expand-all
      :filter-node-method="filterNode"
      @node-click="nodeCLick"
      :expand-on-click-node="false"
      :highlight-current="true"
      :current-node-key="treeId"
      node-key="id"
  />
</template>

<script setup>
import {listBizDeviceTree} from "@/api/operation/bizDeviceTree"
import {dataCenterTreeStore} from '@/store/modules/DataCenterTree'
const globalStore = dataCenterTreeStore()
const filterText = ref('')
const treeRef = ref(null)
const data = reactive([])
const {proxy} = getCurrentInstance()
const defaultProps = {children: 'children', label: 'deviceName',key: 'id'}
const treeId = ref('')

watch(filterText, (val) => {
  treeRef.value.filter(val)
})



function filterNode(value, data) {
  if (!value) return true
  return data.deviceName.includes(value)
}

function nodeCLick(node) {
  globalStore.setTreeId(node.id, node);
}

listBizDeviceTree({}).then(response => {
  treeId.value = globalStore.treeId;
  data.length = 0;
  data.splice(0, data.length, ...proxy.handleTree(response.data, "id"));
})

//监听treeId变化
// watch(() => globalStore.treeId, (newValue) => {
//   console.log(newValue)
// })

</script>