<template>
	<div class="home-container">
		<div class="home_font">
			<article>
				<h1>自动化机器人管理系统</h1>
			</article>
		</div>
		<section>
			<div><img src="@/assets/images/home/<USER>" alt="" /></div>
			<div><img src="@/assets/images/home/<USER>" alt="" /></div>
			<div><img src="@/assets/images/home/<USER>" alt="" /></div>
			<div><img src="@/assets/images/home/<USER>" alt="" /></div>
			<div><img src="@/assets/images/home/<USER>" alt="" /></div>
			<div><img src="@/assets/images/home/<USER>" alt="" /></div>
		</section>
	</div>
</template>

<style scoped>
	.home-container {
		position: relative;
		padding: 0;
		margin: 0;
		display: flex;
		justify-content: center;
		align-items: center;
		height: calc(100vh - 50px);
		perspective: 900px;
		background: url(@/assets/images/home/<USER>
	}

	img {
		width: 300px;
		height: 200px;
	}

	section {
		position: relative;
		width: 300px;
		height: 200px;
		cursor: pointer;
		transform-style: preserve-3d;
		animation: rotate 20s linear infinite;
	}

	section:hover {
		animation-play-state: paused;
	}

	section div {
		position: absolute;
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;
		text-align: center;
		-webkit-box-reflect: below 15px -webkit-linear-gradient(transparent 10%, rgba(255, 255, 255, 0.3));
	}

	section div:nth-child(1) {
		transform: translateZ(300px);

	}

	section div:nth-child(2) {
		transform: rotateY(60deg) translateZ(300px);
		/* background-color: #6e9c72; */
	}

	section div:nth-child(3) {
		transform: rotateY(120deg) translateZ(300px);
		/* background-color: #5e5f7a; */
	}

	section div:nth-child(4) {
		transform: rotateY(180deg) translateZ(300px);
		/* background-color: #f5eb98; */
	}

	section div:nth-child(5) {
		transform: rotateY(240deg) translateZ(300px);
		/* background-color: #50a3bc; */
	}

	section div:nth-child(6) {
		transform: rotateY(300deg) translateZ(300px);
		/* background-color: #f9a99a; */
	}

	/* 先定义旋转动画 */
	@keyframes rotate {
		0% {
			transform: rotateY(0);
		}

		100% {
			transform: rotateY(360deg);
		}
	}

	.home_font {
		position: absolute;
		top: 220px;
	}

	@property --＠color-1 {
		syntax: "<color>";
		inherits: false;
		initial-value: hsl(98 100% 62%)
	}

	@property --＠color-2 {
		syntax: "<color>";
		inherits: false;
		initial-value: hsl(204 100% 59%)
	}

	@-webkit-keyframes gradient-change {
		to {
			--＠color-1: hsl(210 100% 59%);
			--＠color-2: hsl(310 100% 59%)
		}
	}

	@keyframes gradient-change {
		to {
			--＠color-1: hsl(210 100% 59%);
			--＠color-2: hsl(310 100% 59%)
		}
	}

	article {
		-webkit-animation: gradient-change 2s linear infinite alternate;
		animation: gradient-change 2s linear infinite alternate;
		background: linear-gradient(to right in oklch, var(--＠color-1), var(--＠color-2));
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
		background-clip: text;
		color: transparent
	}

	@layer demo.support {
		h1 {
			font-size: 10vmin;
			line-height: 1.1
		}

		body {
			background: hsl(204 100% 5%);
			min-block-size: 100%;
			box-sizing: border-box;
			display: grid;
			place-content: center;
			font-family: system-ui, sans-serif;
			font-size: min(200%, 4vmin);
			padding: 5vmin
		}

		h1,
		p,
		body {
			margin: 0;
			text-wrap: balance
		}

		h1 {
			line-height: 1.25cap;
			font-size: 25px;
		}

		p {
			font-family: "Dank Mono", ui-monospace, monospace
		}

		html {
			block-size: 100%
		}

		article {
			display: grid;
			gap: 1lh;
			text-align: center
		}
	}
</style>