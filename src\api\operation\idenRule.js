import request from '@/utils/request'

// 查询点位模型
export function getIdenModelList(query) {
    return request({
        url: '/iden/rule/idenModelList',
        method: 'get',
        params: query
    })
}
// 查询在线用户列表
export function list(query) {
    return request({
        url: '/iden/rule/list',
        method: 'get',
        params: query
    })
}

export function getRuleAll(query) {
    return request({
        url: '/iden/rule/getAll',
        method: 'get',
        params: query
    })
}

// 添加模型
export function addIdenRule(data) {
    return request({
        url: '/iden/rule/addIdenRule',
        method: 'post',
        data: data
    })
}

// 更新模型
export function updateIdenRule(data) {
    return request({
        url: '/iden/rule/updateIdenRule',
        method: 'post',
        data: data
    })
}
// 删除菜单
export function remove(id) {
    return request({
        url: '/iden/rule/remove/' + id,
        method: 'delete'
    })
}

export function removeAll(data) {
    return request({
        url: '/iden/rule/removeAll',
        method: 'delete',
        data: data
    })
}

export function getByModelId(modelId) {
    return request({
        url: '/iden/rule/getByModelId/' + modelId,
        method: 'get'
    })
}

